{"c": ["app/layout", "app/[locale]/contact/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js", "(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/suspense.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js", "(app-pages-browser)/./node_modules/zod/lib/index.mjs", "(app-pages-browser)/./src/hooks/useApi.ts", "(app-pages-browser)/./src/lib/validation.ts"]}