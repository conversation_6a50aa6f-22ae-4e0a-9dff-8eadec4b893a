'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowRight, Factory, Settings, Gauge, PenTool as Tool, Shield } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { LazyImage } from '../../components/ui/LazyImage';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { productionLines } from '../../data/productionLines';
import { useThemeStore } from '../../stores/themeStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { cn } from '../../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';

const categoriesData = {
  en: [
    { id: 'all', name: 'All Lines', icon: Factory },
    { id: 'Manufacturing', name: 'Manufacturing', icon: Settings },
    { id: 'Food & Beverage', name: 'Food & Beverage', icon: Gauge },
    { id: 'Packaging', name: 'Packaging', icon: Tool },
    { id: 'Pharmaceutical', name: 'Pharmaceutical', icon: Shield },
  ],
  ar: [
    { id: 'all', name: 'جميع الخطوط', icon: Factory },
    { id: 'Manufacturing', name: 'التصنيع', icon: Settings },
    { id: 'Food & Beverage', name: 'الأغذية والمشروبات', icon: Gauge },
    { id: 'Packaging', name: 'التعبئة والتغليف', icon: Tool },
    { id: 'Pharmaceutical', name: 'الصناعات الدوائية', icon: Shield },
  ]
};

export default function ProductionLinesPage() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  // استخدام الفئات المناسبة حسب اللغة
  const categories = categoriesData[currentLanguage];

  const filteredLines = selectedCategory === 'all'
    ? productionLines
    : productionLines.filter(line => line.category === selectedCategory);

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-900 to-slate-800 text-white py-20">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.1}>
            <div className="max-w-3xl mx-auto text-center">
              <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-primary-500/20 text-primary-300 mb-6">
                <Factory size={36} />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'خطوط الإنتاج الصناعية' : 'Industrial Production Lines'}
              </h1>
              <p className="text-xl mb-8 text-slate-300 max-w-2xl mx-auto">
                {currentLanguage === 'ar'
                  ? 'حلول إنتاج متطورة مصممة للكفاءة والموثوقية وقابلية التوسع.'
                  : 'State-of-the-art production solutions engineered for efficiency, reliability, and scalability.'}
              </p>
              <HoverAnimation animation="scale">
                <Button
                  size="lg"
                  variant="accent"
                  className="px-8"
                  onClick={() => router.push(`/${currentLanguage}/contact`)}
                >
                  {currentLanguage === 'ar' ? 'طلب استشارة' : 'Request Consultation'}
                  <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5`} />
                </Button>
              </HoverAnimation>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Categories */}
      <section className={cn("py-12", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="flex flex-wrap gap-4 justify-center">
              <ScrollStagger
                animation="slide"
                direction="up"
                staggerDelay={0.05}
                className="flex flex-wrap gap-4 justify-center"
              >
                {categories.map(category => {
                  const Icon = category.icon;
                  return (
                    <HoverAnimation key={category.id} animation="scale">
                      <Button
                        variant={selectedCategory === category.id ? 'primary' : 'outline'}
                        onClick={() => setSelectedCategory(category.id)}
                        className="flex items-center gap-2"
                      >
                        <Icon size={20} />
                        {category.name}
                      </Button>
                    </HoverAnimation>
                  );
                })}
              </ScrollStagger>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Production Lines Grid */}
      <section className={cn("py-12", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            delay={0.3}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {filteredLines.map(line => (
              <HoverAnimation key={line.id} animation="lift">
                <Card className="flex flex-col overflow-hidden group h-full">
                  <div className="relative">
                    <EnhancedImage
                      src={line.images[0]}
                      alt={line.name}
                      fill={true}
                      objectFit="cover"
                      effect="zoom"
                      progressive={true}
                      placeholder="shimmer"
                      className="w-full h-48"
                      containerClassName="w-full h-48"
                      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    />
                    <span className={cn(
                      "absolute top-4 left-4 px-3 py-1 rounded-full text-sm font-medium z-10",
                      isDarkMode ? "bg-primary-600 text-white" : "bg-primary-500 text-white"
                    )}>
                      {line.category}
                    </span>
                  </div>
                  <div className="p-6 flex-grow">
                    <h3 className="text-xl font-semibold mb-2 text-slate-900 dark:text-white">
                      {currentLanguage === 'ar' ? line.name_ar || line.name : line.name}
                    </h3>
                    <p className="text-slate-600 dark:text-slate-300 mb-4">
                      {currentLanguage === 'ar' ? line.description_ar || line.description : line.description}
                    </p>
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                        <Gauge className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4 text-primary-500`} />
                        {currentLanguage === 'ar' ? `السعة: ${line.capacity}` : `Capacity: ${line.capacity}`}
                      </div>
                      <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                        <Settings className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-4 w-4 text-primary-500`} />
                        {currentLanguage === 'ar'
                          ? `${Object.keys(line.specifications).length} مواصفات فنية`
                          : `${Object.keys(line.specifications).length} Technical Specifications`}
                      </div>
                    </div>
                  </div>
                  <div className="p-6 pt-0">
                    <HoverAnimation animation="scale">
                      <Link href={`/${currentLanguage}/production-lines/${line.slug}`}>
                        <Button className="w-full">
                          {currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                          <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-4 w-4`} />
                        </Button>
                      </Link>
                    </HoverAnimation>
                  </div>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Features Section */}
      <section className={cn("py-16", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'لماذا تختار خطوط إنتاجنا؟' : 'Why Choose Our Production Lines?'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'حلول رائدة في الصناعة مدعومة بالابتكار والخبرة'
                  : 'Industry-leading solutions backed by innovation and expertise'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            delay={0.4}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {[
              {
                icon: <Settings className="h-10 w-10 text-primary-500 dark:text-primary-400" />,
                title: currentLanguage === 'ar' ? 'أتمتة متقدمة' : 'Advanced Automation',
                description: currentLanguage === 'ar'
                  ? 'أنظمة روبوتية وتحكم متطورة لتحقيق أقصى قدر من الكفاءة'
                  : 'State-of-the-art robotics and control systems for maximum efficiency',
              },
              {
                icon: <Shield className="h-10 w-10 text-primary-500 dark:text-primary-400" />,
                title: currentLanguage === 'ar' ? 'ضمان الجودة' : 'Quality Assurance',
                description: currentLanguage === 'ar'
                  ? 'أنظمة متكاملة لمراقبة الجودة مع مراقبة في الوقت الفعلي'
                  : 'Integrated quality control systems with real-time monitoring',
              },
              {
                icon: <Tool className="h-10 w-10 text-primary-500 dark:text-primary-400" />,
                title: currentLanguage === 'ar' ? 'خيارات التخصيص' : 'Customization Options',
                description: currentLanguage === 'ar'
                  ? 'تكوينات مرنة لتلبية احتياجات الإنتاج الخاصة بك'
                  : 'Flexible configurations to meet your specific production needs',
              },
            ].map((feature, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-6 text-center h-full">
                  <div className={cn(
                    "w-20 h-20 mx-auto rounded-full flex items-center justify-center mb-4",
                    isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
                  )}>
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-slate-900 dark:text-white">{feature.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{feature.description}</p>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-500 text-white">
        <div className="container-custom text-center">
          <ScrollAnimation animation="fade" delay={0.5}>
            <h2 className="text-3xl font-bold mb-4">
              {currentLanguage === 'ar' ? 'هل أنت مستعد لترقية إنتاجك؟' : 'Ready to Upgrade Your Production?'}
            </h2>
            <p className="text-xl mb-8 text-primary-50 max-w-2xl mx-auto">
              {currentLanguage === 'ar'
                ? 'تواصل مع خبرائنا لمناقشة متطلبات خط الإنتاج الخاص بك'
                : 'Contact our experts to discuss your production line requirements'}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <HoverAnimation animation="scale">
                <Button
                  variant="accent"
                  size="lg"
                  onClick={() => router.push(`/${currentLanguage}/contact`)}
                >
                  {currentLanguage === 'ar' ? 'ابدأ الآن' : 'Get Started'}
                </Button>
              </HoverAnimation>
              <HoverAnimation animation="scale">
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white text-white hover:bg-primary-600"
                  onClick={() => router.push(`/${currentLanguage}/services`)}
                >
                  {currentLanguage === 'ar' ? 'عرض الخدمات' : 'View Services'}
                </Button>
              </HoverAnimation>
            </div>
          </ScrollAnimation>
        </div>
      </section>
    </div>
  );
}