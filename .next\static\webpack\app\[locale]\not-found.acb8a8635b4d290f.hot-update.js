"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/not-found",{

/***/ "(app-pages-browser)/./src/lib/sqlite.ts":
/*!***************************!*\
  !*** ./src/lib/sqlite.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sqlite: () => (/* binding */ sqlite)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(app-pages-browser)/./node_modules/bcryptjs/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * مكتبة SQLite للتعامل مع قاعدة البيانات المحلية\n * تستبدل هذه المكتبة استخدام Supabase في المشروع\n */ \n // Added for password hashing\nconst IS_SERVER = \"object\" === 'undefined';\nconst SALT_ROUNDS = 10; // For bcrypt hashing\nlet db = null;\nif (IS_SERVER) {\n    try {\n        const BetterSqlite3 = __webpack_require__(/*! better-sqlite3 */ \"(app-pages-browser)/./node_modules/better-sqlite3/lib/index.js\");\n        const path = __webpack_require__(/*! path */ \"(app-pages-browser)/./node_modules/next/dist/compiled/path-browserify/index.js\");\n        const fs = __webpack_require__(/*! fs */ \"?da0f\");\n        const dbDir = path.join(process.cwd(), 'server', 'db');\n        const dbPath = path.join(dbDir, 'ecommerce.sqlite');\n        if (!fs.existsSync(dbDir)) {\n            fs.mkdirSync(dbDir, {\n                recursive: true\n            });\n            console.log(\"[DB_SERVER] Created database directory: \".concat(dbDir));\n        }\n        console.log(\"[DB_SERVER] Attempting to connect to SQLite database at: \".concat(dbPath));\n        db = new BetterSqlite3(dbPath, {}); // Verbose logging can be noisy\n        console.log('[DB_SERVER] Successfully connected to SQLite database.');\n        // Ensure tables are created (idempotent)\n        db.exec(\"\\n      CREATE TABLE IF NOT EXISTS users (\\n        id TEXT PRIMARY KEY,\\n        email TEXT UNIQUE NOT NULL,\\n        password_hash TEXT NOT NULL,\\n        first_name TEXT,\\n        last_name TEXT,\\n        role TEXT DEFAULT 'user' NOT NULL,\\n        created_at TEXT NOT NULL,\\n        updated_at TEXT,\\n        avatar_url TEXT,\\n        phone_number TEXT,\\n        addresses TEXT, -- Store as JSON string\\n        email_verified INTEGER DEFAULT 0, -- 0 for false, 1 for true\\n        last_login TEXT\\n      );\\n    \");\n        console.log('[DB_SERVER] Users table checked/created.');\n        db.exec(\"\\n      CREATE TABLE IF NOT EXISTS products (\\n        id TEXT PRIMARY KEY,\\n        name TEXT NOT NULL,\\n        slug TEXT UNIQUE NOT NULL,\\n        description TEXT,\\n        price REAL NOT NULL,\\n        compare_at_price REAL,\\n        images TEXT,\\n        category TEXT,\\n        tags TEXT,\\n        stock INTEGER DEFAULT 0,\\n        featured INTEGER DEFAULT 0,\\n        specifications TEXT,\\n        created_at TEXT NOT NULL,\\n        updated_at TEXT,\\n        rating REAL,\\n        review_count INTEGER,\\n        related_products TEXT\\n      );\\n    \");\n        console.log('[DB_SERVER] Products table checked/created.');\n        db.exec(\"\\n      CREATE TABLE IF NOT EXISTS service_bookings (\\n        id TEXT PRIMARY KEY,\\n        service_name TEXT NOT NULL,\\n        customer_name TEXT NOT NULL,\\n        customer_email TEXT NOT NULL,\\n        customer_phone TEXT NOT NULL,\\n        company_name TEXT,\\n        service_date TEXT NOT NULL,\\n        preferred_time TEXT,\\n        urgency TEXT DEFAULT 'normal',\\n        message TEXT,\\n        status TEXT DEFAULT 'pending',\\n        notes TEXT,\\n        created_at TEXT NOT NULL,\\n        updated_at TEXT\\n      );\\n    \");\n        console.log('[DB_SERVER] Service bookings table checked/created.');\n        // Add other table creations here as needed (services, blog_posts, etc.)\n        console.log('[DB_SERVER] Database schema checked/initialized.');\n    } catch (error) {\n        console.error('[DB_SERVER] Critical error during database initialization:', error);\n        db = null;\n    }\n} else {\n    console.log('[MockDB_Client] Running in client mode, SQLite DB not initialized.');\n}\n// --- Server-side Helper Functions ---\nasync function _server_hashPassword(password) {\n    if (!IS_SERVER) throw new Error('_server_hashPassword can only be called on the server.');\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, SALT_ROUNDS);\n}\nasync function _server_comparePassword(password, hash) {\n    if (!IS_SERVER) throw new Error('_server_comparePassword can only be called on the server.');\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hash);\n}\nfunction _server_getUserByEmailWithPasswordHash(email) {\n    if (!IS_SERVER || !db) return null;\n    try {\n        const stmt = db.prepare('SELECT * FROM users WHERE email = ?');\n        const row = stmt.get(email.toLowerCase());\n        return row ? mapUserFromDbRow(row) : null;\n    } catch (error) {\n        console.error('[DB_SERVER] Error in _server_getUserByEmailWithPasswordHash:', error);\n        return null;\n    }\n}\nfunction _server_getUserById(id) {\n    if (!IS_SERVER || !db) return null;\n    try {\n        const stmt = db.prepare('SELECT * FROM users WHERE id = ?');\n        const row = stmt.get(id);\n        return row ? mapUserFromDbRow(row) : null;\n    } catch (error) {\n        console.error('[DB_SERVER] Error in _server_getUserById:', error);\n        return null;\n    }\n}\nfunction _server_createUser(userData) {\n    if (!IS_SERVER || !db) return null;\n    try {\n        const now = new Date().toISOString();\n        // Prepare addresses as JSON string if provided\n        const addressesJson = userData.addresses && userData.addresses.length > 0 ? JSON.stringify(userData.addresses) : null;\n        // Generate a new ID if not provided\n        const userId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const stmt = db.prepare(\"\\n      INSERT INTO users (\\n        id, email, password_hash, first_name, last_name, role,\\n        created_at, updated_at, avatar_url, phone_number,\\n        addresses, email_verified, last_login\\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\\n    \");\n        stmt.run(userId, userData.email.toLowerCase(), userData.password_hash, userData.firstName || '', userData.lastName || '', userData.role || 'user', now, now, userData.avatarUrl || null, userData.phoneNumber || null, addressesJson, userData.emailVerified ? 1 : 0, userData.lastLogin || null);\n        return _server_getUserById(userId);\n    } catch (error) {\n        console.error('[DB_SERVER] Error in _server_createUser:', error);\n        return null;\n    }\n}\nfunction _server_getProducts() {\n    if (!IS_SERVER || !db) return [];\n    try {\n        const stmt = db.prepare('SELECT * FROM products ORDER BY created_at DESC');\n        const rows = stmt.all();\n        return rows.map(mapProductFromDbRow);\n    } catch (error) {\n        console.error('[DB_SERVER] Error in _server_getProducts:', error);\n        return [];\n    }\n}\nfunction _server_createProduct(productData) {\n    if (!IS_SERVER || !db) return null;\n    const newId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    const now = new Date().toISOString();\n    try {\n        const stmt = db.prepare(\"\\n      INSERT INTO products (id, name, slug, description, price, compare_at_price, images, category, tags, stock, featured, specifications, created_at)\\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\\n    \");\n        stmt.run(newId, productData.name, productData.slug, productData.description, productData.price, productData.compareAtPrice, JSON.stringify(productData.images || []), productData.category, JSON.stringify(productData.tags || []), productData.stock, productData.featured ? 1 : 0, JSON.stringify(productData.specifications || {}), now);\n        // To get the full product, we'd ideally query it back, but for now let's construct it\n        return {\n            ...productData,\n            id: newId,\n            createdAt: now,\n            reviews: [],\n            rating: 0,\n            reviewCount: 0\n        }; // Cast as Product, assuming defaults for reviews/rating\n    } catch (error) {\n        console.error('[DB_SERVER] Error in _server_createProduct:', error);\n        return null;\n    }\n}\n// --- Helper function to safely parse JSON (used for localStorage) ---\nfunction safeJsonParse(jsonString, defaultValue) {\n    if (jsonString == null) return defaultValue;\n    try {\n        return JSON.parse(jsonString);\n    } catch (e) {\n        // console.error('Failed to parse JSON string:', e, '\\nString was:', jsonString);\n        return defaultValue;\n    }\n}\n// --- localStorage keys ---\nconst USER_STORAGE_KEY = 'sqlite_users';\nconst PRODUCT_STORAGE_KEY = 'sqlite_products';\n// ... other keys\n// --- Default data for client-side localStorage initialization ---\nconst DEFAULT_LOCAL_USERS = [\n    {\n        email: '<EMAIL>',\n        firstName: 'Admin',\n        lastName: 'Local',\n        role: 'admin'\n    },\n    {\n        email: '<EMAIL>',\n        firstName: 'Test',\n        lastName: 'Local',\n        role: 'user'\n    }\n];\n// ... DEFAULT_LOCAL_PRODUCTS (ensure it matches Product type, omitting server-generated fields)\nconst DEFAULT_LOCAL_PRODUCTS = [\n    {\n        name: 'Default Local Product 1',\n        slug: 'default-local-product-1',\n        description: 'This is a default product for localStorage.',\n        price: 19.99,\n        compareAtPrice: 29.99,\n        images: [],\n        category: 'Local Category',\n        tags: [\n            'default',\n            'localstorage'\n        ],\n        stock: 100,\n        featured: true,\n        specifications: {\n            material: 'local_plastic'\n        }\n    }\n];\n// --- Helper function to map database row to User type (includes password_hash for internal use) ---\nfunction mapUserFromDbRow(row) {\n    if (!row) return row;\n    // Parse addresses from JSON string if present\n    let addresses = [];\n    try {\n        if (row.addresses) {\n            addresses = JSON.parse(row.addresses);\n        }\n    } catch (e) {\n        console.error('[DB] Error parsing user addresses:', e);\n    }\n    // Map all fields from the database row to the User type\n    const user = {\n        id: row.id,\n        email: row.email,\n        firstName: row.first_name || '',\n        lastName: row.last_name || '',\n        role: row.role || 'user',\n        createdAt: row.created_at,\n        updatedAt: row.updated_at || null,\n        avatarUrl: row.avatar_url || null,\n        phoneNumber: row.phone_number || null,\n        addresses: addresses,\n        emailVerified: Boolean(row.email_verified),\n        lastLogin: row.last_login || null\n    };\n    // Include password_hash for internal authentication\n    if (row.password_hash) {\n        user.password_hash = row.password_hash;\n    }\n    return user;\n}\n// --- Helper function to map database row to Product type ---\nfunction mapProductFromDbRow(row) {\n    if (!row) return null;\n    return {\n        id: row.id,\n        name: row.name,\n        slug: row.slug,\n        description: row.description,\n        price: row.price ? parseFloat(row.price) : 0,\n        compareAtPrice: row.compare_at_price ? parseFloat(row.compare_at_price) : undefined,\n        images: row.images ? safeJsonParse(row.images, []) : [],\n        category: row.category,\n        tags: row.tags ? safeJsonParse(row.tags, []) : [],\n        stock: row.stock ? parseInt(row.stock, 10) : 0,\n        featured: Boolean(row.featured),\n        specifications: row.specifications ? safeJsonParse(row.specifications, {}) : {},\n        createdAt: row.created_at,\n        updatedAt: row.updated_at,\n        reviews: [],\n        rating: row.rating ? parseFloat(row.rating) : undefined,\n        reviewCount: row.review_count ? parseInt(row.review_count, 10) : undefined,\n        relatedProducts: row.related_products ? safeJsonParse(row.related_products, []) : []\n    };\n}\nclass MockSQLiteDatabase {\n    loadFromLocalStorage(key, defaultValue) {\n        if (IS_SERVER) return defaultValue; // Should not happen with current constructor logic\n        try {\n            const data = localStorage.getItem(key);\n            const parsedData = data ? safeJsonParse(data, defaultValue) : defaultValue;\n            // تأكد من أن البيانات المحملة هي مصفوفة\n            if (!Array.isArray(parsedData)) {\n                console.warn(\"[MockDB_Client] Data loaded from \".concat(key, \" is not an array, using default value\"));\n                return defaultValue;\n            }\n            return parsedData;\n        } catch (error) {\n            console.error(\"[MockDB_Client] Error loading data from localStorage key \".concat(key, \":\"), error);\n            return defaultValue;\n        }\n    }\n    saveToLocalStorage(key, data) {\n        if (IS_SERVER) return;\n        localStorage.setItem(key, JSON.stringify(data));\n    }\n    _initializeDefaultLocalData() {\n        if (IS_SERVER) return;\n        if (this.users.length === 0) {\n            this.users = DEFAULT_LOCAL_USERS.map((u)=>({\n                    ...u,\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n                    createdAt: new Date().toISOString()\n                }));\n            this.saveToLocalStorage(this.userKey, this.users);\n        }\n        if (this.products.length === 0) {\n            this.products = DEFAULT_LOCAL_PRODUCTS.map((p)=>({\n                    ...p,\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n                    createdAt: new Date().toISOString(),\n                    reviews: [],\n                    rating: 0,\n                    reviewCount: 0,\n                    relatedProducts: []\n                }));\n            this.saveToLocalStorage(this.productKey, this.products);\n        }\n    }\n    // --- User Methods ---\n    async getUsers() {\n        if (IS_SERVER) {\n            if (!this.db_conn) return [];\n            try {\n                const stmt = this.db_conn.prepare('SELECT * FROM users');\n                const rows = stmt.all();\n                // Exclude password_hash from results sent to client/general use\n                return rows.map(mapUserFromDbRow).map((userWithHash)=>{\n                    const { password_hash, ...userWithoutHash } = userWithHash;\n                    return userWithoutHash; // Ensure the final object is also User\n                });\n            } catch (error) {\n                console.error('[DB_SERVER] Error in getUsers:', error);\n                return [];\n            }\n        }\n        // تأكد من أن this.users هو مصفوفة\n        if (!Array.isArray(this.users)) {\n            console.warn('[MockDB_Client] this.users is not an array, initializing empty array');\n            this.users = [];\n            this.saveToLocalStorage(this.userKey, this.users);\n        }\n        return [\n            ...this.users\n        ];\n    }\n    async getUserByEmail(email) {\n        if (IS_SERVER) {\n            const userWithHash = _server_getUserByEmailWithPasswordHash(email.toLowerCase());\n            if (userWithHash) {\n                const { password_hash, ...userWithoutHash } = userWithHash;\n                return userWithoutHash;\n            }\n            return null;\n        }\n        // تأكد من أن this.users هو مصفوفة\n        if (!Array.isArray(this.users)) {\n            console.warn('[MockDB_Client] this.users is not an array in getUserByEmail, initializing empty array');\n            this.users = [];\n            this.saveToLocalStorage(this.userKey, this.users);\n            return null;\n        }\n        const user = this.users.find((u)=>u.email.toLowerCase() === email.toLowerCase());\n        return user ? {\n            ...user\n        } : null; // Return a copy\n    }\n    async getUserById(id) {\n        if (IS_SERVER) {\n            const userWithHash = _server_getUserById(id);\n            if (userWithHash) {\n                const { password_hash, ...userWithoutHash } = userWithHash;\n                return userWithoutHash;\n            }\n            return null;\n        }\n        // تأكد من أن this.users هو مصفوفة\n        if (!Array.isArray(this.users)) {\n            console.warn('[MockDB_Client] this.users is not an array in getUserById, initializing empty array');\n            this.users = [];\n            this.saveToLocalStorage(this.userKey, this.users);\n            return null;\n        }\n        const user = this.users.find((u)=>u.id === id);\n        return user ? {\n            ...user\n        } : null;\n    }\n    async createUser(userData) {\n        if (IS_SERVER) {\n            if (!this.db_conn) {\n                console.error('[DB_SERVER] Database connection not available');\n                return null;\n            }\n            if (!userData.email || !userData.password) {\n                console.error('[DB_SERVER] Email and password required for user creation');\n                return null;\n            }\n            try {\n                // Check if user already exists\n                const existingUser = _server_getUserByEmailWithPasswordHash(userData.email.toLowerCase());\n                if (existingUser) {\n                    console.error(\"[DB_SERVER] User with email \".concat(userData.email, \" already exists\"));\n                    return null;\n                }\n                // Create a new user with basic required fields\n                const now = new Date().toISOString();\n                const newUser = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n                    email: userData.email.toLowerCase(),\n                    firstName: userData.firstName || '',\n                    lastName: userData.lastName || '',\n                    role: userData.role || 'user',\n                    createdAt: now,\n                    updatedAt: now,\n                    avatarUrl: userData.avatarUrl || undefined,\n                    phoneNumber: userData.phoneNumber || undefined,\n                    addresses: userData.addresses || [],\n                    emailVerified: userData.emailVerified || false,\n                    lastLogin: undefined\n                };\n                // Hash the password\n                const passwordHash = await _server_hashPassword(userData.password);\n                // Create user in database including password hash\n                // We need to remove fields that aren't in the expected type\n                const { id, createdAt, updatedAt, ...userDataForCreate } = newUser;\n                const user = _server_createUser({\n                    ...userDataForCreate,\n                    password_hash: passwordHash\n                });\n                if (!user) {\n                    console.error('[DB_SERVER] Failed to create user in database');\n                    return null;\n                }\n                // Don't return password hash to client\n                const { password_hash, ...userWithoutHash } = user;\n                return userWithoutHash;\n            } catch (error) {\n                console.error('[DB_SERVER] Error creating user:', error);\n                return null;\n            }\n        }\n        // Client-side mock\n        try {\n            // Check if email already exists in mock data\n            const existingUser = this.users.find((u)=>u.email.toLowerCase() === userData.email.toLowerCase());\n            if (existingUser) {\n                console.error(\"[MockDB_Client] User with email \".concat(userData.email, \" already exists\"));\n                return null;\n            }\n            const now = new Date().toISOString();\n            const newUser = {\n                ...userData,\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n                email: userData.email.toLowerCase(),\n                role: userData.role || 'user',\n                createdAt: now,\n                updatedAt: now,\n                avatarUrl: userData.avatarUrl || undefined,\n                phoneNumber: userData.phoneNumber || undefined,\n                addresses: userData.addresses || [],\n                emailVerified: userData.emailVerified || false,\n                lastLogin: undefined\n            };\n            this.users.push(newUser);\n            this.saveToLocalStorage(this.userKey, this.users);\n            return {\n                ...newUser\n            };\n        } catch (error) {\n            console.error('[MockDB_Client] Error creating user:', error);\n            return null;\n        }\n    }\n    async authenticateUser(email, password) {\n        if (IS_SERVER) {\n            if (!this.db_conn) {\n                console.error('[DB_SERVER] Database connection not available');\n                return null;\n            }\n            try {\n                // First retrieve the user with password hash by email\n                const user = _server_getUserByEmailWithPasswordHash(email.toLowerCase());\n                if (!user || !user.password_hash) {\n                    console.log('[DB_SERVER] User not found or missing password hash:', email);\n                    return null;\n                }\n                // Compare provided password with stored hash\n                const passwordMatch = await _server_comparePassword(password, user.password_hash);\n                if (!passwordMatch) {\n                    console.log('[DB_SERVER] Password mismatch for user:', email);\n                    return null;\n                }\n                // Update last login time\n                const now = new Date().toISOString();\n                const updateStmt = this.db_conn.prepare('UPDATE users SET last_login = ?, email_verified = 1 WHERE id = ?');\n                updateStmt.run(now, user.id);\n                // Fetch the updated user record\n                const updatedUser = _server_getUserById(user.id);\n                if (!updatedUser) {\n                    console.error('[DB_SERVER] Failed to fetch updated user after login');\n                    return null;\n                }\n                // Don't return password hash to the client\n                const { password_hash, ...userWithoutHash } = updatedUser;\n                return userWithoutHash;\n            } catch (error) {\n                console.error('[DB_SERVER] Error during authentication:', error);\n                return null;\n            }\n        }\n        // Client-side mock authentication\n        const user = this.users.find((u)=>u.email.toLowerCase() === email.toLowerCase());\n        if (!user) {\n            console.log('[MockDB_Client] User not found in mock database:', email);\n            return null;\n        }\n        // In client mode, we simulate a successful login by updating last login\n        const now = new Date().toISOString();\n        const updatedUser = {\n            ...user,\n            lastLogin: now,\n            emailVerified: true\n        };\n        // Update the user in local storage\n        const userIndex = this.users.findIndex((u)=>u.id === user.id);\n        if (userIndex !== -1) {\n            this.users[userIndex] = updatedUser;\n            this.saveToLocalStorage(this.userKey, this.users);\n        }\n        return updatedUser;\n    }\n    async updateUser(id, userData) {\n        if (IS_SERVER) {\n            if (!this.db_conn) return null;\n            const existingUser = _server_getUserById(id);\n            if (!existingUser) return null;\n            const fieldsToUpdate = {\n                ...userData,\n                updated_at: new Date().toISOString()\n            };\n            const setClauses = Object.keys(fieldsToUpdate).map((key)=>\"\".concat(key.replace(/[A-Z]/g, (letter)=>\"_\".concat(letter.toLowerCase())), \" = ?\")).join(', ');\n            const values = Object.values(fieldsToUpdate);\n            if (values.length === 0) return existingUser; // No fields to update\n            try {\n                const stmt = this.db_conn.prepare(\"UPDATE users SET \".concat(setClauses, \" WHERE id = ?\"));\n                stmt.run(...values, id);\n                const updatedUser = _server_getUserById(id);\n                if (updatedUser) {\n                    const { password_hash, ...userWithoutHash } = updatedUser;\n                    return userWithoutHash;\n                }\n                return null;\n            } catch (error) {\n                console.error('[DB_SERVER] Error in updateUser:', error);\n                return null;\n            }\n        }\n        // Client-side mock\n        const userIndex = this.users.findIndex((u)=>u.id === id);\n        if (userIndex === -1) return null;\n        this.users[userIndex] = {\n            ...this.users[userIndex],\n            ...userData,\n            updatedAt: new Date().toISOString()\n        };\n        this.saveToLocalStorage(this.userKey, this.users);\n        return {\n            ...this.users[userIndex]\n        };\n    }\n    // --- Product Methods ---\n    async getProducts() {\n        if (IS_SERVER) {\n            return _server_getProducts();\n        }\n        return [\n            ...this.products\n        ];\n    }\n    async createProduct(productData) {\n        if (IS_SERVER) {\n            return _server_createProduct(productData);\n        }\n        // Client-side mock\n        const newId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = new Date().toISOString();\n        const newProduct = {\n            ...productData,\n            id: newId,\n            createdAt: now,\n            reviews: [],\n            rating: 0,\n            reviewCount: 0\n        };\n        this.products.push(newProduct);\n        this.saveToLocalStorage(this.productKey, this.products);\n        return {\n            ...newProduct\n        };\n    }\n    // ... (Other methods like getProductById, updateProduct, deleteProduct should follow similar pattern)\n    async getProductById(id) {\n        if (IS_SERVER) {\n            if (!this.db_conn) return null;\n            try {\n                const stmt = this.db_conn.prepare('SELECT * FROM products WHERE id = ?');\n                const row = stmt.get(id);\n                return row ? mapProductFromDbRow(row) : null;\n            } catch (error) {\n                console.error('[DB_SERVER] Error in getProductById:', error);\n                return null;\n            }\n        }\n        const product = this.products.find((p)=>p.id === id);\n        return product ? {\n            ...product\n        } : null;\n    }\n    // Example for reset (useful for testing or initial setup)\n    async resetLocalUsers() {\n        if (IS_SERVER) {\n            console.warn(\"[DB_SERVER] resetLocalUsers called on server. This will clear the users table.\");\n            if (!this.db_conn) return;\n            try {\n                this.db_conn.exec('DELETE FROM users');\n                console.log(\"[DB_SERVER] All users deleted from database.\");\n            } catch (error) {\n                console.error(\"[DB_SERVER] Error deleting users from database:\", error);\n            }\n            return;\n        }\n        this.users = [];\n        this.saveToLocalStorage(this.userKey, this.users);\n        this._initializeDefaultLocalData(); // Re-initialize with defaults if needed\n        console.log('[MockDB_Client] Local users reset and defaults re-initialized.');\n    }\n    // Service Booking Methods\n    getServiceBookings() {\n        if (IS_SERVER) {\n            if (!this.db_conn) return [];\n            try {\n                const stmt = this.db_conn.prepare('SELECT * FROM service_bookings ORDER BY created_at DESC');\n                return stmt.all();\n            } catch (error) {\n                console.error('[DB_SERVER] Error getting service bookings:', error);\n                return [];\n            }\n        }\n        // Client-side localStorage fallback\n        return this.loadFromLocalStorage('service_bookings', []);\n    }\n    saveServiceBookings(bookings) {\n        if (IS_SERVER) {\n            if (!this.db_conn) return;\n            try {\n                // Clear existing bookings\n                this.db_conn.prepare('DELETE FROM service_bookings').run();\n                // Insert all bookings\n                const stmt = this.db_conn.prepare(\"\\n          INSERT INTO service_bookings (\\n            id, service_name, customer_name, customer_email, customer_phone,\\n            company_name, service_date, preferred_time, urgency, message,\\n            status, notes, created_at, updated_at\\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\\n        \");\n                for (const booking of bookings){\n                    stmt.run(booking.id, booking.serviceName, booking.customerName, booking.customerEmail, booking.customerPhone, booking.companyName, booking.serviceDate, booking.preferredTime, booking.urgency, booking.message, booking.status, booking.notes, booking.createdAt, booking.updatedAt);\n                }\n            } catch (error) {\n                console.error('[DB_SERVER] Error saving service bookings:', error);\n            }\n        } else {\n            // Client-side localStorage\n            this.saveToLocalStorage('service_bookings', bookings);\n        }\n    }\n    saveServiceBooking(booking) {\n        if (IS_SERVER) {\n            if (!this.db_conn) return;\n            try {\n                const stmt = this.db_conn.prepare(\"\\n          INSERT INTO service_bookings (\\n            id, service_name, customer_name, customer_email, customer_phone,\\n            company_name, service_date, preferred_time, urgency, message,\\n            status, notes, created_at, updated_at\\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\\n        \");\n                stmt.run(booking.id, booking.serviceName, booking.customerName, booking.customerEmail, booking.customerPhone, booking.companyName, booking.serviceDate, booking.preferredTime, booking.urgency, booking.message, booking.status, booking.notes, booking.createdAt, booking.updatedAt);\n            } catch (error) {\n                console.error('[DB_SERVER] Error saving service booking:', error);\n            }\n        } else {\n            // Client-side localStorage\n            const bookings = this.getServiceBookings();\n            bookings.push(booking);\n            this.saveToLocalStorage('service_bookings', bookings);\n        }\n    }\n    updateServiceBookingStatus(bookingId, status, notes) {\n        if (IS_SERVER) {\n            if (!this.db_conn) return;\n            try {\n                const stmt = this.db_conn.prepare(\"\\n          UPDATE service_bookings\\n          SET status = ?, notes = ?, updated_at = ?\\n          WHERE id = ?\\n        \");\n                stmt.run(status, notes || '', new Date().toISOString(), bookingId);\n            } catch (error) {\n                console.error('[DB_SERVER] Error updating service booking status:', error);\n            }\n        } else {\n            // Client-side localStorage\n            const bookings = this.getServiceBookings();\n            const bookingIndex = bookings.findIndex((b)=>b.id === bookingId);\n            if (bookingIndex !== -1) {\n                bookings[bookingIndex] = {\n                    ...bookings[bookingIndex],\n                    status,\n                    notes: notes || bookings[bookingIndex].notes,\n                    updatedAt: new Date().toISOString()\n                };\n                this.saveToLocalStorage('service_bookings', bookings);\n            }\n        }\n    }\n    // ... other keys\n    constructor(server_db_connection){\n        this.users = [];\n        this.products = [];\n        // ... other local stores\n        this.userKey = USER_STORAGE_KEY;\n        this.productKey = PRODUCT_STORAGE_KEY;\n        this.db_conn = server_db_connection; // This is the actual 'db' object from server scope\n        if (!IS_SERVER) {\n            this.users = this.loadFromLocalStorage(this.userKey, []);\n            this.products = this.loadFromLocalStorage(this.productKey, []);\n            // ... load other stores\n            this._initializeDefaultLocalData();\n        }\n    }\n}\n// Export a single instance of the database client\n// The 'db' variable from the server scope is passed here.\n// On the client, 'db' will be null, and MockSQLiteDatabase will use localStorage.\nconst sqlite = new MockSQLiteDatabase(db);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/sqlite.ts\n"));

/***/ })

});