'use client';

import { useState, useMemo, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ShoppingCart, Heart, Star, ArrowRight, X, Search,
  Grid, List, SlidersHorizontal, Tag, Filter, ChevronDown,
  ArrowUpDown, ArrowDownUp, CheckCircle, Eye, Truck, Package,
  Sparkles, Flame, TrendingUp, ShieldCheck, Clock, Zap,
  Info, RefreshCw
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Input } from '../ui/Input';
import { LazyImage } from '../ui/LazyImage';
import { EnhancedImage } from '../ui/EnhancedImage';
import { formatCurrency, cn } from '../../lib/utils';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useAuthStore } from '../../stores/authStore';
import { useTheme } from 'next-themes';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { AuthModal } from '../auth/AuthModal';
import { WholesaleQuoteForm } from '../forms/WholesaleQuoteForm';
import { products, productCategories } from '../../data/products';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../ui/animations';
import { Product, ProductFiltersState, SortOption } from '../../types/index';
import { useAuthenticatedAction } from '../../hooks/useAuthenticatedAction';
import { Badge } from '../ui/Badge';
import { Tooltip } from '../ui/Tooltip';
import { EnhancedProductFilters } from './EnhancedProductFilters';
import { ShopHeader } from './ShopHeader';
import { ShopFooter } from './ShopFooter';
import { FeaturedProduct } from './FeaturedProduct';
import { EnhancedProductCard } from '../product/EnhancedProductCard';
import { QuickView } from './QuickView';

interface ShopPageEnhancedProps {
  initialFilters?: {
    featured?: boolean;
    category?: string;
    searchQuery?: string;
  };
}

export const ShopPageEnhanced = ({ initialFilters }: ShopPageEnhancedProps) => {
  const router = useRouter();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showWholesaleForm, setShowWholesaleForm] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [quickViewProduct, setQuickViewProduct] = useState<Product | null>(null);
  const [sortOption, setSortOption] = useState<SortOption>('featured');
  const [isLoading, setIsLoading] = useState(true);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error' | 'info'>('success');

  const maxPrice = useMemo(() => products.reduce((max, p) => p.price > max ? p.price : max, 0), [products]);

  const [filters, setFilters] = useState<ProductFiltersState>({
    category: initialFilters?.category || 'all',
    priceRange: { min: 0, max: maxPrice || 50000 },
    inStock: false,
    onSale: false,
    featured: initialFilters?.featured || false,
    searchQuery: initialFilters?.searchQuery || ''
  });

  // تحديث الفلاتر عند تغير السعر الأقصى
  useEffect(() => {
    setFilters(prevFilters => ({
      ...prevFilters,
      priceRange: {
        ...prevFilters.priceRange,
        max: maxPrice || 50000
      }
    }));
  }, [maxPrice]);

  // محاكاة تحميل البيانات
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 600); // تقليل وقت التحميل لتحسين تجربة المستخدم
    return () => clearTimeout(timer);
  }, []);

  // إغلاق قائمة الترتيب عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = () => {
      if (showSortDropdown) {
        setShowSortDropdown(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showSortDropdown]);

  // حساب عدد الفلاتر النشطة
  useEffect(() => {
    let count = 0;
    if (filters.category !== 'all') count++;
    if (filters.inStock) count++;
    if (filters.onSale) count++;
    if (filters.featured) count++;
    if (filters.searchQuery) count++;
    if (filters.priceRange.min > 0 || filters.priceRange.max < maxPrice) count++;
    setActiveFiltersCount(count);
  }, [filters, maxPrice]);

  // إظهار رسالة نجاح
  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToastMessage(message);
    setToastType(type);
    setShowSuccessToast(true);

    setTimeout(() => {
      setShowSuccessToast(false);
    }, 3000);
  };

  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const { user } = useAuthStore();
  const { theme, resolvedTheme } = useTheme();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // تصفية المنتجات حسب الفلاتر
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      // تصفية حسب الفئة
      if (filters.category !== 'all' && product.category !== filters.category) return false;

      // تصفية حسب المخزون
      if (filters.inStock && product.stock <= 0) return false;

      // تصفية حسب العروض
      if (filters.onSale && (!product.compareAtPrice || product.compareAtPrice <= product.price)) return false;

      // تصفية حسب المنتجات المميزة
      if (filters.featured && !product.featured) return false;

      // تصفية حسب نطاق السعر
      if (product.price < filters.priceRange.min || product.price > filters.priceRange.max) return false;

      // تصفية حسب البحث
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        const nameMatch = product.name.toLowerCase().includes(query);
        const nameArMatch = product.name_ar ? product.name_ar.toLowerCase().includes(query) : false;
        const descMatch = product.description.toLowerCase().includes(query);
        const descArMatch = product.description_ar ? product.description_ar.toLowerCase().includes(query) : false;
        const categoryMatch = product.category.toLowerCase().includes(query);
        const tagsMatch = product.tags.some(tag => tag.toLowerCase().includes(query));

        return nameMatch || nameArMatch || descMatch || descArMatch || categoryMatch || tagsMatch;
      }

      return true;
    });
  }, [filters]);

  // ترتيب المنتجات حسب الخيار المحدد
  const sortedProducts = useMemo(() => {
    let sorted = [...filteredProducts];

    switch (sortOption) {
      case 'featured':
        // ترتيب المنتجات المميزة أولاً، ثم حسب التقييم
        return sorted.sort((a, b) => {
          if (a.featured && !b.featured) return -1;
          if (!a.featured && b.featured) return 1;
          return (b.rating || 0) - (a.rating || 0);
        });

      case 'newest':
        // ترتيب حسب تاريخ الإضافة (الأحدث أولاً)
        return sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      case 'price-asc':
        // ترتيب حسب السعر (من الأقل إلى الأعلى)
        return sorted.sort((a, b) => a.price - b.price);

      case 'price-desc':
        // ترتيب حسب السعر (من الأعلى إلى الأقل)
        return sorted.sort((a, b) => b.price - a.price);

      case 'popular':
        // ترتيب حسب التقييم والمراجعات
        return sorted.sort((a, b) => {
          const aRating = a.rating || 0;
          const bRating = b.rating || 0;
          const aReviews = a.reviewCount || 0;
          const bReviews = b.reviewCount || 0;

          // ترتيب حسب التقييم أولاً، ثم حسب عدد المراجعات
          if (aRating !== bRating) return bRating - aRating;
          return bReviews - aReviews;
        });

      case 'discount':
        // ترتيب حسب نسبة الخصم (الأعلى أولاً)
        return sorted.sort((a, b) => {
          const aDiscount = a.compareAtPrice ? (a.compareAtPrice - a.price) / a.compareAtPrice : 0;
          const bDiscount = b.compareAtPrice ? (b.compareAtPrice - b.price) / b.compareAtPrice : 0;
          return bDiscount - aDiscount;
        });

      default:
        return sorted;
    }
  }, [filteredProducts, sortOption]);

  const handleUnauthenticated = () => {
    setShowAuthModal(true);
  };

  const handleAddToCart = useAuthenticatedAction((product: Product) => {
    cartStore.addItem(product, 1);
    // إظهار رسالة نجاح
    const message = currentLanguage === 'ar'
      ? `تمت إضافة ${product.name} إلى سلة التسوق`
      : `${product.name} added to cart`;
    showToast(message, 'success');
  }, handleUnauthenticated);

  const handleWholesaleInquiry = useAuthenticatedAction((product: Product) => {
    setSelectedProduct(product);
    setShowWholesaleForm(true);
  }, handleUnauthenticated);

  const toggleWishlist = useAuthenticatedAction((product: Product) => {
    if (wishlistStore.isInWishlist(product.id)) {
      wishlistStore.removeItem(product.id);
      const message = currentLanguage === 'ar'
        ? `تمت إزالة ${product.name} من المفضلة`
        : `${product.name} removed from wishlist`;
      showToast(message, 'info');
    } else {
      wishlistStore.addItem(product);
      const message = currentLanguage === 'ar'
        ? `تمت إضافة ${product.name} إلى المفضلة`
        : `${product.name} added to wishlist`;
      showToast(message, 'success');
    }
  }, handleUnauthenticated);

  const handleQuickView = (product: Product) => {
    setQuickViewProduct(product);
  };

  const resetFilters = () => {
    setFilters({
      category: 'all',
      priceRange: { min: 0, max: maxPrice || 50000 },
      inStock: false,
      onSale: false,
      featured: false,
      searchQuery: ''
    });
    setSortOption('featured');
    setShowMobileFilters(false);

    // إظهار رسالة إعادة تعيين الفلاتر
    const message = currentLanguage === 'ar'
      ? 'تم إعادة تعيين جميع الفلاتر'
      : 'All filters have been reset';
    showToast(message, 'info');
  };

  // تبديل وضع العرض (شبكة/قائمة)
  const toggleViewMode = () => {
    setViewMode(prev => prev === 'grid' ? 'list' : 'grid');
  };

  // التحقق من وجود منتجات مميزة
  const hasFeaturedProducts = useMemo(() => {
    return products.some(product => product.featured);
  }, [products]);

  // الحصول على المنتجات المميزة
  const featuredProducts = useMemo(() => {
    return products.filter(product => product.featured).slice(0, 4);
  }, [products]);

  // الحصول على المنتجات الأكثر مبيعًا
  const bestSellingProducts = useMemo(() => {
    return [...products]
      .sort((a, b) => (b.rating || 0) - (a.rating || 0))
      .slice(0, 4);
  }, [products]);

  // الحصول على المنتجات الجديدة
  const newArrivals = useMemo(() => {
    return [...products]
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 4);
  }, [products]);

  // تحديث URL مع الفلاتر النشطة
  const updateUrlWithFilters = () => {
    const params = new URLSearchParams();
    if (filters.featured) params.set('featured', 'true');
    if (filters.category !== 'all') params.set('category', filters.category);
    if (filters.searchQuery) params.set('q', filters.searchQuery);
    if (filters.onSale) params.set('sale', 'true');
    if (filters.inStock) params.set('instock', 'true');
    if (filters.priceRange.min > 0) params.set('min', filters.priceRange.min.toString());
    if (filters.priceRange.max < maxPrice) params.set('max', filters.priceRange.max.toString());

    const url = `/${currentLanguage}/shop${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(url, { scroll: false });
  };

  // تحديث URL عند تغيير الفلاتر
  useEffect(() => {
    updateUrlWithFilters();
  }, [filters]);

  return (
    <div className="container-custom py-8">
      {/* شريط البحث البسيط */}
      <div className="mb-6">
        <div className="flex items-center gap-4">
          <div className="flex-1 relative">
            <Search className={cn(
              "absolute top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5 z-10 pointer-events-none",
              isRTL ? "right-3" : "left-3"
            )} />
            <Input
              type="text"
              placeholder={currentLanguage === 'ar' ? 'ابحث عن منتجات...' : 'Search for products...'}
              value={filters.searchQuery}
              onChange={(e) => setFilters(prev => ({ ...prev, searchQuery: e.target.value }))}
              className={cn(
                "w-full py-3 rounded-lg border-slate-200 dark:border-slate-700 focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
                isRTL ? "pr-10 pl-4" : "pl-10 pr-4"
              )}
            />
          </div>
        </div>
      </div>

      {/* فئات المنتجات */}
      <ShopHeader
        onSearch={(query) => setFilters(prev => ({ ...prev, searchQuery: query }))}
        onCategorySelect={(category) => setFilters(prev => ({ ...prev, category }))}
        searchQuery={filters.searchQuery}
        selectedCategory={filters.category}
      />



      {/* محتوى المتجر الرئيسي */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* الشريط الجانبي */}
        <div className="lg:col-span-1">
          {/* فلاتر المنتجات */}
          <div className="sticky top-24 z-30">
            <EnhancedProductFilters
              filters={filters}
              setFilters={setFilters}
              resetFilters={resetFilters}
              maxPrice={maxPrice}
              productCategories={productCategories}
              showMobileFilters={showMobileFilters}
              setShowMobileFilters={setShowMobileFilters}
              activeFiltersCount={activeFiltersCount}
              tags={Array.from(new Set(products.flatMap(p => p.tags)))}
            />
          </div>

          {/* منتج مميز في الشريط الجانبي */}
          {hasFeaturedProducts && featuredProducts.length > 0 && (
            <div className="mt-8 hidden lg:block relative z-10">
              <FeaturedProduct
                product={featuredProducts[0]}
                onAddToCart={handleAddToCart}
                onToggleWishlist={toggleWishlist}
              />
            </div>
          )}

          {/* المنتجات الأكثر مبيعًا */}
          <div className="mt-8 hidden lg:block relative z-10">
            <Card className="overflow-hidden border border-primary-100 dark:border-primary-800">
              {/* عنوان القسم */}
              <div className="bg-primary-50 dark:bg-primary-900/30 p-3 border-b border-primary-100 dark:border-primary-800">
                <h3 className="font-semibold text-primary-800 dark:text-primary-300 flex items-center">
                  <Flame className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'} text-amber-500`} />
                  {currentLanguage === 'ar' ? 'الأكثر مبيعًا' : 'Best Sellers'}
                </h3>
              </div>

              {/* قائمة المنتجات */}
              <div className="divide-y divide-slate-200 dark:divide-slate-700">
                {bestSellingProducts.slice(0, 3).map((product) => (
                  <Link
                    key={product.id}
                    href={`/${currentLanguage}/shop/${product.slug}`}
                    className="flex p-3 hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors"
                  >
                    {/* صورة المنتج */}
                    <div className="relative w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
                      <EnhancedImage
                        src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                        alt={currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                        fill={true}
                        objectFit="cover"
                      />
                    </div>

                    {/* معلومات المنتج */}
                    <div className="ml-3 flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-slate-900 dark:text-white truncate">
                        {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                      </h4>

                      <div className="flex items-center mt-1">
                        <div className="flex text-yellow-500">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={cn(
                                "h-3 w-3",
                                i < Math.floor(product.rating || 0) ? "fill-current" : "text-slate-300 dark:text-slate-600"
                              )}
                            />
                          ))}
                        </div>
                        <span className="ml-1 text-xs text-slate-500 dark:text-slate-400">
                          ({product.reviewCount || 0})
                        </span>
                      </div>

                      <div className="flex items-baseline mt-1">
                        <span className="text-sm font-bold text-primary-600 dark:text-primary-400">
                          {formatCurrency(product.price)}
                        </span>
                        {product.compareAtPrice && product.compareAtPrice > product.price && (
                          <span className="ml-1 text-xs text-slate-500 line-through">
                            {formatCurrency(product.compareAtPrice)}
                          </span>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>

              {/* رابط عرض المزيد */}
              <div className="p-3 bg-primary-50/50 dark:bg-primary-900/20 border-t border-primary-100 dark:border-primary-800">
                <Link
                  href={`/${currentLanguage}/shop?sort=popular`}
                  className="text-sm text-primary-600 dark:text-primary-400 hover:underline flex items-center justify-center"
                  onClick={(e) => {
                    e.preventDefault();
                    setSortOption('popular');
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                  }}
                >
                  {currentLanguage === 'ar' ? 'عرض المزيد من المنتجات الرائجة' : 'View More Popular Products'}
                  <ArrowRight className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} />
                </Link>
              </div>
            </Card>
          </div>
        </div>

        {/* شبكة المنتجات */}
        <div className="lg:col-span-3">
          {/* أدوات الترتيب وتبديل طريقة العرض */}
          <div className="flex flex-wrap justify-between items-center mb-6 bg-white dark:bg-slate-800 p-4 rounded-lg shadow-sm">
            <div className="flex items-center mb-2 sm:mb-0">
              <span className="text-sm text-slate-600 dark:text-slate-400 mr-2">
                {currentLanguage === 'ar'
                  ? `${sortedProducts.length} منتج`
                  : `${sortedProducts.length} products`}
              </span>

              <div className="relative">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowSortDropdown(!showSortDropdown);
                  }}
                >
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  {currentLanguage === 'ar' ? 'ترتيب حسب' : 'Sort by'}
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>

                {showSortDropdown && (
                  <div className="absolute z-10 mt-1 w-48 bg-white dark:bg-slate-800 rounded-md shadow-lg border border-slate-200 dark:border-slate-700">
                    <div className="py-1">
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'featured' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('featured');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'المميزة' : 'Featured'}
                      </button>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'newest' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('newest');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'الأحدث' : 'Newest'}
                      </button>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'price-asc' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('price-asc');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'السعر: من الأقل إلى الأعلى' : 'Price: Low to High'}
                      </button>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'price-desc' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('price-desc');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'السعر: من الأعلى إلى الأقل' : 'Price: High to Low'}
                      </button>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'popular' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('popular');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'الأكثر شعبية' : 'Most Popular'}
                      </button>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'discount' ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                        onClick={() => {
                          setSortOption('discount');
                          setShowSortDropdown(false);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'أعلى خصم' : 'Biggest Discount'}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center">
              <Tooltip content={currentLanguage === 'ar' ? 'تبديل طريقة العرض' : 'Toggle view mode'}>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleViewMode}
                  className="mr-2 hover:scale-105 transition-transform duration-200"
                  aria-label={currentLanguage === 'ar' ? 'تبديل طريقة العرض' : 'Toggle view mode'}
                >
                  {viewMode === 'grid' ? (
                    <List className="h-4 w-4" />
                  ) : (
                    <Grid className="h-4 w-4" />
                  )}
                </Button>
              </Tooltip>

              <Button
                variant={activeFiltersCount > 0 ? "default" : "outline"}
                size="sm"
                onClick={() => setShowMobileFilters(!showMobileFilters)}
                className="lg:hidden hover:scale-105 transition-transform duration-200"
              >
                <Filter className="h-4 w-4 mr-2" />
                {currentLanguage === 'ar' ? 'الفلاتر' : 'Filters'}
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="ml-2 bg-white text-primary-700">
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </div>
          </div>

          {/* شبكة المنتجات */}
          {isLoading ? (
            // حالة التحميل
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white dark:bg-slate-800 rounded-lg overflow-hidden animate-pulse border border-slate-200 dark:border-slate-700 shadow-sm">
                  <div className="aspect-square bg-slate-200 dark:bg-slate-700 relative">
                    {/* محاكاة شارات المنتج */}
                    <div className="absolute top-2 left-2 h-5 w-16 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
                    {/* محاكاة أزرار الإجراءات */}
                    <div className="absolute bottom-2 right-2 flex gap-1">
                      <div className="h-8 w-8 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
                      <div className="h-8 w-8 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
                    </div>
                  </div>
                  <div className="p-4 space-y-3">
                    {/* محاكاة الفئة والتقييم */}
                    <div className="flex justify-between">
                      <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/4"></div>
                      <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/4"></div>
                    </div>
                    {/* محاكاة اسم المنتج */}
                    <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded w-3/4"></div>
                    {/* محاكاة وصف المنتج */}
                    <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-full"></div>
                    <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-2/3"></div>
                    {/* محاكاة السعر والمخزون */}
                    <div className="flex justify-between pt-2">
                      <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded w-1/3"></div>
                      <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded w-1/5"></div>
                    </div>
                    {/* محاكاة زر الإضافة إلى السلة */}
                    <div className="h-9 bg-slate-200 dark:bg-slate-700 rounded w-full mt-4"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : sortedProducts.length === 0 ? (
            // لا توجد منتجات
            <div className="bg-white dark:bg-slate-800 rounded-lg p-8 text-center border border-slate-200 dark:border-slate-700 shadow-sm">
              <div className="flex justify-center mb-6">
                <div className="relative">
                  <div className="absolute -inset-4 rounded-full bg-slate-100 dark:bg-slate-700/50 animate-pulse"></div>
                  <Package className="h-16 w-16 text-slate-400 dark:text-slate-500 relative z-10" />
                </div>
              </div>
              <h3 className="text-2xl font-semibold text-slate-900 dark:text-white mb-3">
                {currentLanguage === 'ar' ? 'لا توجد منتجات' : 'No Products Found'}
              </h3>
              <p className="text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto">
                {currentLanguage === 'ar'
                  ? 'لم نتمكن من العثور على أي منتجات تطابق معايير البحث الخاصة بك. يرجى تجربة معايير بحث مختلفة أو إعادة تعيين الفلاتر.'
                  : 'We couldn\'t find any products that match your search criteria. Try different search terms or reset the filters.'}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button variant="default" onClick={resetFilters} className="flex items-center justify-center">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {currentLanguage === 'ar' ? 'إعادة تعيين الفلاتر' : 'Reset Filters'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setFilters({
                      category: 'all',
                      priceRange: { min: 0, max: maxPrice || 50000 },
                      inStock: false,
                      onSale: false,
                      featured: false,
                      searchQuery: ''
                    });
                    setSortOption('featured');
                  }}
                  className="flex items-center justify-center"
                >
                  <Tag className="h-4 w-4 mr-2" />
                  {currentLanguage === 'ar' ? 'تصفح جميع المنتجات' : 'Browse All Products'}
                </Button>
              </div>
            </div>
          ) : (
            // عرض المنتجات
            <div className="space-y-8">


              {/* عرض جميع المنتجات */}
              <div>
                {sortOption !== 'featured' || filters.featured || filters.onSale || filters.searchQuery ? (
                  <div className="flex items-center mb-4">
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                      {filters.searchQuery ? (
                        currentLanguage === 'ar' ? `نتائج البحث: "${filters.searchQuery}"` : `Search Results: "${filters.searchQuery}"`
                      ) : filters.featured ? (
                        currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured Products'
                      ) : filters.onSale ? (
                        currentLanguage === 'ar' ? 'المنتجات المخفضة' : 'Sale Products'
                      ) : sortOption === 'newest' ? (
                        currentLanguage === 'ar' ? 'أحدث المنتجات' : 'Newest Products'
                      ) : sortOption === 'popular' ? (
                        currentLanguage === 'ar' ? 'المنتجات الأكثر شعبية' : 'Popular Products'
                      ) : sortOption === 'price-asc' ? (
                        currentLanguage === 'ar' ? 'المنتجات من الأقل إلى الأعلى سعرًا' : 'Products: Low to High Price'
                      ) : sortOption === 'price-desc' ? (
                        currentLanguage === 'ar' ? 'المنتجات من الأعلى إلى الأقل سعرًا' : 'Products: High to Low Price'
                      ) : (
                        currentLanguage === 'ar' ? 'جميع المنتجات' : 'All Products'
                      )}
                    </h3>
                  </div>
                ) : (
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                      {currentLanguage === 'ar' ? 'جميع المنتجات' : 'All Products'}
                    </h3>
                  </div>
                )}

                <div className={cn(
                  viewMode === 'grid'
                    ? "grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6"
                    : "flex flex-col gap-4"
                )}>
                  {sortedProducts.map((product, index) => (
                    <EnhancedProductCard
                      key={product.id}
                      product={product}
                      index={index}
                      showQuickView={true}
                      showAddToCart={true}
                      showWishlist={true}
                      onQuickView={handleQuickView}
                      onAddToCart={handleAddToCart}
                      onToggleWishlist={toggleWishlist}
                      onWholesaleInquiry={handleWholesaleInquiry}
                      viewMode={viewMode}
                      className="h-full"
                    />
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* ترقيم الصفحات وقسم النشرة الإخبارية */}
          <ShopFooter
            totalProducts={sortedProducts.length}
            currentPage={1}
            itemsPerPage={12}
            onPageChange={(page) => console.log(`Navigate to page ${page}`)}
          />
        </div>
      </div>

      {/* نافذة النظرة السريعة */}
      {quickViewProduct && (
        <QuickView
          product={quickViewProduct}
          onClose={() => setQuickViewProduct(null)}
          onAddToCart={handleAddToCart}
          onToggleWishlist={toggleWishlist}
        />
      )}

      {/* نافذة طلب عرض سعر للجملة */}
      {showWholesaleForm && selectedProduct && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
          <div className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <WholesaleQuoteForm
              product={selectedProduct}
              selectedProduct={selectedProduct}
              onClose={() => {
                setShowWholesaleForm(false);
                setSelectedProduct(null);
              }}
            />
          </div>
        </div>
      )}

      {/* نافذة تسجيل الدخول */}
      {showAuthModal && (
        <AuthModal
          onClose={() => setShowAuthModal(false)}
          defaultTab="login"
        />
      )}

      {/* رسالة النجاح */}
      {showSuccessToast && (
        <div className={cn(
          "fixed bottom-4 right-4 z-50 p-4 rounded-lg shadow-xl max-w-md",
          "animate-bounce-in transition-all duration-300",
          "backdrop-blur-md border",
          toastType === 'success' ? "bg-green-500/90 text-white border-green-400" :
          toastType === 'error' ? "bg-red-500/90 text-white border-red-400" :
          "bg-blue-500/90 text-white border-blue-400"
        )}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full mr-3",
                toastType === 'success' ? "bg-green-600" :
                toastType === 'error' ? "bg-red-600" :
                "bg-blue-600"
              )}>
                {toastType === 'success' && <CheckCircle className="h-5 w-5" />}
                {toastType === 'error' && <X className="h-5 w-5" />}
                {toastType === 'info' && <Info className="h-5 w-5" />}
              </div>
              <div>
                <h4 className="font-medium mb-1">
                  {toastType === 'success' ? (currentLanguage === 'ar' ? 'تم بنجاح' : 'Success') :
                   toastType === 'error' ? (currentLanguage === 'ar' ? 'خطأ' : 'Error') :
                   (currentLanguage === 'ar' ? 'معلومات' : 'Information')}
                </h4>
                <p className="text-sm text-white/90">{toastMessage}</p>
              </div>
            </div>
            <button
              onClick={() => setShowSuccessToast(false)}
              className="ml-4 p-1 rounded-full hover:bg-white/20 transition-colors"
              aria-label={currentLanguage === 'ar' ? 'إغلاق' : 'Close'}
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
