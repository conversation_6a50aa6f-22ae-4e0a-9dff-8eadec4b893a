/**
 * مكتبة SQLite للتعامل مع قاعدة البيانات المحلية
 * تستبدل هذه المكتبة استخدام Supabase في المشروع
 */

import { v4 as uuidv4 } from 'uuid';
import type { Product, User, Review, ProductionLine, Service, BlogPost } from '../types/index';
import bcrypt from 'bcryptjs'; // Added for password hashing

const IS_SERVER = typeof window === 'undefined';
const SALT_ROUNDS = 10; // For bcrypt hashing

let db: any = null;

if (IS_SERVER) {
  try {
    const BetterSqlite3 = require('better-sqlite3');
    const path = require('path');
    const fs = require('fs');

    const dbDir = path.join(process.cwd(), 'server', 'db');
    const dbPath = path.join(dbDir, 'ecommerce.sqlite');

    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
      console.log(`[DB_SERVER] Created database directory: ${dbDir}`);
    }

    console.log(`[DB_SERVER] Attempting to connect to SQLite database at: ${dbPath}`);
    db = new BetterSqlite3(dbPath, { /* verbose: console.log */ }); // Verbose logging can be noisy
    console.log('[DB_SERVER] Successfully connected to SQLite database.');

    // Ensure tables are created (idempotent)
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        first_name TEXT,
        last_name TEXT,
        role TEXT DEFAULT 'user' NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        avatar_url TEXT,
        phone_number TEXT,
        addresses TEXT, -- Store as JSON string
        email_verified INTEGER DEFAULT 0, -- 0 for false, 1 for true
        last_login TEXT
      );
    `);
    console.log('[DB_SERVER] Users table checked/created.');

    db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        compare_at_price REAL,
        images TEXT,
        category TEXT,
        tags TEXT,
        stock INTEGER DEFAULT 0,
        featured INTEGER DEFAULT 0,
        specifications TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        rating REAL,
        review_count INTEGER,
        related_products TEXT
      );
    `);
    console.log('[DB_SERVER] Products table checked/created.');

    db.exec(`
      CREATE TABLE IF NOT EXISTS service_bookings (
        id TEXT PRIMARY KEY,
        service_name TEXT NOT NULL,
        customer_name TEXT NOT NULL,
        customer_email TEXT NOT NULL,
        customer_phone TEXT NOT NULL,
        company_name TEXT,
        service_date TEXT NOT NULL,
        preferred_time TEXT,
        urgency TEXT DEFAULT 'normal',
        message TEXT,
        status TEXT DEFAULT 'pending',
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
      );
    `);
    console.log('[DB_SERVER] Service bookings table checked/created.');

    // Add other table creations here as needed (services, blog_posts, etc.)

    console.log('[DB_SERVER] Database schema checked/initialized.');

  } catch (error) {
    console.error('[DB_SERVER] Critical error during database initialization:', error);
    db = null;
  }
} else {
  console.log('[MockDB_Client] Running in client mode, SQLite DB not initialized.');
}

// --- Server-side Helper Functions ---
async function _server_hashPassword(password: string): Promise<string> {
  if (!IS_SERVER) throw new Error('_server_hashPassword can only be called on the server.');
  return bcrypt.hash(password, SALT_ROUNDS);
}

async function _server_comparePassword(password: string, hash: string): Promise<boolean> {
  if (!IS_SERVER) throw new Error('_server_comparePassword can only be called on the server.');
  return bcrypt.compare(password, hash);
}

function _server_getUserByEmailWithPasswordHash(email: string): User | null {
  if (!IS_SERVER || !db) return null;
  try {
    const stmt = db.prepare('SELECT * FROM users WHERE email = ?');
    const row = stmt.get(email.toLowerCase());
    return row ? mapUserFromDbRow(row) : null;
  } catch (error) {
    console.error('[DB_SERVER] Error in _server_getUserByEmailWithPasswordHash:', error);
    return null;
  }
}

function _server_getUserById(id: string): User | null {
  if (!IS_SERVER || !db) return null;
  try {
    const stmt = db.prepare('SELECT * FROM users WHERE id = ?');
    const row = stmt.get(id);
    return row ? mapUserFromDbRow(row) : null;
  } catch (error) {
    console.error('[DB_SERVER] Error in _server_getUserById:', error);
    return null;
  }
}

function _server_createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'password_hash'> & { password_hash: string }): User | null {
  if (!IS_SERVER || !db) return null;
  try {
    const now = new Date().toISOString();

    // Prepare addresses as JSON string if provided
    const addressesJson = userData.addresses && userData.addresses.length > 0
      ? JSON.stringify(userData.addresses)
      : null;

    // Generate a new ID if not provided
    const userId = uuidv4();

    const stmt = db.prepare(`
      INSERT INTO users (
        id, email, password_hash, first_name, last_name, role,
        created_at, updated_at, avatar_url, phone_number,
        addresses, email_verified, last_login
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      userId,
      userData.email.toLowerCase(),
      userData.password_hash,
      userData.firstName || '',
      userData.lastName || '',
      userData.role || 'user',
      now,
      now,
      userData.avatarUrl || null,
      userData.phoneNumber || null,
      addressesJson,
      userData.emailVerified ? 1 : 0,
      userData.lastLogin || null
    );

    return _server_getUserById(userId);
  } catch (error) {
    console.error('[DB_SERVER] Error in _server_createUser:', error);
    return null;
  }
}

function _server_getProducts(): Product[] {
  if (!IS_SERVER || !db) return [];
  try {
    const stmt = db.prepare('SELECT * FROM products ORDER BY created_at DESC');
    const rows = stmt.all();
    return rows.map(mapProductFromDbRow);
  } catch (error) {
    console.error('[DB_SERVER] Error in _server_getProducts:', error);
    return [];
  }
}

function _server_createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt' | 'reviews' | 'rating' | 'reviewCount'>): Product | null {
  if (!IS_SERVER || !db) return null;
  const newId = uuidv4();
  const now = new Date().toISOString();
  try {
    const stmt = db.prepare(`
      INSERT INTO products (id, name, slug, description, price, compare_at_price, images, category, tags, stock, featured, specifications, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    stmt.run(
      newId,
      productData.name,
      productData.slug,
      productData.description,
      productData.price,
      productData.compareAtPrice,
      JSON.stringify(productData.images || []),
      productData.category,
      JSON.stringify(productData.tags || []),
      productData.stock,
      productData.featured ? 1 : 0,
      JSON.stringify(productData.specifications || {}),
      now
    );
    // To get the full product, we'd ideally query it back, but for now let's construct it
    return {
        ...productData,
        id: newId,
        createdAt: now,
        reviews: [],
        rating: 0,
        reviewCount: 0
    } as Product; // Cast as Product, assuming defaults for reviews/rating
  } catch (error) {
    console.error('[DB_SERVER] Error in _server_createProduct:', error);
    return null;
  }
}

// --- Helper function to safely parse JSON (used for localStorage) ---
function safeJsonParse(jsonString: string | null | undefined, defaultValue: any): any {
  if (jsonString == null) return defaultValue;
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    // console.error('Failed to parse JSON string:', e, '\nString was:', jsonString);
    return defaultValue;
  }
}

// --- localStorage keys ---
const USER_STORAGE_KEY = 'sqlite_users';
const PRODUCT_STORAGE_KEY = 'sqlite_products';
// ... other keys

// --- Default data for client-side localStorage initialization ---
const DEFAULT_LOCAL_USERS: Array<Omit<User, 'id' | 'createdAt' | 'password_hash'>> = [
  { email: '<EMAIL>', firstName: 'Admin', lastName: 'Local', role: 'admin' },
  { email: '<EMAIL>', firstName: 'Test', lastName: 'Local', role: 'user' },
];

// ... DEFAULT_LOCAL_PRODUCTS (ensure it matches Product type, omitting server-generated fields)
const DEFAULT_LOCAL_PRODUCTS: Array<Omit<Product, 'id' | 'createdAt' | 'updatedAt' | 'reviews' | 'rating' | 'reviewCount' | 'relatedProducts'>> = [
  {
    name: 'Default Local Product 1',
    slug: 'default-local-product-1',
    description: 'This is a default product for localStorage.',
    price: 19.99,
    compareAtPrice: 29.99,
    images: [],
    category: 'Local Category',
    tags: ['default', 'localstorage'],
    stock: 100,
    featured: true,
    specifications: { material: 'local_plastic' },
  },
];

// --- Helper function to map database row to User type (includes password_hash for internal use) ---
function mapUserFromDbRow(row: any): User {
  if (!row) return row;

  // Parse addresses from JSON string if present
  let addresses = [];
  try {
    if (row.addresses) {
      addresses = JSON.parse(row.addresses);
    }
  } catch (e) {
    console.error('[DB] Error parsing user addresses:', e);
  }

  // Map all fields from the database row to the User type
  const user: User = {
    id: row.id,
    email: row.email,
    firstName: row.first_name || '',
    lastName: row.last_name || '',
    role: row.role || 'user',
    createdAt: row.created_at,
    updatedAt: row.updated_at || null,
    avatarUrl: row.avatar_url || null,
    phoneNumber: row.phone_number || null,
    addresses: addresses,
    emailVerified: Boolean(row.email_verified),
    lastLogin: row.last_login || null
  } as User;

  // Include password_hash for internal authentication
  if (row.password_hash) {
    (user as any).password_hash = row.password_hash;
  }

  return user;
}

// --- Helper function to map database row to Product type ---
function mapProductFromDbRow(row: any): Product {
  if (!row) return null as any;
  return {
    id: row.id,
    name: row.name,
    slug: row.slug,
    description: row.description,
    price: row.price ? parseFloat(row.price) : 0,
    compareAtPrice: row.compare_at_price ? parseFloat(row.compare_at_price) : undefined,
    images: row.images ? safeJsonParse(row.images, []) : [],
    category: row.category,
    tags: row.tags ? safeJsonParse(row.tags, []) : [],
    stock: row.stock ? parseInt(row.stock, 10) : 0,
    featured: Boolean(row.featured),
    specifications: row.specifications ? safeJsonParse(row.specifications, {}) : {},
    createdAt: row.created_at,
    updatedAt: row.updated_at,
    reviews: [], // Placeholder, to be implemented
    rating: row.rating ? parseFloat(row.rating) : undefined,
    reviewCount: row.review_count ? parseInt(row.review_count, 10) : undefined,
    relatedProducts: row.related_products ? safeJsonParse(row.related_products, []) : [],
  };
}

class MockSQLiteDatabase {
  private db_conn: any;
  private users: User[] = [];
  private products: Product[] = [];
  // ... other local stores

  private readonly userKey = USER_STORAGE_KEY;
  private readonly productKey = PRODUCT_STORAGE_KEY;
  // ... other keys

  constructor(server_db_connection: any) {
    this.db_conn = server_db_connection; // This is the actual 'db' object from server scope
    if (!IS_SERVER) {
      this.users = this.loadFromLocalStorage(this.userKey, []);
      this.products = this.loadFromLocalStorage(this.productKey, []);
      // ... load other stores
      this._initializeDefaultLocalData();
    }
  }

  private loadFromLocalStorage<T>(key: string, defaultValue: T[]): T[] {
    if (IS_SERVER) return defaultValue; // Should not happen with current constructor logic
    try {
      const data = localStorage.getItem(key);
      const parsedData = data ? safeJsonParse(data, defaultValue) : defaultValue;
      // تأكد من أن البيانات المحملة هي مصفوفة
      if (!Array.isArray(parsedData)) {
        console.warn(`[MockDB_Client] Data loaded from ${key} is not an array, using default value`);
        return defaultValue;
      }
      return parsedData;
    } catch (error) {
      console.error(`[MockDB_Client] Error loading data from localStorage key ${key}:`, error);
      return defaultValue;
    }
  }

  private saveToLocalStorage<T>(key: string, data: T[]): void {
    if (IS_SERVER) return;
    localStorage.setItem(key, JSON.stringify(data));
  }

  private _initializeDefaultLocalData(): void {
    if (IS_SERVER) return;
    if (this.users.length === 0) {
      this.users = DEFAULT_LOCAL_USERS.map(u => ({
        ...u,
        id: uuidv4(),
        createdAt: new Date().toISOString(),
        // No password_hash for local mock users used directly for display/testing
      } as User));
      this.saveToLocalStorage(this.userKey, this.users);
    }
    if (this.products.length === 0) {
      this.products = DEFAULT_LOCAL_PRODUCTS.map(p => ({
        ...p,
        id: uuidv4(),
        createdAt: new Date().toISOString(),
        reviews: [],
        rating: 0,
        reviewCount: 0,
        relatedProducts: [],
      } as Product));
      this.saveToLocalStorage(this.productKey, this.products);
    }
  }

  // --- User Methods ---
  async getUsers(): Promise<User[]> {
    if (IS_SERVER) {
      if (!this.db_conn) return [];
      try {
        const stmt = this.db_conn.prepare('SELECT * FROM users');
        const rows = stmt.all();
        // Exclude password_hash from results sent to client/general use
        return rows.map(mapUserFromDbRow).map((userWithHash: User) => {
          const { password_hash, ...userWithoutHash } = userWithHash;
          return userWithoutHash as User; // Ensure the final object is also User
        });
      } catch (error) {
        console.error('[DB_SERVER] Error in getUsers:', error);
        return [];
      }
    }
    // تأكد من أن this.users هو مصفوفة
    if (!Array.isArray(this.users)) {
      console.warn('[MockDB_Client] this.users is not an array, initializing empty array');
      this.users = [];
      this.saveToLocalStorage(this.userKey, this.users);
    }
    return [...this.users];
  }

  async getUserByEmail(email: string): Promise<User | null> {
    if (IS_SERVER) {
      const userWithHash = _server_getUserByEmailWithPasswordHash(email.toLowerCase());
      if (userWithHash) {
        const { password_hash, ...userWithoutHash } = userWithHash;
        return userWithoutHash as User;
      }
      return null;
    }
    // تأكد من أن this.users هو مصفوفة
    if (!Array.isArray(this.users)) {
      console.warn('[MockDB_Client] this.users is not an array in getUserByEmail, initializing empty array');
      this.users = [];
      this.saveToLocalStorage(this.userKey, this.users);
      return null;
    }
    const user = this.users.find(u => u.email.toLowerCase() === email.toLowerCase());
    return user ? { ...user } : null; // Return a copy
  }

  async getUserById(id: string): Promise<User | null> {
    if (IS_SERVER) {
        const userWithHash = _server_getUserById(id);
        if (userWithHash) {
            const { password_hash, ...userWithoutHash } = userWithHash;
            return userWithoutHash as User;
        }
        return null;
    }
    // تأكد من أن this.users هو مصفوفة
    if (!Array.isArray(this.users)) {
      console.warn('[MockDB_Client] this.users is not an array in getUserById, initializing empty array');
      this.users = [];
      this.saveToLocalStorage(this.userKey, this.users);
      return null;
    }
    const user = this.users.find(u => u.id === id);
    return user ? { ...user } : null;
  }

  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'password_hash'> & { password?: string }): Promise<User | null> {
    if (IS_SERVER) {
      if (!this.db_conn) {
        console.error('[DB_SERVER] Database connection not available');
        return null;
      }

      if (!userData.email || !userData.password) {
        console.error('[DB_SERVER] Email and password required for user creation');
        return null;
      }

      try {
        // Check if user already exists
        const existingUser = _server_getUserByEmailWithPasswordHash(userData.email.toLowerCase());
        if (existingUser) {
          console.error(`[DB_SERVER] User with email ${userData.email} already exists`);
          return null;
        }

        // Create a new user with basic required fields
        const now = new Date().toISOString();
        const newUser = {
          id: uuidv4(),
          email: userData.email.toLowerCase(),
          firstName: userData.firstName || '',
          lastName: userData.lastName || '',
          role: userData.role || 'user',
          createdAt: now,
          updatedAt: now,
          avatarUrl: userData.avatarUrl || undefined,
          phoneNumber: userData.phoneNumber || undefined,
          addresses: userData.addresses || [],
          emailVerified: userData.emailVerified || false,
          lastLogin: undefined,
        };

        // Hash the password
        const passwordHash = await _server_hashPassword(userData.password);

        // Create user in database including password hash
        // We need to remove fields that aren't in the expected type
        const { id, createdAt, updatedAt, ...userDataForCreate } = newUser;
        const user = _server_createUser({ ...userDataForCreate, password_hash: passwordHash });

        if (!user) {
          console.error('[DB_SERVER] Failed to create user in database');
          return null;
        }

        // Don't return password hash to client
        const { password_hash, ...userWithoutHash } = user;
        return userWithoutHash as User;
      } catch (error) {
        console.error('[DB_SERVER] Error creating user:', error);
        return null;
      }
    }

    // Client-side mock
    try {
      // Check if email already exists in mock data
      const existingUser = this.users.find(u => u.email.toLowerCase() === userData.email.toLowerCase());
      if (existingUser) {
        console.error(`[MockDB_Client] User with email ${userData.email} already exists`);
        return null;
      }

      const now = new Date().toISOString();
      const newUser: User = {
        ...userData,
        id: uuidv4(),
        email: userData.email.toLowerCase(),
        role: userData.role || 'user',
        createdAt: now,
        updatedAt: now,
        avatarUrl: userData.avatarUrl || undefined,
        phoneNumber: userData.phoneNumber || undefined,
        addresses: userData.addresses || [],
        emailVerified: userData.emailVerified || false,
        lastLogin: undefined
      };

      this.users.push(newUser);
      this.saveToLocalStorage(this.userKey, this.users);
      return { ...newUser };
    } catch (error) {
      console.error('[MockDB_Client] Error creating user:', error);
      return null;
    }
  }

  async authenticateUser(email: string, password: string): Promise<User | null> {
    if (IS_SERVER) {
      if (!this.db_conn) {
        console.error('[DB_SERVER] Database connection not available');
        return null;
      }

      try {
        // First retrieve the user with password hash by email
        const user = _server_getUserByEmailWithPasswordHash(email.toLowerCase());

        if (!user || !user.password_hash) {
          console.log('[DB_SERVER] User not found or missing password hash:', email);
          return null;
        }

        // Compare provided password with stored hash
        const passwordMatch = await _server_comparePassword(password, user.password_hash);
        if (!passwordMatch) {
          console.log('[DB_SERVER] Password mismatch for user:', email);
          return null;
        }

        // Update last login time
        const now = new Date().toISOString();
        const updateStmt = this.db_conn.prepare('UPDATE users SET last_login = ?, email_verified = 1 WHERE id = ?');
        updateStmt.run(now, user.id);

        // Fetch the updated user record
        const updatedUser = _server_getUserById(user.id);
        if (!updatedUser) {
          console.error('[DB_SERVER] Failed to fetch updated user after login');
          return null;
        }

        // Don't return password hash to the client
        const { password_hash, ...userWithoutHash } = updatedUser;
        return userWithoutHash as User;
      } catch (error) {
        console.error('[DB_SERVER] Error during authentication:', error);
        return null;
      }
    }

    // Client-side mock authentication
    const user = this.users.find(u => u.email.toLowerCase() === email.toLowerCase());
    if (!user) {
      console.log('[MockDB_Client] User not found in mock database:', email);
      return null;
    }

    // In client mode, we simulate a successful login by updating last login
    const now = new Date().toISOString();
    const updatedUser = { ...user, lastLogin: now, emailVerified: true };

    // Update the user in local storage
    const userIndex = this.users.findIndex(u => u.id === user.id);
    if (userIndex !== -1) {
      this.users[userIndex] = updatedUser;
      this.saveToLocalStorage(this.userKey, this.users);
    }

    return updatedUser;
  }

  async updateUser(id: string, userData: Partial<Omit<User, 'id' | 'createdAt' | 'password_hash'>>): Promise<User | null> {
    if (IS_SERVER) {
      if (!this.db_conn) return null;
      const existingUser = _server_getUserById(id);
      if (!existingUser) return null;

      const fieldsToUpdate = { ...userData, updated_at: new Date().toISOString() };
      const setClauses = Object.keys(fieldsToUpdate)
        .map(key => `${key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)} = ?`)
        .join(', ');
      const values = Object.values(fieldsToUpdate);

      if (values.length === 0) return existingUser; // No fields to update

      try {
        const stmt = this.db_conn.prepare(`UPDATE users SET ${setClauses} WHERE id = ?`);
        stmt.run(...values, id);
        const updatedUser = _server_getUserById(id);
        if (updatedUser) {
            const { password_hash, ...userWithoutHash } = updatedUser;
            return userWithoutHash as User;
        }
        return null;
      } catch (error) {
        console.error('[DB_SERVER] Error in updateUser:', error);
        return null;
      }
    }
    // Client-side mock
    const userIndex = this.users.findIndex(u => u.id === id);
    if (userIndex === -1) return null;
    this.users[userIndex] = { ...this.users[userIndex], ...userData, updatedAt: new Date().toISOString() } as User;
    this.saveToLocalStorage(this.userKey, this.users);
    return { ...this.users[userIndex] };
  }

  // --- Product Methods ---
  async getProducts(): Promise<Product[]> {
    if (IS_SERVER) {
      return _server_getProducts();
    }
    return [...this.products];
  }

  async createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt' | 'reviews' | 'rating' | 'reviewCount'>): Promise<Product | null> {
    if (IS_SERVER) {
      return _server_createProduct(productData);
    }
    // Client-side mock
    const newId = uuidv4();
    const now = new Date().toISOString();
    const newProduct: Product = {
      ...productData,
      id: newId,
      createdAt: now,
      reviews: [], rating: 0, reviewCount: 0, // Default values
    } as Product;
    this.products.push(newProduct);
    this.saveToLocalStorage(this.productKey, this.products);
    return { ...newProduct };
  }

  // ... (Other methods like getProductById, updateProduct, deleteProduct should follow similar pattern)
  async getProductById(id: string): Promise<Product | null> {
    if (IS_SERVER) {
      if (!this.db_conn) return null;
      try {
        const stmt = this.db_conn.prepare('SELECT * FROM products WHERE id = ?');
        const row = stmt.get(id);
        return row ? mapProductFromDbRow(row) : null;
      } catch (error) {
        console.error('[DB_SERVER] Error in getProductById:', error);
        return null;
      }
    }
    const product = this.products.find(p => p.id === id);
    return product ? { ...product } : null;
  }

  // Example for reset (useful for testing or initial setup)
  async resetLocalUsers(): Promise<void> {
    if (IS_SERVER) {
        console.warn("[DB_SERVER] resetLocalUsers called on server. This will clear the users table.");
        if (!this.db_conn) return;
        try {
            this.db_conn.exec('DELETE FROM users');
            console.log("[DB_SERVER] All users deleted from database.");
        } catch (error) {
            console.error("[DB_SERVER] Error deleting users from database:", error);
        }
        return;
    }
    this.users = [];
    this.saveToLocalStorage(this.userKey, this.users);
    this._initializeDefaultLocalData(); // Re-initialize with defaults if needed
    console.log('[MockDB_Client] Local users reset and defaults re-initialized.');
  }

  // Service Booking Methods
  getServiceBookings(): any[] {
    if (IS_SERVER) {
      if (!this.db_conn) return [];
      try {
        const stmt = this.db_conn.prepare('SELECT * FROM service_bookings ORDER BY created_at DESC');
        return stmt.all();
      } catch (error) {
        console.error('[DB_SERVER] Error getting service bookings:', error);
        return [];
      }
    }
    // Client-side localStorage fallback
    return this.loadFromLocalStorage('service_bookings', []);
  }

  saveServiceBookings(bookings: any[]): void {
    if (IS_SERVER) {
      if (!this.db_conn) return;
      try {
        // Clear existing bookings
        this.db_conn.prepare('DELETE FROM service_bookings').run();

        // Insert all bookings
        const stmt = this.db_conn.prepare(`
          INSERT INTO service_bookings (
            id, service_name, customer_name, customer_email, customer_phone,
            company_name, service_date, preferred_time, urgency, message,
            status, notes, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        for (const booking of bookings) {
          stmt.run(
            booking.id, booking.serviceName, booking.customerName, booking.customerEmail,
            booking.customerPhone, booking.companyName, booking.serviceDate,
            booking.preferredTime, booking.urgency, booking.message,
            booking.status, booking.notes, booking.createdAt, booking.updatedAt
          );
        }
      } catch (error) {
        console.error('[DB_SERVER] Error saving service bookings:', error);
      }
    } else {
      // Client-side localStorage
      this.saveToLocalStorage('service_bookings', bookings);
    }
  }

  saveServiceBooking(booking: any): void {
    if (IS_SERVER) {
      if (!this.db_conn) return;
      try {
        const stmt = this.db_conn.prepare(`
          INSERT INTO service_bookings (
            id, service_name, customer_name, customer_email, customer_phone,
            company_name, service_date, preferred_time, urgency, message,
            status, notes, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        stmt.run(
          booking.id, booking.serviceName, booking.customerName, booking.customerEmail,
          booking.customerPhone, booking.companyName, booking.serviceDate,
          booking.preferredTime, booking.urgency, booking.message,
          booking.status, booking.notes, booking.createdAt, booking.updatedAt
        );
      } catch (error) {
        console.error('[DB_SERVER] Error saving service booking:', error);
      }
    } else {
      // Client-side localStorage
      const bookings = this.getServiceBookings();
      bookings.push(booking);
      this.saveToLocalStorage('service_bookings', bookings);
    }
  }

  updateServiceBookingStatus(bookingId: string, status: string, notes?: string): void {
    if (IS_SERVER) {
      if (!this.db_conn) return;
      try {
        const stmt = this.db_conn.prepare(`
          UPDATE service_bookings
          SET status = ?, notes = ?, updated_at = ?
          WHERE id = ?
        `);
        stmt.run(status, notes || '', new Date().toISOString(), bookingId);
      } catch (error) {
        console.error('[DB_SERVER] Error updating service booking status:', error);
      }
    } else {
      // Client-side localStorage
      const bookings = this.getServiceBookings();
      const bookingIndex = bookings.findIndex(b => b.id === bookingId);
      if (bookingIndex !== -1) {
        bookings[bookingIndex] = {
          ...bookings[bookingIndex],
          status,
          notes: notes || bookings[bookingIndex].notes,
          updatedAt: new Date().toISOString()
        };
        this.saveToLocalStorage('service_bookings', bookings);
      }
    }
  }

  // Add similar methods for services, productionLines, blogPosts as needed
}

// Export a single instance of the database client
// The 'db' variable from the server scope is passed here.
// On the client, 'db' will be null, and MockSQLiteDatabase will use localStorage.
export const sqlite = new MockSQLiteDatabase(db);
