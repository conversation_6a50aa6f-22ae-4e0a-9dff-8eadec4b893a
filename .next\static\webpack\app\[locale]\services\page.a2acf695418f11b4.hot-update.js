"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/services/page",{

/***/ "(app-pages-browser)/./src/pages/services/ServicesPage.tsx":
/*!*********************************************!*\
  !*** ./src/pages/services/ServicesPage.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServicesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_forms_ServiceBookingForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/forms/ServiceBookingForm */ \"(app-pages-browser)/./src/components/forms/ServiceBookingForm.tsx\");\n/* harmony import */ var _data_services__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../data/services */ \"(app-pages-browser)/./src/data/services.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../stores/themeStore */ \"(app-pages-browser)/./src/stores/themeStore.ts\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _translations__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../translations */ \"(app-pages-browser)/./src/translations/index.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_animations__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../components/ui/animations */ \"(app-pages-browser)/./src/components/ui/animations/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst icons = {\n    Search: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    Package: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    Truck: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    FileCheck: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    Users: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    ClipboardList: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n};\nfunction ServicesPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [showBookingForm, setShowBookingForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedService, setSelectedService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        searchQuery: '',\n        category: 'all',\n        sortBy: 'name'\n    });\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_8__.useThemeStore)();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_9__.useLanguageStore)();\n    const { t, locale } = (0,_translations__WEBPACK_IMPORTED_MODULE_10__.useTranslation)();\n    // استخدام اللغة من المسار أو من المتجر\n    const currentLanguage = locale || language;\n    // Service categories for filtering\n    const categories = [\n        {\n            id: 'all',\n            name: currentLanguage === 'ar' ? 'جميع الخدمات' : 'All Services'\n        },\n        {\n            id: 'inspection',\n            name: currentLanguage === 'ar' ? 'خدمات الفحص' : 'Inspection Services'\n        },\n        {\n            id: 'logistics',\n            name: currentLanguage === 'ar' ? 'الخدمات اللوجستية' : 'Logistics Services'\n        },\n        {\n            id: 'consulting',\n            name: currentLanguage === 'ar' ? 'الاستشارات' : 'Consulting Services'\n        },\n        {\n            id: 'certification',\n            name: currentLanguage === 'ar' ? 'الشهادات' : 'Certification Services'\n        }\n    ];\n    // Filter and sort services\n    const filteredServices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ServicesPage.useMemo[filteredServices]\": ()=>{\n            let filtered = _data_services__WEBPACK_IMPORTED_MODULE_7__.services.filter({\n                \"ServicesPage.useMemo[filteredServices].filtered\": (service)=>{\n                    const searchTerm = filters.searchQuery.toLowerCase();\n                    const serviceName = currentLanguage === 'ar' ? service.name_ar || service.name : service.name;\n                    const serviceDesc = currentLanguage === 'ar' ? service.description_ar || service.description : service.description;\n                    const matchesSearch = !searchTerm || serviceName.toLowerCase().includes(searchTerm) || serviceDesc.toLowerCase().includes(searchTerm);\n                    const matchesCategory = filters.category === 'all' || service.id.includes(filters.category) || filters.category === 'logistics' && [\n                        'shipping',\n                        'storage'\n                    ].includes(service.id) || filters.category === 'inspection' && service.id === 'inspection' || filters.category === 'consulting' && [\n                        'consulting',\n                        'order-management'\n                    ].includes(service.id) || filters.category === 'certification' && service.id === 'certification';\n                    return matchesSearch && matchesCategory;\n                }\n            }[\"ServicesPage.useMemo[filteredServices].filtered\"]);\n            // Sort services\n            filtered.sort({\n                \"ServicesPage.useMemo[filteredServices]\": (a, b)=>{\n                    const aName = currentLanguage === 'ar' ? a.name_ar || a.name : a.name;\n                    const bName = currentLanguage === 'ar' ? b.name_ar || b.name : b.name;\n                    switch(filters.sortBy){\n                        case 'name':\n                            return aName.localeCompare(bName);\n                        case 'popularity':\n                            // Mock popularity sorting - in real app, this would be based on actual data\n                            return Math.random() - 0.5;\n                        case 'recent':\n                            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                        default:\n                            return 0;\n                    }\n                }\n            }[\"ServicesPage.useMemo[filteredServices]\"]);\n            return filtered;\n        }\n    }[\"ServicesPage.useMemo[filteredServices]\"], [\n        _data_services__WEBPACK_IMPORTED_MODULE_7__.services,\n        filters,\n        currentLanguage\n    ]);\n    const handleBookService = (serviceName)=>{\n        setSelectedService(serviceName);\n        setShowBookingForm(true);\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            searchQuery: '',\n            category: 'all',\n            sortBy: 'name'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"relative py-16 overflow-hidden transition-colors duration-500\", isDarkMode ? \"bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900\" : \"bg-gradient-to-br from-slate-50 via-white to-slate-100\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"absolute inset-0 opacity-5 transition-opacity duration-500\", isDarkMode ? \"opacity-10\" : \"opacity-5\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:50px_50px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-4xl md:text-5xl font-bold leading-tight tracking-tight mb-6\", isDarkMode ? \"text-white\" : \"text-slate-900\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block\",\n                                                children: currentLanguage === 'ar' ? 'خدمات' : 'Business'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"block bg-gradient-to-r bg-clip-text text-transparent\", isDarkMode ? \"from-primary-400 to-blue-400\" : \"from-primary-600 to-blue-600\"),\n                                                children: currentLanguage === 'ar' ? 'الأعمال' : 'Services'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-lg md:text-xl leading-relaxed max-w-3xl mx-auto mb-8\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\"),\n                                        children: currentLanguage === 'ar' ? 'خدمات دعم شاملة لتبسيط عملياتك وتعزيز كفاءة أعمالك مع حلول متطورة ومخصصة.' : 'Comprehensive support services to streamline your operations and enhance business efficiency with advanced, tailored solutions.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollStagger, {\n                                            animation: \"slide\",\n                                            direction: \"up\",\n                                            staggerDelay: 0.1,\n                                            children: [\n                                                {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    value: '6+',\n                                                    label: currentLanguage === 'ar' ? 'خدمة متخصصة' : 'Expert Services'\n                                                },\n                                                {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    value: '500+',\n                                                    label: currentLanguage === 'ar' ? 'عميل راضي' : 'Satisfied Clients'\n                                                },\n                                                {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    value: '24/48h',\n                                                    label: currentLanguage === 'ar' ? 'وقت الاستجابة' : 'Response Time'\n                                                },\n                                                {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    value: '50+',\n                                                    label: currentLanguage === 'ar' ? 'دولة' : 'Countries'\n                                                }\n                                            ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"p-4 rounded-lg transition-colors duration-300\", isDarkMode ? \"bg-slate-800/50 hover:bg-slate-800/70\" : \"bg-white/50 hover:bg-white/70\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"flex items-center justify-center w-8 h-8 rounded-full mx-auto mb-2\", isDarkMode ? \"bg-primary-500/20 text-primary-400\" : \"bg-primary-100 text-primary-600\"),\n                                                            children: stat.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-lg font-bold mb-1\", isDarkMode ? \"text-white\" : \"text-slate-900\"),\n                                                            children: stat.value\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-xs font-medium\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\"),\n                                                            children: stat.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"primary\",\n                                                    className: \"px-8 py-3 text-lg font-semibold group\",\n                                                    onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/contact\")),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5 group-hover:scale-110 transition-transform\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'استشارة مجانية' : 'Free Consultation'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"outline\",\n                                                    className: \"px-8 py-3 text-lg font-semibold group\",\n                                                    onClick: ()=>{\n                                                        var _document_getElementById;\n                                                        return (_document_getElementById = document.getElementById('services-grid')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                            behavior: 'smooth'\n                                                        });\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2', \" h-5 w-5 group-hover:translate-x-1 transition-transform\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'استكشف الخدمات' : 'Explore Services'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"py-12\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-50\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollAnimation, {\n                        animation: \"fade\",\n                        delay: 0.2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"absolute top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400\", currentLanguage === 'ar' ? \"right-4\" : \"left-4\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            type: \"text\",\n                                            placeholder: currentLanguage === 'ar' ? 'ابحث عن الخدمات...' : 'Search services...',\n                                            value: filters.searchQuery,\n                                            onChange: (e)=>setFilters((prev)=>({\n                                                        ...prev,\n                                                        searchQuery: e.target.value\n                                                    })),\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"w-full py-4 text-lg rounded-xl border-2 transition-all duration-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500\", currentLanguage === 'ar' ? \"pr-12 pl-4\" : \"pl-12 pr-4\", isDarkMode ? \"bg-slate-700 border-slate-600 text-white placeholder-slate-400\" : \"bg-white border-slate-200 text-slate-900 placeholder-slate-500\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this),\n                                        filters.searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setFilters((prev)=>({\n                                                        ...prev,\n                                                        searchQuery: ''\n                                                    })),\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"absolute top-1/2 transform -translate-y-1/2 p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors\", currentLanguage === 'ar' ? \"left-2\" : \"right-2\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-4 w-4 text-slate-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"block text-sm font-medium mb-2\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\"),\n                                                    children: currentLanguage === 'ar' ? 'فئة الخدمة' : 'Service Category'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filters.category,\n                                                    onChange: (e)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                category: e.target.value\n                                                            })),\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"w-full p-3 rounded-lg border transition-colors duration-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500\", isDarkMode ? \"bg-slate-700 border-slate-600 text-white\" : \"bg-white border-slate-200 text-slate-900\"),\n                                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category.id,\n                                                            children: category.name\n                                                        }, category.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"block text-sm font-medium mb-2\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\"),\n                                                    children: currentLanguage === 'ar' ? 'ترتيب حسب' : 'Sort By'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filters.sortBy,\n                                                    onChange: (e)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                sortBy: e.target.value\n                                                            })),\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"w-full p-3 rounded-lg border transition-colors duration-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500\", isDarkMode ? \"bg-slate-700 border-slate-600 text-white\" : \"bg-white border-slate-200 text-slate-900\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"name\",\n                                                            children: currentLanguage === 'ar' ? 'الاسم' : 'Name'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"popularity\",\n                                                            children: currentLanguage === 'ar' ? 'الشعبية' : 'Popularity'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"recent\",\n                                                            children: currentLanguage === 'ar' ? 'الأحدث' : 'Most Recent'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this),\n                                        (filters.searchQuery || filters.category !== 'all' || filters.sortBy !== 'name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: clearFilters,\n                                                className: \"px-6 py-3 h-fit\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    currentLanguage === 'ar' ? 'مسح الفلاتر' : 'Clear Filters'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-sm mb-6 p-3 rounded-lg\", isDarkMode ? \"bg-slate-700 text-slate-300\" : \"bg-white text-slate-600\"),\n                                    children: [\n                                        currentLanguage === 'ar' ? \"عرض \".concat(filteredServices.length, \" من \").concat(_data_services__WEBPACK_IMPORTED_MODULE_7__.services.length, \" خدمة\") : \"Showing \".concat(filteredServices.length, \" of \").concat(_data_services__WEBPACK_IMPORTED_MODULE_7__.services.length, \" services\"),\n                                        filters.searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: currentLanguage === 'ar' ? 'للبحث \"'.concat(filters.searchQuery, '\"') : 'for \"'.concat(filters.searchQuery, '\"')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"services-grid\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"py-16 md:py-24\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-3xl mx-auto text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\",\n                                        children: currentLanguage === 'ar' ? 'عروض خدماتنا' : 'Our Service Offerings'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-slate-600 dark:text-slate-300\",\n                                        children: currentLanguage === 'ar' ? 'اكتشف مجموعة كاملة من خدمات الأعمال المصممة لدعم عملياتك في كل مرحلة.' : 'Discover the full range of business services designed to support your operations at every stage.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this),\n                        filteredServices.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollStagger, {\n                            animation: \"slide\",\n                            direction: \"up\",\n                            staggerDelay: 0.1,\n                            delay: 0.4,\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: filteredServices.map((service)=>{\n                                const Icon = icons[service.icon];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.HoverAnimation, {\n                                    animation: \"lift\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"group hover:shadow-xl transition-all duration-300 h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"text-center pb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"p-4 rounded-full transition-all duration-300 group-hover:scale-110\", isDarkMode ? \"bg-primary-500/20\" : \"bg-primary-50\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                size: 32,\n                                                                className: \"text-primary-500 dark:text-primary-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-xl mb-2 text-slate-900 dark:text-white\",\n                                                        children: currentLanguage === 'ar' ? service.name_ar || service.name : service.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"flex flex-col h-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600 dark:text-slate-300 mb-6 flex-grow\",\n                                                        children: currentLanguage === 'ar' ? service.description_ar || service.description : service.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: [\n                                                                (currentLanguage === 'ar' ? service.features_ar || service.features : service.features).slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-xs px-2 py-1 rounded-full\", isDarkMode ? \"bg-slate-700 text-slate-300\" : \"bg-slate-100 text-slate-600\"),\n                                                                        children: feature\n                                                                    }, index, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 33\n                                                                    }, this)),\n                                                                service.features.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"text-xs px-2 py-1 rounded-full\", isDarkMode ? \"bg-primary-500/20 text-primary-400\" : \"bg-primary-50 text-primary-600\"),\n                                                                    children: [\n                                                                        \"+\",\n                                                                        service.features.length - 3,\n                                                                        \" \",\n                                                                        currentLanguage === 'ar' ? 'المزيد' : 'more'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 mt-auto\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: ()=>handleBookService(currentLanguage === 'ar' ? service.name_ar || service.name : service.name),\n                                                                    className: \"flex-1 group\",\n                                                                    variant: \"primary\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2 group-hover:scale-110 transition-transform\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        currentLanguage === 'ar' ? 'احجز الخدمة' : 'Book Service'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/services/\").concat(service.slug)),\n                                                                    variant: \"outline\",\n                                                                    className: \"flex-1 group\",\n                                                                    children: [\n                                                                        currentLanguage === 'ar' ? 'معرفة المزيد' : 'Learn More',\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"\".concat(currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2', \" h-4 w-4 transition-transform group-hover:translate-x-1\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 21\n                                    }, this)\n                                }, service.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-6\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-100\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-12 w-12 text-slate-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-slate-900 dark:text-white mb-2\",\n                                        children: currentLanguage === 'ar' ? 'لم يتم العثور على خدمات' : 'No Services Found'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600 dark:text-slate-300 mb-6\",\n                                        children: currentLanguage === 'ar' ? 'جرب تعديل معايير البحث أو الفلاتر للعثور على ما تبحث عنه.' : 'Try adjusting your search criteria or filters to find what you\\'re looking for.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: clearFilters,\n                                        variant: \"outline\",\n                                        children: currentLanguage === 'ar' ? 'مسح جميع الفلاتر' : 'Clear All Filters'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"py-16 md:py-24\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-50\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-3xl mx-auto text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\",\n                                        children: currentLanguage === 'ar' ? 'كيف نعمل' : 'How We Work'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-slate-600 dark:text-slate-300\",\n                                        children: currentLanguage === 'ar' ? 'نهجنا الاستشاري يضمن حلولاً مخصصة لاحتياجات عملك الفريدة.' : 'Our consultative approach ensures tailored solutions for your unique business needs.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollStagger, {\n                            animation: \"slide\",\n                            direction: \"right\",\n                            staggerDelay: 0.15,\n                            delay: 0.4,\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                {\n                                    number: \"01\",\n                                    title: currentLanguage === 'ar' ? \"الاستشارة\" : \"Consultation\",\n                                    description: currentLanguage === 'ar' ? \"نبدأ باستشارة شاملة لفهم احتياجات وتحديات عملك.\" : \"We begin with a thorough consultation to understand your business needs and challenges.\"\n                                },\n                                {\n                                    number: \"02\",\n                                    title: currentLanguage === 'ar' ? \"التحليل\" : \"Analysis\",\n                                    description: currentLanguage === 'ar' ? \"يقوم خبراؤنا بتحليل متطلباتك وتطوير توصيات خدمة مخصصة.\" : \"Our experts analyze your requirements and develop customized service recommendations.\"\n                                },\n                                {\n                                    number: \"03\",\n                                    title: currentLanguage === 'ar' ? \"التنفيذ\" : \"Implementation\",\n                                    description: currentLanguage === 'ar' ? \"نقوم بتنفيذ الخدمات المتفق عليها مع الاهتمام بالتفاصيل وضمان الجودة.\" : \"We implement the agreed services with attention to detail and quality assurance.\"\n                                },\n                                {\n                                    number: \"04\",\n                                    title: currentLanguage === 'ar' ? \"الدعم المستمر\" : \"Ongoing Support\",\n                                    description: currentLanguage === 'ar' ? \"المراقبة والدعم المستمر يضمنان النتائج المثلى والقدرة على التكيف.\" : \"Continuous monitoring and support ensure optimal results and adaptability.\"\n                                }\n                            ].map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.HoverAnimation, {\n                                    animation: \"lift\",\n                                    className: \"relative text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-500 text-white rounded-full h-12 w-12 flex items-center justify-center font-bold text-lg mb-4 mx-auto\",\n                                            children: step.number\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"p-6 rounded-lg shadow-sm h-full\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-2 text-slate-900 dark:text-white\",\n                                                    children: step.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 dark:text-slate-300\",\n                                                    children: step.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 537,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                    lineNumber: 523,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 522,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 md:py-24 bg-primary-500 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollAnimation, {\n                        animation: \"fade\",\n                        delay: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-3xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-6\",\n                                    children: currentLanguage === 'ar' ? 'هل أنت مستعد لتعزيز عمليات عملك؟' : 'Ready to Enhance Your Business Operations?'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl mb-8 text-primary-50\",\n                                    children: currentLanguage === 'ar' ? 'اتصل بفريقنا لمناقشة كيف يمكن لخدماتنا تلبية احتياجات عملك المحددة.' : 'Contact our team to discuss how our services can address your specific business needs.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.HoverAnimation, {\n                                        animation: \"scale\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/services/request\")),\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"bg-transparent border-white text-white hover:bg-primary-600 px-8\",\n                                            children: currentLanguage === 'ar' ? 'طلب عرض سعر للخدمة' : 'Request Service Quote'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                    lineNumber: 590,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 589,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"py-16 md:py-24\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-3xl mx-auto text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\",\n                                        children: currentLanguage === 'ar' ? 'الأسئلة الشائعة' : 'Frequently Asked Questions'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-slate-600 dark:text-slate-300\",\n                                        children: currentLanguage === 'ar' ? 'ابحث عن إجابات للأسئلة الشائعة حول خدمات أعمالنا.' : 'Find answers to common questions about our business services.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 621,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollStagger, {\n                            animation: \"fade\",\n                            staggerDelay: 0.1,\n                            delay: 0.4,\n                            className: \"max-w-4xl mx-auto space-y-6\",\n                            children: [\n                                {\n                                    question: currentLanguage === 'ar' ? \"ما هي أنواع الشركات التي تخدمونها؟\" : \"What types of businesses do you serve?\",\n                                    answer: currentLanguage === 'ar' ? \"نخدم مجموعة واسعة من الشركات عبر مختلف الصناعات، بما في ذلك التصنيع والتجزئة والتوزيع والتجارة الإلكترونية. خدماتنا قابلة للتطوير ويمكن تخصيصها لتلبية احتياجات كل من الشركات الصغيرة والمؤسسات الكبيرة.\" : \"We serve a wide range of businesses across various industries, including manufacturing, retail, distribution, and e-commerce. Our services are scalable and can be customized to meet the needs of both small businesses and large enterprises.\"\n                                },\n                                {\n                                    question: currentLanguage === 'ar' ? \"ما هي سرعة ترتيب خدمات الفحص؟\" : \"How quickly can you arrange inspection services?\",\n                                    answer: currentLanguage === 'ar' ? \"يمكن ترتيب خدمات الفحص القياسية في غضون 48-72 ساعة من الطلب. للاحتياجات العاجلة، نقدم خدمات معجلة يمكن جدولتها في غضون 24 ساعة في معظم المواقع، حسب التوفر. تضمن شبكتنا العالمية من المفتشين أوقات استجابة سريعة.\" : \"Standard inspection services can be arranged within 48-72 hours of request. For urgent needs, we offer expedited services that can be scheduled within 24 hours in most locations, subject to availability. Our global network of inspectors ensures quick response times.\"\n                                },\n                                {\n                                    question: currentLanguage === 'ar' ? \"هل تقدمون خدمات دولية؟\" : \"Do you provide services internationally?\",\n                                    answer: currentLanguage === 'ar' ? \"نعم، نقدم خدمات أعمالنا عالميًا من خلال شبكتنا الواسعة من المكاتب والشركاء في مراكز التصنيع والخدمات اللوجستية الرئيسية عبر آسيا وأوروبا والأمريكتين. يمكننا تنسيق الخدمات عبر العديد من البلدان والمناطق.\" : \"Yes, we offer our business services globally through our extensive network of offices and partners in major manufacturing and logistics hubs across Asia, Europe, and the Americas. We can coordinate services across multiple countries and regions.\"\n                                },\n                                {\n                                    question: \"What certifications and standards do you comply with?\",\n                                    answer: \"Our services comply with international standards including ISO 9001, ISO 14001, and industry-specific certifications. For product certification services, we work with all major certification bodies and can assist with CE, FCC, UL, and other market-specific requirements.\"\n                                },\n                                {\n                                    question: \"How do you ensure service quality and consistency?\",\n                                    answer: \"We maintain strict quality control processes, including regular training for our staff, standardized operating procedures, and continuous monitoring of service delivery. Our quality management system ensures consistent service delivery across all locations.\"\n                                },\n                                {\n                                    question: \"What are your payment terms and methods?\",\n                                    answer: \"We accept various payment methods including bank transfers, credit cards, and digital payments. For regular clients, we offer flexible payment terms including net-30 accounts. Volume-based discounts and service packages are available for long-term contracts.\"\n                                },\n                                {\n                                    question: \"Can you handle rush orders or emergency situations?\",\n                                    answer: \"Yes, we have dedicated teams to handle urgent requests and emergency situations. We offer expedited services across all our service lines with priority handling and 24/7 support for critical situations.\"\n                                },\n                                {\n                                    question: \"Do you provide customized service packages?\",\n                                    answer: \"Yes, we can create customized service packages that combine multiple services to meet your specific business needs. Our solutions architects will work with you to design the most efficient and cost-effective service package.\"\n                                },\n                                {\n                                    question: \"What kind of reporting and documentation do you provide?\",\n                                    answer: \"We provide detailed digital reports for all services, including inspection findings, certification status, shipping documentation, and performance analytics. Our clients have access to a secure portal for real-time tracking and report downloads.\"\n                                },\n                                {\n                                    question: \"How do you handle confidential information?\",\n                                    answer: \"We maintain strict confidentiality protocols and are compliant with GDPR and other data protection regulations. All client information is stored securely, and our staff signs comprehensive NDAs.\"\n                                }\n                            ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.HoverAnimation, {\n                                    animation: \"lift\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"group rounded-lg overflow-hidden\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"flex items-center justify-between p-6 cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-medium text-slate-900 dark:text-white pr-8\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"flex-shrink-0 ml-1.5 p-1.5 rounded-full\", isDarkMode ? \"text-slate-300 bg-slate-700\" : \"text-slate-700 bg-white\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5 transform group-open:rotate-180 transition-transform duration-200\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: \"2\",\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                lineNumber: 702,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 pb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 dark:text-slate-300\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 634,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                    lineNumber: 620,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 619,\n                columnNumber: 7\n            }, this),\n            showBookingForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_12__.ScrollAnimation, {\n                    animation: \"scale\",\n                    duration: 0.3,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-2xl w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_ServiceBookingForm__WEBPACK_IMPORTED_MODULE_6__.ServiceBookingForm, {\n                            serviceName: selectedService,\n                            onClose: ()=>{\n                                setShowBookingForm(false);\n                                setSelectedService(undefined);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                    lineNumber: 724,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 723,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(ServicesPage, \"X3wwz2DGKpEpblgQnkfiwH7CVSg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _stores_themeStore__WEBPACK_IMPORTED_MODULE_8__.useThemeStore,\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_9__.useLanguageStore,\n        _translations__WEBPACK_IMPORTED_MODULE_10__.useTranslation\n    ];\n});\n_c = ServicesPage;\nvar _c;\n$RefreshReg$(_c, \"ServicesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/pages/services/ServicesPage.tsx\n"));

/***/ })

});