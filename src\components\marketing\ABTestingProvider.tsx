'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// تعريف أنواع التجارب
export type ExperimentName = 'productLayout' | 'ctaColor' | 'heroImage' | 'checkoutProcess';
export type VariantName = 'A' | 'B';

// تعريف نوع سياق التجارب
interface ABTestingContextType {
  getVariant: (experimentName: ExperimentName) => VariantName;
  trackConversion: (experimentName: ExperimentName) => void;
  experiments: Record<ExperimentName, VariantName>;
}

// إنشاء سياق التجارب
const ABTestingContext = createContext<ABTestingContextType | undefined>(undefined);

// خصائص مزود التجارب
interface ABTestingProviderProps {
  children: ReactNode;
}

export function ABTestingProvider({ children }: ABTestingProviderProps) {
  // حالة التجارب
  const [experiments, setExperiments] = useState<Record<ExperimentName, VariantName>>({
    productLayout: 'A',
    ctaColor: 'A',
    heroImage: 'A',
    checkoutProcess: 'A'
  });

  // تهيئة التجارب عند تحميل الصفحة
  useEffect(() => {
    // التحقق من وجود تجارب محفوظة في localStorage
    const savedExperiments = localStorage.getItem('ab_testing_experiments');
    
    if (savedExperiments) {
      try {
        const parsedExperiments = JSON.parse(savedExperiments);
        setExperiments(prev => ({ ...prev, ...parsedExperiments }));
      } catch (error) {
        console.error('Error parsing saved experiments:', error);
      }
    } else {
      // إنشاء تجارب جديدة بشكل عشوائي
      const newExperiments: Record<ExperimentName, VariantName> = {
        productLayout: Math.random() < 0.5 ? 'A' : 'B',
        ctaColor: Math.random() < 0.5 ? 'A' : 'B',
        heroImage: Math.random() < 0.5 ? 'A' : 'B',
        checkoutProcess: Math.random() < 0.5 ? 'A' : 'B'
      };
      
      setExperiments(newExperiments);
      localStorage.setItem('ab_testing_experiments', JSON.stringify(newExperiments));
    }
  }, []);

  // الحصول على متغير التجربة
  const getVariant = (experimentName: ExperimentName): VariantName => {
    return experiments[experimentName] || 'A';
  };

  // تتبع التحويل
  const trackConversion = (experimentName: ExperimentName) => {
    // هنا يمكن إضافة رمز لإرسال بيانات التحويل إلى خدمة تحليلات
    console.log(`Conversion tracked for experiment: ${experimentName}, variant: ${experiments[experimentName]}`);
    
    // يمكن استخدام Google Analytics أو أي خدمة تحليلات أخرى
    if (typeof window !== 'undefined' && 'gtag' in window) {
      const gtag = (window as any).gtag;
      gtag('event', 'ab_test_conversion', {
        'experiment_name': experimentName,
        'variant': experiments[experimentName]
      });
    }
  };

  // قيمة سياق التجارب
  const contextValue: ABTestingContextType = {
    getVariant,
    trackConversion,
    experiments
  };

  return (
    <ABTestingContext.Provider value={contextValue}>
      {children}
    </ABTestingContext.Provider>
  );
}

// Hook لاستخدام سياق التجارب
export function useABTesting() {
  const context = useContext(ABTestingContext);
  
  if (context === undefined) {
    throw new Error('useABTesting must be used within an ABTestingProvider');
  }
  
  return context;
}
