'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Search, Tag, ArrowRight } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';
import { productCategories } from '../../data/products';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../ui/animations';

interface ShopHeaderProps {
  onSearch: (query: string) => void;
  onCategorySelect: (category: string) => void;
  searchQuery: string;
  selectedCategory: string;
}

export function ShopHeader({
  onSearch,
  onCategorySelect,
  searchQuery,
  selectedCategory
}: ShopHeaderProps) {
  const router = useRouter();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // تحديث البحث المحلي عند تغيير البحث الخارجي
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  // معالجة البحث
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(localSearchQuery);
  };

  return (
    <div className="mb-6">

      {/* فئات المنتجات */}
      <ScrollAnimation animation="fade" delay={0.2}>
        <div className="mb-8 bg-gradient-to-br from-white via-slate-50 to-white dark:from-slate-800 dark:via-slate-700 dark:to-slate-800 rounded-2xl shadow-xl p-8 border border-slate-200/50 dark:border-slate-600/50">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-1">
                {currentLanguage === 'ar' ? 'تصفح حسب الفئة' : 'Browse by Category'}
              </h2>
              <p className="text-slate-600 dark:text-slate-400 text-sm">
                {currentLanguage === 'ar' ? 'اختر من مجموعة واسعة من الفئات' : 'Choose from a wide range of categories'}
              </p>
            </div>
            <Link href={`/${currentLanguage}/shop/categories`} className="text-primary-600 dark:text-primary-400 flex items-center text-sm hover:underline font-medium bg-primary-50 dark:bg-primary-900/30 px-3 py-2 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors">
              {currentLanguage === 'ar' ? 'عرض جميع الفئات' : 'View all categories'}
              <ArrowRight className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} />
            </Link>
          </div>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.05}
            className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6"
          >
            {productCategories.map((category) => (
              <HoverAnimation key={category.id} animation="lift">
                <button
                  onClick={() => onCategorySelect(category.id)}
                  className={cn(
                    "relative h-28 rounded-2xl overflow-hidden group",
                    "transition-all duration-500 shadow-lg hover:shadow-2xl hover:shadow-primary-500/20",
                    "hover:-translate-y-2 hover:scale-105",
                    "border-2 border-transparent",
                    selectedCategory === category.id
                      ? "ring-4 ring-primary-500/50 dark:ring-primary-400/50 border-primary-500 dark:border-primary-400"
                      : "hover:border-primary-300 dark:hover:border-primary-600"
                  )}
                >
                  {/* صورة الفئة */}
                  <div
                    className="absolute inset-0 bg-cover bg-center transition-transform duration-700 group-hover:scale-110 group-hover:rotate-1"
                    style={{ backgroundImage: `url(${category.image})` }}
                  ></div>

                  {/* طبقة التراكب المحسنة */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20 group-hover:from-black/90 group-hover:via-black/50 transition-all duration-500"></div>

                  {/* تأثير الضوء */}
                  <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  {/* اسم الفئة */}
                  <div className="absolute inset-0 flex items-center justify-center p-3 z-10">
                    <span className="text-white text-center font-bold text-sm leading-tight drop-shadow-lg group-hover:scale-110 transition-transform duration-300">
                      {currentLanguage === 'ar' ? category.name.ar : category.name.en}
                    </span>
                  </div>

                  {/* أيقونة التحديد */}
                  {selectedCategory === category.id && (
                    <div className="absolute top-3 right-3 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center shadow-lg animate-pulse z-20">
                      <Tag className="w-3 h-3 text-white" />
                    </div>
                  )}

                  {/* مؤشر الحالة */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
                </button>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </ScrollAnimation>
    </div>
  );
}
