'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Search, Tag, ArrowRight } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';
import { productCategories } from '../../data/products';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../ui/animations';

interface ShopHeaderProps {
  onSearch: (query: string) => void;
  onCategorySelect: (category: string) => void;
  searchQuery: string;
  selectedCategory: string;
}

export function ShopHeader({
  onSearch,
  onCategorySelect,
  searchQuery,
  selectedCategory
}: ShopHeaderProps) {
  const router = useRouter();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // تحديث البحث المحلي عند تغيير البحث الخارجي
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  // معالجة البحث
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(localSearchQuery);
  };

  return (
    <div className="mb-6">

      {/* فئات المنتجات */}
      <ScrollAnimation animation="fade" delay={0.2}>
        <div className="mb-6 bg-white dark:bg-slate-800 rounded-lg shadow-sm p-4 border border-slate-200 dark:border-slate-700">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-slate-900 dark:text-white">
              {currentLanguage === 'ar' ? 'تصفح حسب الفئة' : 'Browse by Category'}
            </h2>
            <Link href={`/${currentLanguage}/shop/categories`} className="text-primary-600 dark:text-primary-400 flex items-center text-sm hover:underline font-medium">
              {currentLanguage === 'ar' ? 'عرض الكل' : 'View all'}
              <ArrowRight className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} />
            </Link>
          </div>

          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-3">
            {productCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => onCategorySelect(category.id)}
                className={cn(
                  "relative h-16 rounded-lg overflow-hidden group transition-all duration-200",
                  "border border-slate-200 dark:border-slate-700 hover:border-primary-300 dark:hover:border-primary-600",
                  "hover:shadow-md",
                  selectedCategory === category.id
                    ? "ring-2 ring-primary-500 border-primary-500 dark:border-primary-400"
                    : ""
                )}
              >
                {/* صورة الفئة */}
                <div
                  className="absolute inset-0 bg-cover bg-center transition-transform duration-300 group-hover:scale-105"
                  style={{ backgroundImage: `url(${category.image})` }}
                ></div>

                {/* طبقة التراكب */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-black/20"></div>

                {/* اسم الفئة */}
                <div className="absolute inset-0 flex items-center justify-center p-2 z-10">
                  <span className="text-white text-center font-medium text-xs leading-tight">
                    {currentLanguage === 'ar' ? category.name.ar : category.name.en}
                  </span>
                </div>

                {/* أيقونة التحديد */}
                {selectedCategory === category.id && (
                  <div className="absolute top-1 right-1 w-4 h-4 bg-primary-500 rounded-full flex items-center justify-center z-20">
                    <Tag className="w-2 h-2 text-white" />
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>
      </ScrollAnimation>
    </div>
  );
}
