'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Search, Tag, ArrowRight } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';
import { productCategories } from '../../data/products';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../ui/animations';

interface ShopHeaderProps {
  onSearch: (query: string) => void;
  onCategorySelect: (category: string) => void;
  searchQuery: string;
  selectedCategory: string;
}

export function ShopHeader({
  onSearch,
  onCategorySelect,
  searchQuery,
  selectedCategory
}: ShopHeaderProps) {
  const router = useRouter();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // تحديث البحث المحلي عند تغيير البحث الخارجي
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  // معالجة البحث
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(localSearchQuery);
  };

  return (
    <div className="mb-8">
      {/* بانر المتجر */}
      <ScrollAnimation animation="fade" delay={0.1}>
        <div className={cn(
          "relative h-64 md:h-80 rounded-2xl overflow-hidden mb-8",
          "bg-gradient-to-r from-primary-600 to-secondary-600"
        )}>
          {/* طبقة التراكب */}
          <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-black/30 z-10"></div>

          {/* صورة الخلفية */}
          <div
            className="absolute inset-0 bg-cover bg-center z-0 transition-transform duration-500"
            style={{ backgroundImage: 'url(https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg)' }}
          ></div>

          {/* عناصر زخرفية */}
          <div className="absolute top-0 left-0 w-full h-full z-[5] opacity-20">
            <div className="absolute top-10 left-10 w-32 h-32 rounded-full bg-primary-500/30 blur-3xl"></div>
            <div className="absolute bottom-10 right-10 w-40 h-40 rounded-full bg-secondary-500/30 blur-3xl"></div>
          </div>

          {/* محتوى البانر */}
          <div className="relative z-20 h-full flex flex-col justify-center items-center text-white p-6 text-center">
            <div className="mb-4 flex items-center justify-center">
              <div className="w-12 h-1 bg-primary-400 rounded-full mr-3"></div>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold">
                {currentLanguage === 'ar' ? 'متجر ارتال' : 'ARTAL Shop'}
              </h1>
              <div className="w-12 h-1 bg-primary-400 rounded-full ml-3"></div>
            </div>
            <p className="text-lg md:text-xl max-w-2xl mb-6 text-white/90">
              {currentLanguage === 'ar'
                ? 'اكتشف مجموعتنا الواسعة من المنتجات عالية الجودة بأسعار تنافسية'
                : 'Discover our wide range of high-quality products at competitive prices'}
            </p>

            {/* نموذج البحث */}
            <form onSubmit={handleSearch} className="w-full max-w-md relative">
              <div className="relative">
                <Input
                  type="text"
                  placeholder={currentLanguage === 'ar' ? 'ابحث عن منتجات...' : 'Search for products...'}
                  value={localSearchQuery}
                  onChange={(e) => setLocalSearchQuery(e.target.value)}
                  className={cn(
                    "pl-10 pr-4 py-3 rounded-full shadow-lg border-0",
                    "bg-white/90 backdrop-blur-sm text-slate-900 placeholder-slate-500",
                    "focus:ring-2 focus:ring-primary-500 focus:bg-white",
                    "transition-all duration-300 hover:shadow-xl"
                  )}
                />
                <Search className={cn(
                  "absolute top-1/2 transform -translate-y-1/2 text-slate-500",
                  isRTL ? "right-3" : "left-3"
                )} />
                <Button
                  type="submit"
                  className={cn(
                    "absolute top-1/2 transform -translate-y-1/2 rounded-full",
                    isRTL ? "left-1" : "right-1",
                    "shadow-md hover:shadow-lg transition-all duration-300"
                  )}
                  size="sm"
                >
                  {currentLanguage === 'ar' ? 'بحث' : 'Search'}
                </Button>
              </div>
              <div className="mt-3 flex flex-wrap justify-center gap-2 text-xs">
                <span className="text-white/70">{currentLanguage === 'ar' ? 'بحث شائع:' : 'Popular searches:'}</span>
                {['electronics', 'furniture', 'clothing', 'accessories'].map((term) => (
                  <button
                    key={term}
                    type="button"
                    onClick={() => {
                      setLocalSearchQuery(term);
                      onSearch(term);
                    }}
                    className="px-2 py-1 bg-white/20 hover:bg-white/30 rounded-full transition-colors duration-300"
                  >
                    {term}
                  </button>
                ))}
              </div>
            </form>
          </div>
        </div>
      </ScrollAnimation>

      {/* فئات المنتجات */}
      <ScrollAnimation animation="fade" delay={0.2}>
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
              {currentLanguage === 'ar' ? 'تصفح حسب الفئة' : 'Browse by Category'}
            </h2>
            <Link href={`/${currentLanguage}/shop/categories`} className="text-primary-600 dark:text-primary-400 flex items-center text-sm hover:underline">
              {currentLanguage === 'ar' ? 'عرض جميع الفئات' : 'View all categories'}
              <ArrowRight className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} />
            </Link>
          </div>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.05}
            className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4"
          >
            {productCategories.map((category) => (
              <HoverAnimation key={category.id} animation="lift">
                <button
                  onClick={() => onCategorySelect(category.id)}
                  className={cn(
                    "relative h-24 rounded-xl overflow-hidden group",
                    "transition-all duration-300 shadow hover:shadow-md",
                    selectedCategory === category.id
                      ? "ring-2 ring-primary-500 dark:ring-primary-400"
                      : "ring-0"
                  )}
                >
                  {/* صورة الفئة */}
                  <div
                    className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                    style={{ backgroundImage: `url(${category.image})` }}
                  ></div>

                  {/* طبقة التراكب */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-black/20 group-hover:from-black/80 transition-colors duration-300"></div>

                  {/* اسم الفئة */}
                  <div className="absolute inset-0 flex items-center justify-center p-2">
                    <span className="text-white text-center font-medium text-sm">
                      {currentLanguage === 'ar' ? category.name.ar : category.name.en}
                    </span>
                  </div>

                  {/* أيقونة التحديد */}
                  {selectedCategory === category.id && (
                    <div className="absolute top-2 right-2 w-4 h-4 bg-primary-500 rounded-full flex items-center justify-center">
                      <Tag className="w-2.5 h-2.5 text-white" />
                    </div>
                  )}
                </button>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </ScrollAnimation>
    </div>
  );
}
