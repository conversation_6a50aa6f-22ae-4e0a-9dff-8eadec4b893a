"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_data_products_ts";
exports.ids = ["_ssr_src_data_products_ts"];
exports.modules = {

/***/ "(ssr)/./src/data/products.ts":
/*!******************************!*\
  !*** ./src/data/products.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   productCategories: () => (/* binding */ productCategories),\n/* harmony export */   products: () => (/* binding */ products)\n/* harmony export */ });\nvar products = [\n    {\n        id: 'smart-factory-sensor',\n        name: 'Smart Factory IoT Sensor Kit',\n        slug: 'smart-factory-sensor',\n        description: 'Next-generation IoT sensors for real-time manufacturing monitoring and predictive maintenance.',\n        price: 2999.99,\n        compareAtPrice: 3499.99,\n        images: [\n            '/images/product-automation.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'IoT',\n            'Industry 4.0'\n        ],\n        stock: 50,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 150,\n        specifications: {\n            'Sensor Type': 'Multi-parameter',\n            'Connectivity': 'WiFi, Bluetooth, LoRaWAN',\n            'Battery Life': 'Up to 2 years',\n            'Data Rate': '1 sample/second',\n            'Operating Temperature': '-20°C to 85°C'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'ai-quality-system',\n        name: 'AI-Powered Quality Inspection System',\n        slug: 'ai-quality-system',\n        description: 'Advanced computer vision system for automated quality control in production lines.',\n        price: 8999.99,\n        compareAtPrice: 10999.99,\n        images: [\n            '/images/product-packaging.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Quality Control',\n        tags: [\n            'New Arrival',\n            'AI',\n            'Automation'\n        ],\n        stock: 15,\n        featured: true,\n        rating: 4.9,\n        reviewCount: 210,\n        specifications: {\n            'Camera Resolution': '4K Ultra HD',\n            'Processing Speed': 'Up to 100 items/minute',\n            'AI Model': 'Deep Learning CNN',\n            'Integration': 'REST API, MQTT',\n            'Accuracy Rate': '99.9%'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'smart-inventory',\n        name: 'Smart Inventory Management System',\n        slug: 'smart-inventory',\n        description: 'Cloud-based inventory tracking system with real-time analytics and AI-driven forecasting.',\n        price: 4999.99,\n        compareAtPrice: 5999.99,\n        images: [\n            '/images/product-manufacturing.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Warehouse Management',\n        tags: [\n            'New Arrival',\n            'Smart Systems',\n            'Cloud'\n        ],\n        stock: 30,\n        featured: true,\n        rating: 4.6,\n        reviewCount: 95,\n        specifications: {\n            'Storage Capacity': 'Unlimited',\n            'Real-time Tracking': 'Yes',\n            'Mobile App': 'iOS & Android',\n            'Barcode Support': '1D & 2D',\n            'Analytics': 'Advanced ML-based'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'collaborative-robot',\n        name: 'Next-Gen Collaborative Robot',\n        slug: 'collaborative-robot',\n        description: 'Advanced collaborative robot with enhanced safety features and intuitive programming.',\n        price: 29999.99,\n        compareAtPrice: 34999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Robotics',\n        tags: [\n            'New Arrival',\n            'Robotics',\n            'Safety'\n        ],\n        stock: 10,\n        featured: true,\n        rating: 4.8,\n        reviewCount: 75,\n        specifications: {\n            'Payload': 'Up to 10kg',\n            'Reach': '1.3m',\n            'Degrees of Freedom': '6-axis',\n            'Safety Features': 'Force limiting, Vision',\n            'Programming': 'Teach pendant, GUI'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'digital-twin-platform',\n        name: 'Digital Twin Manufacturing Platform',\n        slug: 'digital-twin-platform',\n        description: 'Complete digital twin solution for real-time production monitoring and optimization.',\n        price: 12999.99,\n        compareAtPrice: 15999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'Industry 4.0',\n            'Digital Twin'\n        ],\n        stock: 20,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 110,\n        specifications: {\n            'Simulation': 'Real-time 3D',\n            'Data Integration': 'OPC UA, MQTT',\n            'Analytics': 'Predictive maintenance',\n            'Visualization': 'AR/VR support',\n            'API': 'RESTful, GraphQL'\n        },\n        createdAt: new Date().toISOString()\n    }\n];\nvar productCategories = [\n    {\n        id: 'smart-manufacturing',\n        name: {\n            en: 'Smart Manufacturing',\n            ar: 'التصنيع الذكي'\n        },\n        description: {\n            en: 'Industry 4.0 solutions for modern factories',\n            ar: 'حلول الصناعة 4.0 للمصانع الحديثة'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'quality-control',\n        name: {\n            en: 'Quality Control',\n            ar: 'مراقبة الجودة'\n        },\n        description: {\n            en: 'Advanced quality assurance systems',\n            ar: 'أنظمة ضمان الجودة المتقدمة'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'warehouse-management',\n        name: {\n            en: 'Warehouse Management',\n            ar: 'إدارة المستودعات'\n        },\n        description: {\n            en: 'Smart warehouse and inventory solutions',\n            ar: 'حلول المستودعات والمخزون الذكية'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'robotics',\n        name: {\n            en: 'Robotics',\n            ar: 'الروبوتات'\n        },\n        description: {\n            en: 'Next-generation industrial robots',\n            ar: 'الجيل القادم من الروبوتات الصناعية'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'digital-solutions',\n        name: {\n            en: 'Digital Solutions',\n            ar: 'الحلول الرقمية'\n        },\n        description: {\n            en: 'Digital transformation tools',\n            ar: 'أدوات التحول الرقمي'\n        },\n        image: '/images/placeholder-light.svg'\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/products.ts\n");

/***/ })

};
;