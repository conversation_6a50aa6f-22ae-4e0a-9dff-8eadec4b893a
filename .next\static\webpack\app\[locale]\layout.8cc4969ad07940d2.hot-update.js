"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _lib_queryClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/queryClient */ \"(app-pages-browser)/./src/lib/queryClient.ts\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _components_auth_AuthModalProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/auth/AuthModalProvider */ \"(app-pages-browser)/./src/components/auth/AuthModalProvider.tsx\");\n/* harmony import */ var _components_marketing_ABTestingProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/marketing/ABTestingProvider */ \"(app-pages-browser)/./src/components/marketing/ABTestingProvider.tsx\");\n/* harmony import */ var _services_AuthService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../services/AuthService */ \"(app-pages-browser)/./src/services/AuthService.ts\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \nvar _s = $RefreshSig$();\n\n\n// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';\n\n\n\n\n\n\n// Dynamically import ThemeProvider to avoid SSR issues\nconst ThemeProvider = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\")).then((mod)=>mod.ThemeProvider), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\providers.tsx -> \" + \"next-themes\"\n        ]\n    },\n    ssr: false\n});\n_c = ThemeProvider;\nfunction Providers(param) {\n    let { children, locale } = param;\n    _s();\n    const { setLanguage } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore)();\n    // Effect for initializing language based on locale prop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Providers.useEffect\": ()=>{\n            if (locale && (locale === 'ar' || locale === 'en')) {\n                setLanguage(locale);\n            }\n        }\n    }[\"Providers.useEffect\"], [\n        locale,\n        setLanguage\n    ]);\n    // Effect for one-time client-side initializations\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Providers.useEffect\": ()=>{\n            // إنشاء مستخدمين افتراضيين للتطوير المحلي\n            if (true) {\n                (0,_services_AuthService__WEBPACK_IMPORTED_MODULE_6__.initializeDefaultUsers)();\n            }\n        }\n    }[\"Providers.useEffect\"], []); // Empty dependency array ensures this runs only once on mount\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.QueryClientProvider, {\n        client: _lib_queryClient__WEBPACK_IMPORTED_MODULE_2__.queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeProvider, {\n            attribute: \"class\",\n            defaultTheme: \"system\",\n            enableSystem: true,\n            storageKey: \"ui-theme\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_marketing_ABTestingProvider__WEBPACK_IMPORTED_MODULE_5__.ABTestingProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AuthModalProvider__WEBPACK_IMPORTED_MODULE_4__.AuthModalProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\app\\\\providers.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\app\\\\providers.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(Providers, \"uwVJrNLr2+3rpMqVaPdiTXGDfW4=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_3__.useLanguageStore\n    ];\n});\n_c1 = Providers;\nvar _c, _c1;\n$RefreshReg$(_c, \"ThemeProvider\");\n$RefreshReg$(_c1, \"Providers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/providers.tsx\n"));

/***/ })

});