"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/shop/page",{

/***/ "(app-pages-browser)/./src/components/shop/ShopFooter.tsx":
/*!********************************************!*\
  !*** ./src/components/shop/ShopFooter.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShopFooter: () => (/* binding */ ShopFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevrons-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevrons-right.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _translations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../translations */ \"(app-pages-browser)/./src/translations/index.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../stores/themeStore */ \"(app-pages-browser)/./src/stores/themeStore.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _ui_animations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/animations */ \"(app-pages-browser)/./src/components/ui/animations/index.ts\");\n/* __next_internal_client_entry_do_not_use__ ShopFooter auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ShopFooter(param) {\n    let { totalProducts, currentPage, itemsPerPage, onPageChange } = param;\n    _s();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_2__.useLanguageStore)();\n    const { t, locale } = (0,_translations__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_4__.useThemeStore)();\n    // استخدام اللغة من المسار أو من المتجر\n    const currentLanguage = locale || language;\n    const isRTL = currentLanguage === 'ar';\n    // حساب إجمالي عدد الصفحات\n    const totalPages = Math.ceil(totalProducts / itemsPerPage);\n    // إنشاء مصفوفة أرقام الصفحات\n    const getPageNumbers = ()=>{\n        const pageNumbers = [];\n        const maxPagesToShow = 5;\n        if (totalPages <= maxPagesToShow) {\n            // إذا كان إجمالي عدد الصفحات أقل من أو يساوي الحد الأقصى، عرض جميع الصفحات\n            for(let i = 1; i <= totalPages; i++){\n                pageNumbers.push(i);\n            }\n        } else {\n            // إذا كان إجمالي عدد الصفحات أكبر من الحد الأقصى\n            let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));\n            let endPage = startPage + maxPagesToShow - 1;\n            if (endPage > totalPages) {\n                endPage = totalPages;\n                startPage = Math.max(1, endPage - maxPagesToShow + 1);\n            }\n            for(let i = startPage; i <= endPage; i++){\n                pageNumbers.push(i);\n            }\n            // إضافة \"...\" إذا لزم الأمر\n            if (startPage > 1) {\n                pageNumbers.unshift(-1); // استخدام -1 لتمثيل \"...\"\n                pageNumbers.unshift(1); // دائمًا إضافة الصفحة الأولى\n            }\n            if (endPage < totalPages) {\n                pageNumbers.push(-2); // استخدام -2 لتمثيل \"...\" الثاني\n                pageNumbers.push(totalPages); // دائمًا إضافة الصفحة الأخيرة\n            }\n        }\n        return pageNumbers;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-12\",\n        children: totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_animations__WEBPACK_IMPORTED_MODULE_6__.ScrollAnimation, {\n            animation: \"fade\",\n            delay: 0.1,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center my-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-1 rounded-lg p-1\", \"bg-white dark:bg-slate-800 shadow-md\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>onPageChange(1),\n                            disabled: currentPage === 1,\n                            className: \"h-9 w-9\",\n                            \"aria-label\": currentLanguage === 'ar' ? 'الصفحة الأولى' : 'First page',\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4\", isRTL && \"rotate-180\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>onPageChange(currentPage - 1),\n                            disabled: currentPage === 1,\n                            className: \"h-9 w-9\",\n                            \"aria-label\": currentLanguage === 'ar' ? 'الصفحة السابقة' : 'Previous page',\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4\", isRTL && \"rotate-180\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, this),\n                        getPageNumbers().map((pageNumber, index)=>pageNumber < 0 ? // عرض \"...\" للصفحات المحذوفة\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 text-slate-500 dark:text-slate-400\",\n                                children: \"...\"\n                            }, \"ellipsis-\".concat(index), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 19\n                            }, this) : // زر رقم الصفحة\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: currentPage === pageNumber ? \"default\" : \"ghost\",\n                                onClick: ()=>onPageChange(pageNumber),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-9 w-9 rounded-md\", currentPage === pageNumber && \"bg-primary-500 text-white hover:bg-primary-600\"),\n                                children: pageNumber\n                            }, \"page-\".concat(pageNumber), false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 19\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>onPageChange(currentPage + 1),\n                            disabled: currentPage === totalPages,\n                            className: \"h-9 w-9\",\n                            \"aria-label\": currentLanguage === 'ar' ? 'الصفحة التالية' : 'Next page',\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4\", isRTL && \"rotate-180\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>onPageChange(totalPages),\n                            disabled: currentPage === totalPages,\n                            className: \"h-9 w-9\",\n                            \"aria-label\": currentLanguage === 'ar' ? 'الصفحة الأخيرة' : 'Last page',\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4\", isRTL && \"rotate-180\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n                lineNumber: 82,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n            lineNumber: 81,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\components\\\\shop\\\\ShopFooter.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(ShopFooter, \"RnNKZR3AlEMlgEn6XQdXPR4V4Vw=\", false, function() {\n    return [\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_2__.useLanguageStore,\n        _translations__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        _stores_themeStore__WEBPACK_IMPORTED_MODULE_4__.useThemeStore\n    ];\n});\n_c = ShopFooter;\nvar _c;\n$RefreshReg$(_c, \"ShopFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shop/ShopFooter.tsx\n"));

/***/ })

});