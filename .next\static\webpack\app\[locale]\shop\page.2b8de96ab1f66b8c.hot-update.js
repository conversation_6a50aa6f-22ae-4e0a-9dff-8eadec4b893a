"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/shop/page",{

/***/ "(app-pages-browser)/./src/data/products.ts":
/*!******************************!*\
  !*** ./src/data/products.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   productCategories: () => (/* binding */ productCategories),\n/* harmony export */   products: () => (/* binding */ products)\n/* harmony export */ });\nconst products = [\n    {\n        id: 'smart-factory-sensor',\n        name: 'Smart Factory IoT Sensor Kit',\n        slug: 'smart-factory-sensor',\n        description: 'Next-generation IoT sensors for real-time manufacturing monitoring and predictive maintenance. Features advanced analytics and cloud connectivity.',\n        price: 2999.99,\n        compareAtPrice: 3499.99,\n        images: [\n            '/images/product-automation.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'IoT',\n            'Industry 4.0',\n            'Best Seller'\n        ],\n        stock: 50,\n        inStock: true,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 150,\n        specifications: {\n            'Sensor Type': 'Multi-parameter',\n            'Connectivity': 'WiFi, Bluetooth, LoRaWAN',\n            'Battery Life': 'Up to 2 years',\n            'Data Rate': '1 sample/second',\n            'Operating Temperature': '-20°C to 85°C',\n            'Warranty': '3 years',\n            'Certification': 'CE, FCC, RoHS'\n        },\n        createdAt: new Date().toISOString(),\n        relatedProducts: [\n            'industrial-control-panel',\n            'automation-software'\n        ]\n    },\n    {\n        id: 'ai-quality-system',\n        name: 'AI-Powered Quality Inspection System',\n        slug: 'ai-quality-system',\n        description: 'Advanced computer vision system for automated quality control in production lines.',\n        price: 8999.99,\n        compareAtPrice: 10999.99,\n        images: [\n            '/images/product-packaging.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Quality Control',\n        tags: [\n            'New Arrival',\n            'AI',\n            'Automation'\n        ],\n        stock: 15,\n        featured: true,\n        rating: 4.9,\n        reviewCount: 210,\n        specifications: {\n            'Camera Resolution': '4K Ultra HD',\n            'Processing Speed': 'Up to 100 items/minute',\n            'AI Model': 'Deep Learning CNN',\n            'Integration': 'REST API, MQTT',\n            'Accuracy Rate': '99.9%'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'smart-inventory',\n        name: 'Smart Inventory Management System',\n        slug: 'smart-inventory',\n        description: 'Cloud-based inventory tracking system with real-time analytics and AI-driven forecasting.',\n        price: 4999.99,\n        compareAtPrice: 5999.99,\n        images: [\n            '/images/product-manufacturing.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Warehouse Management',\n        tags: [\n            'New Arrival',\n            'Smart Systems',\n            'Cloud'\n        ],\n        stock: 30,\n        featured: true,\n        rating: 4.6,\n        reviewCount: 95,\n        specifications: {\n            'Storage Capacity': 'Unlimited',\n            'Real-time Tracking': 'Yes',\n            'Mobile App': 'iOS & Android',\n            'Barcode Support': '1D & 2D',\n            'Analytics': 'Advanced ML-based'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'collaborative-robot',\n        name: 'Next-Gen Collaborative Robot',\n        slug: 'collaborative-robot',\n        description: 'Advanced collaborative robot with enhanced safety features and intuitive programming.',\n        price: 29999.99,\n        compareAtPrice: 34999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Robotics',\n        tags: [\n            'New Arrival',\n            'Robotics',\n            'Safety'\n        ],\n        stock: 10,\n        featured: true,\n        rating: 4.8,\n        reviewCount: 75,\n        specifications: {\n            'Payload': 'Up to 10kg',\n            'Reach': '1.3m',\n            'Degrees of Freedom': '6-axis',\n            'Safety Features': 'Force limiting, Vision',\n            'Programming': 'Teach pendant, GUI'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'digital-twin-platform',\n        name: 'Digital Twin Manufacturing Platform',\n        slug: 'digital-twin-platform',\n        description: 'Complete digital twin solution for real-time production monitoring and optimization. Advanced AI-driven analytics and predictive maintenance.',\n        price: 12999.99,\n        compareAtPrice: 15999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'Industry 4.0',\n            'Digital Twin',\n            'Premium'\n        ],\n        stock: 20,\n        inStock: true,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 110,\n        specifications: {\n            'Simulation': 'Real-time 3D',\n            'Data Integration': 'OPC UA, MQTT',\n            'Analytics': 'Predictive maintenance',\n            'Visualization': 'AR/VR support',\n            'API': 'RESTful, GraphQL',\n            'Cloud Support': 'AWS, Azure, GCP',\n            'License': 'Enterprise'\n        },\n        createdAt: new Date().toISOString(),\n        relatedProducts: [\n            'smart-factory-sensor',\n            'automation-software'\n        ]\n    },\n    // Additional Professional Products\n    {\n        id: 'industrial-robot-arm',\n        name: 'Precision Industrial Robot Arm',\n        slug: 'industrial-robot-arm',\n        description: 'High-precision 6-axis industrial robot arm for automated manufacturing processes. Perfect for assembly, welding, and material handling.',\n        price: 45999.99,\n        compareAtPrice: 52999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Robotics',\n        tags: [\n            'Premium',\n            'Automation',\n            'High Precision'\n        ],\n        stock: 8,\n        inStock: true,\n        featured: true,\n        rating: 4.9,\n        reviewCount: 85,\n        specifications: {\n            'Payload': '10kg',\n            'Reach': '1.4m',\n            'Repeatability': '±0.03mm',\n            'Degrees of Freedom': '6',\n            'Power': '3.5kW',\n            'Weight': '180kg',\n            'Programming': 'Teach pendant, offline'\n        },\n        createdAt: new Date().toISOString(),\n        relatedProducts: [\n            'smart-factory-sensor',\n            'industrial-control-panel'\n        ]\n    },\n    {\n        id: 'cnc-machining-center',\n        name: 'CNC Vertical Machining Center',\n        slug: 'cnc-machining-center',\n        description: 'State-of-the-art CNC vertical machining center with advanced control system and high-speed spindle for precision manufacturing.',\n        price: 125999.99,\n        compareAtPrice: 145999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Machine Tools',\n        tags: [\n            'Premium',\n            'CNC',\n            'High Performance'\n        ],\n        stock: 3,\n        inStock: true,\n        featured: false,\n        rating: 4.8,\n        reviewCount: 42,\n        specifications: {\n            'Table Size': '800x400mm',\n            'X/Y/Z Travel': '800/500/500mm',\n            'Spindle Speed': '12,000 RPM',\n            'Tool Capacity': '24 tools',\n            'Positioning Accuracy': '±0.005mm',\n            'Control System': 'Fanuc 0i-MF Plus'\n        },\n        createdAt: new Date().toISOString(),\n        relatedProducts: [\n            'precision-measuring-tools',\n            'cutting-tools-set'\n        ]\n    },\n    {\n        id: 'laser-cutting-machine',\n        name: 'Fiber Laser Cutting Machine',\n        slug: 'laser-cutting-machine',\n        description: 'High-power fiber laser cutting machine for metal fabrication. Exceptional cutting quality and speed with minimal maintenance.',\n        price: 89999.99,\n        compareAtPrice: 99999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Laser Equipment',\n        tags: [\n            'Best Seller',\n            'Laser',\n            'Metal Cutting'\n        ],\n        stock: 5,\n        inStock: true,\n        featured: true,\n        rating: 4.6,\n        reviewCount: 128,\n        specifications: {\n            'Laser Power': '3000W',\n            'Cutting Area': '3000x1500mm',\n            'Max Thickness': '20mm (steel)',\n            'Positioning Accuracy': '±0.03mm',\n            'Cutting Speed': '35m/min',\n            'Power Consumption': '25kW'\n        },\n        createdAt: new Date().toISOString(),\n        relatedProducts: [\n            'sheet-metal-bender',\n            'welding-equipment'\n        ]\n    },\n    {\n        id: 'industrial-3d-printer',\n        name: 'Industrial SLA 3D Printer',\n        slug: 'industrial-3d-printer',\n        description: 'Professional stereolithography 3D printer for high-resolution prototyping and small-batch production. Perfect for detailed parts.',\n        price: 15999.99,\n        compareAtPrice: 18999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Additive Manufacturing',\n        tags: [\n            '3D Printing',\n            'Prototyping',\n            'High Resolution'\n        ],\n        stock: 12,\n        inStock: true,\n        featured: false,\n        rating: 4.4,\n        reviewCount: 67,\n        specifications: {\n            'Build Volume': '145x145x175mm',\n            'Layer Resolution': '0.01-0.2mm',\n            'Light Source': '405nm UV LED',\n            'Print Speed': '30mm/hour',\n            'Connectivity': 'USB, Ethernet, WiFi',\n            'Software': 'PreForm'\n        },\n        createdAt: new Date().toISOString(),\n        relatedProducts: [\n            '3d-printing-materials',\n            'post-processing-kit'\n        ]\n    }\n];\nconst productCategories = [\n    {\n        id: 'smart-manufacturing',\n        name: {\n            en: 'Smart Manufacturing',\n            ar: 'التصنيع الذكي'\n        },\n        description: {\n            en: 'Industry 4.0 solutions for modern factories',\n            ar: 'حلول الصناعة 4.0 للمصانع الحديثة'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'quality-control',\n        name: {\n            en: 'Quality Control',\n            ar: 'مراقبة الجودة'\n        },\n        description: {\n            en: 'Advanced quality assurance systems',\n            ar: 'أنظمة ضمان الجودة المتقدمة'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'warehouse-management',\n        name: {\n            en: 'Warehouse Management',\n            ar: 'إدارة المستودعات'\n        },\n        description: {\n            en: 'Smart warehouse and inventory solutions',\n            ar: 'حلول المستودعات والمخزون الذكية'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'robotics',\n        name: {\n            en: 'Robotics',\n            ar: 'الروبوتات'\n        },\n        description: {\n            en: 'Next-generation industrial robots',\n            ar: 'الجيل القادم من الروبوتات الصناعية'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'digital-solutions',\n        name: {\n            en: 'Digital Solutions',\n            ar: 'الحلول الرقمية'\n        },\n        description: {\n            en: 'Digital transformation tools',\n            ar: 'أدوات التحول الرقمي'\n        },\n        image: '/images/placeholder-light.svg'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/products.ts\n"));

/***/ })

});