"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/production-lines/page",{

/***/ "(app-pages-browser)/./src/pages/production/ProductionLinesPage.tsx":
/*!******************************************************!*\
  !*** ./src/pages/production/ProductionLinesPage.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductionLinesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/factory.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_EnhancedImage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/ui/EnhancedImage */ \"(app-pages-browser)/./src/components/ui/EnhancedImage.tsx\");\n/* harmony import */ var _data_productionLines__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../data/productionLines */ \"(app-pages-browser)/./src/data/productionLines.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../stores/themeStore */ \"(app-pages-browser)/./src/stores/themeStore.ts\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _translations__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../translations */ \"(app-pages-browser)/./src/translations/index.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_animations__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../components/ui/animations */ \"(app-pages-browser)/./src/components/ui/animations/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst categoriesData = {\n    en: [\n        {\n            id: 'all',\n            name: 'All Lines',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Manufacturing',\n            name: 'Manufacturing',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Food & Beverage',\n            name: 'Food & Beverage',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Packaging',\n            name: 'Packaging',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Pharmaceutical',\n            name: 'Pharmaceutical',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            count: 0\n        }\n    ],\n    ar: [\n        {\n            id: 'all',\n            name: 'جميع الخطوط',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Manufacturing',\n            name: 'التصنيع',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Food & Beverage',\n            name: 'الأغذية والمشروبات',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Packaging',\n            name: 'التعبئة والتغليف',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Pharmaceutical',\n            name: 'الصناعات الدوائية',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            count: 0\n        }\n    ]\n};\nconst statusData = {\n    en: [\n        {\n            id: 'all',\n            name: 'All Status',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            color: 'text-slate-500'\n        },\n        {\n            id: 'active',\n            name: 'Active',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            color: 'text-green-500'\n        },\n        {\n            id: 'maintenance',\n            name: 'Maintenance',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            color: 'text-yellow-500'\n        },\n        {\n            id: 'inactive',\n            name: 'Inactive',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            color: 'text-red-500'\n        }\n    ],\n    ar: [\n        {\n            id: 'all',\n            name: 'جميع الحالات',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            color: 'text-slate-500'\n        },\n        {\n            id: 'active',\n            name: 'نشط',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            color: 'text-green-500'\n        },\n        {\n            id: 'maintenance',\n            name: 'صيانة',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            color: 'text-yellow-500'\n        },\n        {\n            id: 'inactive',\n            name: 'غير نشط',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            color: 'text-red-500'\n        }\n    ]\n};\nconst sortOptions = {\n    en: [\n        {\n            id: 'name',\n            name: 'Name A-Z',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"]\n        },\n        {\n            id: 'efficiency',\n            name: 'Efficiency',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"]\n        },\n        {\n            id: 'capacity',\n            name: 'Capacity',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            id: 'priority',\n            name: 'Priority',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"]\n        },\n        {\n            id: 'createdAt',\n            name: 'Date Added',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"]\n        }\n    ],\n    ar: [\n        {\n            id: 'name',\n            name: 'الاسم أ-ي',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"]\n        },\n        {\n            id: 'efficiency',\n            name: 'الكفاءة',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"]\n        },\n        {\n            id: 'capacity',\n            name: 'السعة',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            id: 'priority',\n            name: 'الأولوية',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"]\n        },\n        {\n            id: 'createdAt',\n            name: 'تاريخ الإضافة',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"]\n        }\n    ]\n};\nfunction ProductionLinesPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_9__.useThemeStore)();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_10__.useLanguageStore)();\n    const { t, locale } = (0,_translations__WEBPACK_IMPORTED_MODULE_11__.useTranslation)();\n    // استخدام اللغة من المسار أو من المتجر\n    const currentLanguage = locale || language;\n    // State management for enhanced filtering and UI\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: 'all',\n        status: 'all',\n        efficiency: {\n            min: 0,\n            max: 100\n        },\n        capacity: {\n            min: 0,\n            max: 100000\n        },\n        manufacturer: 'all',\n        searchQuery: '',\n        tags: [],\n        featured: false\n    });\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('priority');\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asc');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // استخدام البيانات المناسبة حسب اللغة\n    const categories = categoriesData[currentLanguage];\n    const statusOptions = statusData[currentLanguage];\n    const sortingOptions = sortOptions[currentLanguage];\n    // Calculate category counts\n    const categoriesWithCounts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductionLinesPage.useMemo[categoriesWithCounts]\": ()=>{\n            return categories.map({\n                \"ProductionLinesPage.useMemo[categoriesWithCounts]\": (category)=>({\n                        ...category,\n                        count: category.id === 'all' ? _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.length : _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.filter({\n                            \"ProductionLinesPage.useMemo[categoriesWithCounts]\": (line)=>line.category === category.id\n                        }[\"ProductionLinesPage.useMemo[categoriesWithCounts]\"]).length\n                    })\n            }[\"ProductionLinesPage.useMemo[categoriesWithCounts]\"]);\n        }\n    }[\"ProductionLinesPage.useMemo[categoriesWithCounts]\"], [\n        categories\n    ]);\n    // Advanced filtering logic\n    const filteredLines = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductionLinesPage.useMemo[filteredLines]\": ()=>{\n            let filtered = _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines;\n            // Category filter\n            if (filters.category !== 'all') {\n                filtered = filtered.filter({\n                    \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>line.category === filters.category\n                }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            }\n            // Status filter\n            if (filters.status !== 'all') {\n                filtered = filtered.filter({\n                    \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>line.status === filters.status\n                }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            }\n            // Search filter\n            if (filters.searchQuery) {\n                const query = filters.searchQuery.toLowerCase();\n                filtered = filtered.filter({\n                    \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>{\n                        var _line_manufacturer, _line_tags;\n                        return line.name.toLowerCase().includes(query) || line.description.toLowerCase().includes(query) || ((_line_manufacturer = line.manufacturer) === null || _line_manufacturer === void 0 ? void 0 : _line_manufacturer.toLowerCase().includes(query)) || ((_line_tags = line.tags) === null || _line_tags === void 0 ? void 0 : _line_tags.some({\n                            \"ProductionLinesPage.useMemo[filteredLines]\": (tag)=>tag.toLowerCase().includes(query)\n                        }[\"ProductionLinesPage.useMemo[filteredLines]\"]));\n                    }\n                }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            }\n            // Efficiency filter\n            filtered = filtered.filter({\n                \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>line.efficiency >= filters.efficiency.min && line.efficiency <= filters.efficiency.max\n            }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            // Featured filter\n            if (filters.featured) {\n                filtered = filtered.filter({\n                    \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>line.featured\n                }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            }\n            // Tags filter\n            if (filters.tags.length > 0) {\n                filtered = filtered.filter({\n                    \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>filters.tags.some({\n                            \"ProductionLinesPage.useMemo[filteredLines]\": (tag)=>{\n                                var _line_tags;\n                                return (_line_tags = line.tags) === null || _line_tags === void 0 ? void 0 : _line_tags.includes(tag);\n                            }\n                        }[\"ProductionLinesPage.useMemo[filteredLines]\"])\n                }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            }\n            return filtered;\n        }\n    }[\"ProductionLinesPage.useMemo[filteredLines]\"], [\n        filters\n    ]);\n    // Sorting logic\n    const sortedLines = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductionLinesPage.useMemo[sortedLines]\": ()=>{\n            const sorted = [\n                ...filteredLines\n            ].sort({\n                \"ProductionLinesPage.useMemo[sortedLines].sorted\": (a, b)=>{\n                    let aValue = a[sortBy];\n                    let bValue = b[sortBy];\n                    // Handle special cases\n                    if (sortBy === 'capacity') {\n                        aValue = parseInt(a.capacity.replace(/[^\\d]/g, ''));\n                        bValue = parseInt(b.capacity.replace(/[^\\d]/g, ''));\n                    } else if (sortBy === 'createdAt') {\n                        aValue = new Date(a.createdAt).getTime();\n                        bValue = new Date(b.createdAt).getTime();\n                    }\n                    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;\n                    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;\n                    return 0;\n                }\n            }[\"ProductionLinesPage.useMemo[sortedLines].sorted\"]);\n            return sorted;\n        }\n    }[\"ProductionLinesPage.useMemo[sortedLines]\"], [\n        filteredLines,\n        sortBy,\n        sortDirection\n    ]);\n    // Statistics calculation\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductionLinesPage.useMemo[stats]\": ()=>{\n            const activeLines = _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.filter({\n                \"ProductionLinesPage.useMemo[stats]\": (line)=>line.status === 'active'\n            }[\"ProductionLinesPage.useMemo[stats]\"]).length;\n            const maintenanceLines = _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.filter({\n                \"ProductionLinesPage.useMemo[stats]\": (line)=>line.status === 'maintenance'\n            }[\"ProductionLinesPage.useMemo[stats]\"]).length;\n            const averageEfficiency = _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.reduce({\n                \"ProductionLinesPage.useMemo[stats]\": (sum, line)=>sum + line.efficiency\n            }[\"ProductionLinesPage.useMemo[stats]\"], 0) / _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.length;\n            return {\n                totalLines: _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.length,\n                activeLines,\n                maintenanceLines,\n                averageEfficiency: Math.round(averageEfficiency * 10) / 10,\n                filteredCount: sortedLines.length\n            };\n        }\n    }[\"ProductionLinesPage.useMemo[stats]\"], [\n        sortedLines\n    ]);\n    // Handle filter changes\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    // Handle sort changes\n    const handleSortChange = (field)=>{\n        if (field === sortBy) {\n            setSortDirection((prev)=>prev === 'asc' ? 'desc' : 'asc');\n        } else {\n            setSortBy(field);\n            setSortDirection('asc');\n        }\n    };\n    // Reset filters\n    const resetFilters = ()=>{\n        setFilters({\n            category: 'all',\n            status: 'all',\n            efficiency: {\n                min: 0,\n                max: 100\n            },\n            capacity: {\n                min: 0,\n                max: 100000\n            },\n            manufacturer: 'all',\n            searchQuery: '',\n            tags: [],\n            featured: false\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"relative py-16 overflow-hidden transition-colors duration-500\", isDarkMode ? \"bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900\" : \"bg-gradient-to-br from-slate-50 via-white to-slate-100\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"absolute inset-0 opacity-5 transition-opacity duration-500\", isDarkMode ? \"opacity-10\" : \"opacity-5\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:50px_50px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-4xl md:text-5xl font-bold leading-tight tracking-tight mb-6\", isDarkMode ? \"text-white\" : \"text-slate-900\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block\",\n                                                children: currentLanguage === 'ar' ? 'خطوط الإنتاج' : 'Production'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"block bg-gradient-to-r bg-clip-text text-transparent\", isDarkMode ? \"from-primary-400 to-blue-400\" : \"from-primary-600 to-blue-600\"),\n                                                children: currentLanguage === 'ar' ? 'الصناعية' : 'Lines'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-lg md:text-xl leading-relaxed max-w-3xl mx-auto mb-8\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\"),\n                                        children: currentLanguage === 'ar' ? 'تقنيات إنتاج متقدمة مصممة للكفاءة والموثوقية مع أنظمة ذكية للتحكم والمراقبة.' : 'Advanced production technologies engineered for efficiency and reliability with intelligent control and monitoring systems.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"primary\",\n                                                    className: \"px-8 py-3 text-lg font-semibold group\",\n                                                    onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/contact\")),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5 group-hover:scale-110 transition-transform\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'استشارة متخصصة' : 'Expert Consultation'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"outline\",\n                                                    className: \"px-8 py-3 text-lg font-semibold group\",\n                                                    onClick: ()=>{\n                                                        var _document_getElementById;\n                                                        return (_document_getElementById = document.getElementById('production-lines')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                            behavior: 'smooth'\n                                                        });\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2', \" h-5 w-5 group-hover:translate-x-1 transition-transform\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'استكشف المنتجات' : 'Explore Products'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"py-16\", isDarkMode ? \"bg-slate-900\" : \"bg-slate-50\"),\n                id: \"production-lines\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.2,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                            type: \"text\",\n                                            placeholder: currentLanguage === 'ar' ? 'البحث في خطوط الإنتاج...' : 'Search production lines...',\n                                            value: filters.searchQuery,\n                                            onChange: (e)=>handleFilterChange('searchQuery', e.target.value),\n                                            className: \"pl-12 pr-4 py-4 text-lg rounded-xl border-2 border-slate-200 dark:border-slate-700 focus:border-primary-500 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this),\n                                        filters.searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleFilterChange('searchQuery', ''),\n                                            className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-slate-600 dark:text-slate-300 mb-3\",\n                                                children: currentLanguage === 'ar' ? 'الفئات' : 'Categories'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: categoriesWithCounts.map((category)=>{\n                                                    const Icon = category.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                        animation: \"scale\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: filters.category === category.id ? 'primary' : 'outline',\n                                                            onClick: ()=>handleFilterChange('category', category.id),\n                                                            className: \"flex items-center gap-2 text-sm\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                category.name,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-0.5 rounded-full text-xs\", filters.category === category.id ? \"bg-white/20 text-white\" : \"bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300\"),\n                                                                    children: category.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, category.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:w-64\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-slate-600 dark:text-slate-300 mb-3\",\n                                                children: currentLanguage === 'ar' ? 'الحالة' : 'Status'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: statusOptions.map((status)=>{\n                                                    const Icon = status.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                        animation: \"scale\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: filters.status === status.id ? 'primary' : 'outline',\n                                                            onClick: ()=>handleFilterChange('status', status.id),\n                                                            className: \"flex items-center gap-2 text-sm\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    size: 16,\n                                                                    className: status.color\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                status.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, status.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setShowFilters(!showFilters),\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    currentLanguage === 'ar' ? 'مرشحات متقدمة' : 'Advanced Filters'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center gap-2 text-sm text-slate-600 dark:text-slate-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: filters.featured,\n                                                            onChange: (e)=>handleFilterChange('featured', e.target.checked),\n                                                            className: \"rounded border-slate-300 text-primary-600 focus:ring-primary-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"text-yellow-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'مميز فقط' : 'Featured Only'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this),\n                                            (filters.searchQuery || filters.category !== 'all' || filters.status !== 'all' || filters.featured) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: resetFilters,\n                                                className: \"text-sm text-slate-500 hover:text-slate-700\",\n                                                children: currentLanguage === 'ar' ? 'إعادة تعيين' : 'Reset Filters'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                children: currentLanguage === 'ar' ? \"\".concat(stats.filteredCount, \" من \").concat(stats.totalLines, \" خط إنتاج\") : \"\".concat(stats.filteredCount, \" of \").concat(stats.totalLines, \" production lines\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: sortBy,\n                                                onChange: (e)=>handleSortChange(e.target.value),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-3 py-2 rounded-lg border text-sm\", isDarkMode ? \"bg-slate-800 border-slate-700 text-white\" : \"bg-white border-slate-300 text-slate-900\"),\n                                                children: sortingOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: option.id,\n                                                        children: option.name\n                                                    }, option.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex rounded-lg border border-slate-300 dark:border-slate-700 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setViewMode('grid'),\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"p-2 transition-colors\", viewMode === 'grid' ? \"bg-primary-500 text-white\" : \"bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setViewMode('list'),\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"p-2 transition-colors\", viewMode === 'list' ? \"bg-primary-500 text-white\" : \"bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, this),\n                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"slide\",\n                            direction: \"down\",\n                            delay: 0.1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: \"p-6 mb-8 border-2 border-primary-200 dark:border-primary-800\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-slate-600 dark:text-slate-300 mb-2\",\n                                                    children: currentLanguage === 'ar' ? 'نطاق الكفاءة (%)' : 'Efficiency Range (%)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            max: \"100\",\n                                                            value: filters.efficiency.min,\n                                                            onChange: (e)=>handleFilterChange('efficiency', {\n                                                                    ...filters.efficiency,\n                                                                    min: Number(e.target.value)\n                                                                }),\n                                                            className: \"w-20\",\n                                                            placeholder: \"Min\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-400\",\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            max: \"100\",\n                                                            value: filters.efficiency.max,\n                                                            onChange: (e)=>handleFilterChange('efficiency', {\n                                                                    ...filters.efficiency,\n                                                                    max: Number(e.target.value)\n                                                                }),\n                                                            className: \"w-20\",\n                                                            placeholder: \"Max\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-slate-600 dark:text-slate-300 mb-2\",\n                                                    children: currentLanguage === 'ar' ? 'الشركة المصنعة' : 'Manufacturer'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filters.manufacturer,\n                                                    onChange: (e)=>handleFilterChange('manufacturer', e.target.value),\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"w-full px-3 py-2 rounded-lg border\", isDarkMode ? \"bg-slate-800 border-slate-700 text-white\" : \"bg-white border-slate-300 text-slate-900\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"all\",\n                                                            children: currentLanguage === 'ar' ? 'جميع الشركات' : 'All Manufacturers'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"AFTAL Industries\",\n                                                            children: \"AFTAL Industries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"AFTAL Food Systems\",\n                                                            children: \"AFTAL Food Systems\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"AFTAL Packaging Solutions\",\n                                                            children: \"AFTAL Packaging Solutions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"AFTAL Pharma Systems\",\n                                                            children: \"AFTAL Pharma Systems\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-slate-600 dark:text-slate-300 mb-2\",\n                                                    children: currentLanguage === 'ar' ? 'العلامات' : 'Tags'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        'automation',\n                                                        'robotics',\n                                                        'ai',\n                                                        'quality-control',\n                                                        'haccp',\n                                                        'gmp'\n                                                    ].map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const newTags = filters.tags.includes(tag) ? filters.tags.filter((t)=>t !== tag) : [\n                                                                    ...filters.tags,\n                                                                    tag\n                                                                ];\n                                                                handleFilterChange('tags', newTags);\n                                                            },\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-1 rounded text-xs transition-colors\", filters.tags.includes(tag) ? \"bg-primary-500 text-white\" : \"bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-600\"),\n                                                            children: tag\n                                                        }, tag, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"py-12\", isDarkMode ? \"bg-slate-800\" : \"bg-white\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 13\n                    }, this) : sortedLines.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                        animation: \"fade\",\n                        delay: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"p-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-16 w-16 text-slate-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-slate-900 dark:text-white\",\n                                    children: currentLanguage === 'ar' ? 'لا توجد خطوط إنتاج' : 'No Production Lines Found'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 dark:text-slate-300 mb-6\",\n                                    children: currentLanguage === 'ar' ? 'لم يتم العثور على خطوط إنتاج تطابق معايير البحث الخاصة بك.' : 'No production lines match your search criteria.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: resetFilters,\n                                    variant: \"outline\",\n                                    children: currentLanguage === 'ar' ? 'إعادة تعيين المرشحات' : 'Reset Filters'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 13\n                    }, this) : viewMode === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollStagger, {\n                        animation: \"slide\",\n                        direction: \"up\",\n                        staggerDelay: 0.1,\n                        delay: 0.3,\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: sortedLines.map((line)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                animation: \"lift\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"flex flex-col overflow-hidden group h-full relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 z-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-1 rounded-full text-xs font-medium\", line.status === 'active' && \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\", line.status === 'maintenance' && \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\", line.status === 'inactive' && \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200\"),\n                                                children: [\n                                                    line.status === 'active' && (currentLanguage === 'ar' ? 'نشط' : 'Active'),\n                                                    line.status === 'maintenance' && (currentLanguage === 'ar' ? 'صيانة' : 'Maintenance'),\n                                                    line.status === 'inactive' && (currentLanguage === 'ar' ? 'غير نشط' : 'Inactive')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 21\n                                        }, this),\n                                        line.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4 z-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        size: 12\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    currentLanguage === 'ar' ? 'مميز' : 'Featured'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_EnhancedImage__WEBPACK_IMPORTED_MODULE_7__.EnhancedImage, {\n                                                    src: line.images[0],\n                                                    alt: line.name,\n                                                    fill: true,\n                                                    objectFit: \"cover\",\n                                                    effect: \"zoom\",\n                                                    progressive: true,\n                                                    placeholder: \"shimmer\",\n                                                    className: \"w-full h-56\",\n                                                    containerClassName: \"w-full h-56\",\n                                                    sizes: \"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            line.videoUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"secondary\",\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                        size: 14,\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    currentLanguage === 'ar' ? 'فيديو' : 'Video'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"secondary\",\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                        size: 14,\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 689,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    currentLanguage === 'ar' ? 'كتالوج' : 'Catalog'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 flex-grow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"inline-block px-2 py-1 rounded text-xs font-medium mb-2\", isDarkMode ? \"bg-primary-900/20 text-primary-400\" : \"bg-primary-100 text-primary-800\"),\n                                                                    children: currentLanguage === 'ar' ? line.category_ar || line.category : line.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-slate-900 dark:text-white\",\n                                                                    children: currentLanguage === 'ar' ? line.name_ar || line.name : line.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 705,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-slate-600 dark:text-slate-300\",\n                                                                    children: currentLanguage === 'ar' ? 'الكفاءة' : 'Efficiency'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-lg font-bold\", line.efficiency >= 95 && \"text-green-600\", line.efficiency >= 90 && line.efficiency < 95 && \"text-yellow-600\", line.efficiency < 90 && \"text-red-600\"),\n                                                                    children: [\n                                                                        line.efficiency,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 dark:text-slate-300 mb-4 line-clamp-2\",\n                                                    children: currentLanguage === 'ar' ? line.description_ar || line.description : line.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentLanguage === 'ar' ? \"السعة: \".concat(line.capacity_ar || line.capacity) : \"Capacity: \".concat(line.capacity)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                    className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentLanguage === 'ar' ? \"استهلاك الطاقة: \".concat(line.energyConsumption) : \"Energy: \".concat(line.energyConsumption)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                    className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentLanguage === 'ar' ? \"التكلفة التشغيلية: $\".concat(line.operatingCost, \"/يوم\") : \"Operating Cost: $\".concat(line.operatingCost, \"/day\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 23\n                                                }, this),\n                                                line.tags && line.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1 mb-4\",\n                                                    children: [\n                                                        line.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded text-xs\",\n                                                                children: tag\n                                                            }, tag, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 29\n                                                            }, this)),\n                                                        line.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded text-xs\",\n                                                            children: [\n                                                                \"+\",\n                                                                line.tags.length - 3\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                        animation: \"scale\",\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/\".concat(currentLanguage, \"/production-lines/\").concat(line.slug),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                        className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 768,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                        animation: \"scale\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"px-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 19\n                                }, this)\n                            }, line.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 13\n                    }, this) : /* List View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollStagger, {\n                        animation: \"slide\",\n                        direction: \"up\",\n                        staggerDelay: 0.05,\n                        delay: 0.3,\n                        className: \"space-y-4\",\n                        children: sortedLines.map((line)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                animation: \"lift\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"p-6 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col lg:flex-row gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"lg:w-64 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_EnhancedImage__WEBPACK_IMPORTED_MODULE_7__.EnhancedImage, {\n                                                            src: line.images[0],\n                                                            alt: line.name,\n                                                            fill: true,\n                                                            objectFit: \"cover\",\n                                                            effect: \"zoom\",\n                                                            progressive: true,\n                                                            placeholder: \"shimmer\",\n                                                            className: \"w-full h-40 lg:h-32 rounded-lg\",\n                                                            containerClassName: \"w-full h-40 lg:h-32 rounded-lg overflow-hidden\",\n                                                            sizes: \"(max-width: 1024px) 100vw, 256px\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        line.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-medium flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    size: 10\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                currentLanguage === 'ar' ? 'مميز' : 'Featured'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col lg:flex-row lg:items-start lg:justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3 mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-1 rounded text-xs font-medium\", isDarkMode ? \"bg-primary-900/20 text-primary-400\" : \"bg-primary-100 text-primary-800\"),\n                                                                                children: currentLanguage === 'ar' ? line.category_ar || line.category : line.category\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 826,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-1 rounded text-xs font-medium\", line.status === 'active' && \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\", line.status === 'maintenance' && \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\", line.status === 'inactive' && \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200\"),\n                                                                                children: [\n                                                                                    line.status === 'active' && (currentLanguage === 'ar' ? 'نشط' : 'Active'),\n                                                                                    line.status === 'maintenance' && (currentLanguage === 'ar' ? 'صيانة' : 'Maintenance'),\n                                                                                    line.status === 'inactive' && (currentLanguage === 'ar' ? 'غير نشط' : 'Inactive')\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 832,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 825,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-slate-900 dark:text-white mb-2\",\n                                                                        children: currentLanguage === 'ar' ? line.name_ar || line.name : line.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 843,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-600 dark:text-slate-300 mb-3 line-clamp-2\",\n                                                                        children: currentLanguage === 'ar' ? line.description_ar || line.description : line.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 846,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right lg:ml-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-slate-600 dark:text-slate-300 mb-1\",\n                                                                        children: currentLanguage === 'ar' ? 'الكفاءة' : 'Efficiency'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-2xl font-bold\", line.efficiency >= 95 && \"text-green-600\", line.efficiency >= 90 && line.efficiency < 95 && \"text-yellow-600\", line.efficiency < 90 && \"text-red-600\"),\n                                                                        children: [\n                                                                            line.efficiency,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 855,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 851,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 869,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: currentLanguage === 'ar' ? 'السعة' : 'Capacity'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 871,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: currentLanguage === 'ar' ? line.capacity_ar || line.capacity : line.capacity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 872,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 870,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                        className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 876,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: currentLanguage === 'ar' ? 'الطاقة' : 'Energy'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 878,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: line.energyConsumption\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 879,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 877,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                        className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 883,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: currentLanguage === 'ar' ? 'التكلفة' : 'Cost'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 885,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    line.operatingCost,\n                                                                                    \"/day\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 886,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 884,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 890,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: currentLanguage === 'ar' ? 'الصيانة' : 'Maintenance'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 892,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: new Date(line.maintenanceSchedule || '').toLocaleDateString()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 893,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 891,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/\".concat(currentLanguage, \"/production-lines/\").concat(line.slug),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                                className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 903,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 902,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            line.videoUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                            lineNumber: 911,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        currentLanguage === 'ar' ? 'فيديو' : 'Video'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 910,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 909,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                            lineNumber: 918,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        currentLanguage === 'ar' ? 'كتالوج' : 'Catalog'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 917,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 916,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                            lineNumber: 924,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        currentLanguage === 'ar' ? 'إحصائيات' : 'Analytics'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 923,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 796,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 19\n                                }, this)\n                            }, line.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 794,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                lineNumber: 606,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"py-20\", isDarkMode ? \"bg-slate-900\" : \"bg-slate-50\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold mb-6 text-slate-900 dark:text-white\",\n                                        children: currentLanguage === 'ar' ? 'لماذا تختار خطوط إنتاجنا؟' : 'Why Choose Our Production Lines?'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-slate-600 dark:text-slate-300 leading-relaxed\",\n                                        children: currentLanguage === 'ar' ? 'حلول رائدة في الصناعة مدعومة بالابتكار والخبرة مع أحدث التقنيات والمعايير العالمية' : 'Industry-leading solutions backed by innovation and expertise with cutting-edge technology and global standards'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 947,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 943,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 942,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollStagger, {\n                            animation: \"slide\",\n                            direction: \"up\",\n                            staggerDelay: 0.1,\n                            delay: 0.4,\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\",\n                            children: [\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-12 w-12 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: currentLanguage === 'ar' ? 'أتمتة متقدمة' : 'Advanced Automation',\n                                    description: currentLanguage === 'ar' ? 'أنظمة روبوتية وتحكم متطورة مع الذكاء الاصطناعي لتحقيق أقصى قدر من الكفاءة والدقة' : 'State-of-the-art robotics and AI-powered control systems for maximum efficiency and precision',\n                                    features: currentLanguage === 'ar' ? [\n                                        'تحكم PLC متقدم',\n                                        'أنظمة SCADA',\n                                        'صيانة تنبؤية'\n                                    ] : [\n                                        'Advanced PLC Control',\n                                        'SCADA Systems',\n                                        'Predictive Maintenance'\n                                    ]\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-12 w-12 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: currentLanguage === 'ar' ? 'ضمان الجودة' : 'Quality Assurance',\n                                    description: currentLanguage === 'ar' ? 'أنظمة متكاملة لمراقبة الجودة مع مراقبة في الوقت الفعلي ومعايير دولية' : 'Integrated quality control systems with real-time monitoring and international standards',\n                                    features: currentLanguage === 'ar' ? [\n                                        'مراقبة في الوقت الفعلي',\n                                        'معايير ISO',\n                                        'تتبع كامل'\n                                    ] : [\n                                        'Real-time Monitoring',\n                                        'ISO Standards',\n                                        'Full Traceability'\n                                    ]\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                        className: \"h-12 w-12 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: currentLanguage === 'ar' ? 'شهادات عالمية' : 'Global Certifications',\n                                    description: currentLanguage === 'ar' ? 'معتمدة من أهم المنظمات العالمية مع ضمان الامتثال للمعايير الدولية' : 'Certified by leading global organizations with guaranteed compliance to international standards',\n                                    features: currentLanguage === 'ar' ? [\n                                        'شهادات ISO',\n                                        'معايير FDA',\n                                        'امتثال GMP'\n                                    ] : [\n                                        'ISO Certified',\n                                        'FDA Standards',\n                                        'GMP Compliance'\n                                    ]\n                                }\n                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                    animation: \"lift\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"p-8 text-center h-full group hover:shadow-xl transition-shadow duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\", isDarkMode ? \"bg-primary-900/20\" : \"bg-primary-50\"),\n                                                children: feature.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 996,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-4 text-slate-900 dark:text-white\",\n                                                children: feature.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1002,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600 dark:text-slate-300 mb-6 leading-relaxed\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: feature.features.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center text-sm text-slate-500 dark:text-slate-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-500 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 1007,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            item\n                                                        ]\n                                                    }, idx, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1006,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1004,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"p-8 text-center\", isDarkMode ? \"bg-primary-900/20\" : \"bg-primary-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold mb-8 text-slate-900 dark:text-white\",\n                                        children: currentLanguage === 'ar' ? 'أداء مثبت' : 'Proven Performance'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-primary-600 mb-2\",\n                                                        children: \"99.5%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                        children: currentLanguage === 'ar' ? 'معدل التشغيل' : 'Uptime Rate'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-primary-600 mb-2\",\n                                                        children: \"30%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                        children: currentLanguage === 'ar' ? 'توفير في التكاليف' : 'Cost Savings'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-primary-600 mb-2\",\n                                                        children: \"50+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1040,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                        children: currentLanguage === 'ar' ? 'دولة' : 'Countries'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1041,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-primary-600 mb-2\",\n                                                        children: \"24/7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                        children: currentLanguage === 'ar' ? 'دعم فني' : 'Technical Support'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1026,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 1019,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 1018,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                    lineNumber: 941,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                lineNumber: 940,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 bg-gradient-to-br from-primary-600 via-primary-500 to-primary-700 text-white overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-[url('/images/industrial-pattern.svg')] bg-repeat\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 1061,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 1060,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.5,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold mb-6\",\n                                        children: currentLanguage === 'ar' ? 'هل أنت مستعد لتحويل إنتاجك؟' : 'Ready to Transform Your Production?'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1067,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl mb-8 text-primary-50 max-w-3xl mx-auto leading-relaxed\",\n                                        children: currentLanguage === 'ar' ? 'انضم إلى أكثر من 500 شركة حول العالم تثق في حلولنا الصناعية المتطورة. احصل على استشارة مجانية من خبرائنا اليوم.' : 'Join over 500 companies worldwide who trust our advanced industrial solutions. Get a free consultation from our experts today.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1070,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"h-6 w-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 1080,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1079,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold mb-2\",\n                                                        children: currentLanguage === 'ar' ? 'استشارة مجانية' : 'Free Consultation'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-primary-100\",\n                                                        children: currentLanguage === 'ar' ? 'تحدث مع خبرائنا' : 'Talk to our experts'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1078,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                            className: \"h-6 w-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 1091,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1090,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold mb-2\",\n                                                        children: currentLanguage === 'ar' ? 'زيارة موقعية' : 'Site Visit'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-primary-100\",\n                                                        children: currentLanguage === 'ar' ? 'تقييم احتياجاتك' : 'Assess your needs'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1096,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1089,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                            className: \"h-6 w-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 1102,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1101,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold mb-2\",\n                                                        children: currentLanguage === 'ar' ? 'كتالوج مجاني' : 'Free Catalog'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1104,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-primary-100\",\n                                                        children: currentLanguage === 'ar' ? 'احصل على المواصفات' : 'Get specifications'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1107,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1100,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1077,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-center gap-4 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"accent\",\n                                                    size: \"lg\",\n                                                    className: \"px-8 py-4 text-lg font-semibold\",\n                                                    onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/contact\")),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 1121,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'احجز استشارة مجانية' : 'Book Free Consultation'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 1115,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    className: \"px-8 py-4 text-lg font-semibold border-white/30 text-white hover:bg-white/10\",\n                                                    onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/services\")),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 1132,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'تحميل الكتالوج' : 'Download Catalog'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 1126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1125,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-center items-center gap-6 text-primary-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: currentLanguage === 'ar' ? 'متاح الآن للاستشارة' : 'Available now for consultation'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1142,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1140,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1147,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: currentLanguage === 'ar' ? 'استجابة خلال 24 ساعة' : 'Response within 24 hours'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1146,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1139,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 1066,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 1065,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 1064,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                lineNumber: 1058,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n        lineNumber: 254,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductionLinesPage, \"eItUqtpcS1lGa7uqoxyRC41kOPI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _stores_themeStore__WEBPACK_IMPORTED_MODULE_9__.useThemeStore,\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_10__.useLanguageStore,\n        _translations__WEBPACK_IMPORTED_MODULE_11__.useTranslation\n    ];\n});\n_c = ProductionLinesPage;\nvar _c;\n$RefreshReg$(_c, \"ProductionLinesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/pages/production/ProductionLinesPage.tsx\n"));

/***/ })

});