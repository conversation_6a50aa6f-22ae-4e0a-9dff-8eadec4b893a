"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_data_products_ts"],{

/***/ "(app-pages-browser)/./src/data/products.ts":
/*!******************************!*\
  !*** ./src/data/products.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   productCategories: () => (/* binding */ productCategories),\n/* harmony export */   products: () => (/* binding */ products)\n/* harmony export */ });\nconst products = [\n    {\n        id: 'smart-factory-sensor',\n        name: 'Smart Factory IoT Sensor Kit',\n        slug: 'smart-factory-sensor',\n        description: 'Next-generation IoT sensors for real-time manufacturing monitoring and predictive maintenance.',\n        price: 2999.99,\n        compareAtPrice: 3499.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'IoT',\n            'Industry 4.0'\n        ],\n        stock: 50,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 150,\n        specifications: {\n            'Sensor Type': 'Multi-parameter',\n            'Connectivity': 'WiFi, Bluetooth, LoRaWAN',\n            'Battery Life': 'Up to 2 years',\n            'Data Rate': '1 sample/second',\n            'Operating Temperature': '-20°C to 85°C'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'ai-quality-system',\n        name: 'AI-Powered Quality Inspection System',\n        slug: 'ai-quality-system',\n        description: 'Advanced computer vision system for automated quality control in production lines.',\n        price: 8999.99,\n        compareAtPrice: 10999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Quality Control',\n        tags: [\n            'New Arrival',\n            'AI',\n            'Automation'\n        ],\n        stock: 15,\n        featured: true,\n        rating: 4.9,\n        reviewCount: 210,\n        specifications: {\n            'Camera Resolution': '4K Ultra HD',\n            'Processing Speed': 'Up to 100 items/minute',\n            'AI Model': 'Deep Learning CNN',\n            'Integration': 'REST API, MQTT',\n            'Accuracy Rate': '99.9%'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'smart-inventory',\n        name: 'Smart Inventory Management System',\n        slug: 'smart-inventory',\n        description: 'Cloud-based inventory tracking system with real-time analytics and AI-driven forecasting.',\n        price: 4999.99,\n        compareAtPrice: 5999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Warehouse Management',\n        tags: [\n            'New Arrival',\n            'Smart Systems',\n            'Cloud'\n        ],\n        stock: 30,\n        featured: true,\n        rating: 4.6,\n        reviewCount: 95,\n        specifications: {\n            'Storage Capacity': 'Unlimited',\n            'Real-time Tracking': 'Yes',\n            'Mobile App': 'iOS & Android',\n            'Barcode Support': '1D & 2D',\n            'Analytics': 'Advanced ML-based'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'collaborative-robot',\n        name: 'Next-Gen Collaborative Robot',\n        slug: 'collaborative-robot',\n        description: 'Advanced collaborative robot with enhanced safety features and intuitive programming.',\n        price: 29999.99,\n        compareAtPrice: 34999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Robotics',\n        tags: [\n            'New Arrival',\n            'Robotics',\n            'Safety'\n        ],\n        stock: 10,\n        featured: true,\n        rating: 4.8,\n        reviewCount: 75,\n        specifications: {\n            'Payload': 'Up to 10kg',\n            'Reach': '1.3m',\n            'Degrees of Freedom': '6-axis',\n            'Safety Features': 'Force limiting, Vision',\n            'Programming': 'Teach pendant, GUI'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'digital-twin-platform',\n        name: 'Digital Twin Manufacturing Platform',\n        slug: 'digital-twin-platform',\n        description: 'Complete digital twin solution for real-time production monitoring and optimization.',\n        price: 12999.99,\n        compareAtPrice: 15999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'Industry 4.0',\n            'Digital Twin'\n        ],\n        stock: 20,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 110,\n        specifications: {\n            'Simulation': 'Real-time 3D',\n            'Data Integration': 'OPC UA, MQTT',\n            'Analytics': 'Predictive maintenance',\n            'Visualization': 'AR/VR support',\n            'API': 'RESTful, GraphQL'\n        },\n        createdAt: new Date().toISOString()\n    }\n];\nconst productCategories = [\n    {\n        id: 'smart-manufacturing',\n        name: {\n            en: 'Smart Manufacturing',\n            ar: 'التصنيع الذكي'\n        },\n        description: {\n            en: 'Industry 4.0 solutions for modern factories',\n            ar: 'حلول الصناعة 4.0 للمصانع الحديثة'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'quality-control',\n        name: {\n            en: 'Quality Control',\n            ar: 'مراقبة الجودة'\n        },\n        description: {\n            en: 'Advanced quality assurance systems',\n            ar: 'أنظمة ضمان الجودة المتقدمة'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'warehouse-management',\n        name: {\n            en: 'Warehouse Management',\n            ar: 'إدارة المستودعات'\n        },\n        description: {\n            en: 'Smart warehouse and inventory solutions',\n            ar: 'حلول المستودعات والمخزون الذكية'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'robotics',\n        name: {\n            en: 'Robotics',\n            ar: 'الروبوتات'\n        },\n        description: {\n            en: 'Next-generation industrial robots',\n            ar: 'الجيل القادم من الروبوتات الصناعية'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'digital-solutions',\n        name: {\n            en: 'Digital Solutions',\n            ar: 'الحلول الرقمية'\n        },\n        description: {\n            en: 'Digital transformation tools',\n            ar: 'أدوات التحول الرقمي'\n        },\n        image: '/images/placeholder-light.svg'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/products.ts\n"));

/***/ })

}]);