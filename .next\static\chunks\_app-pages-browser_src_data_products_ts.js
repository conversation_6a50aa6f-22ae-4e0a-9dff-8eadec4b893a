"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_data_products_ts"],{

/***/ "(app-pages-browser)/./src/data/products.ts":
/*!******************************!*\
  !*** ./src/data/products.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   productCategories: () => (/* binding */ productCategories),\n/* harmony export */   products: () => (/* binding */ products)\n/* harmony export */ });\nvar products = [\n    {\n        id: 'smart-factory-sensor',\n        name: 'Smart Factory IoT Sensor Kit',\n        slug: 'smart-factory-sensor',\n        description: 'Next-generation IoT sensors for real-time manufacturing monitoring and predictive maintenance.',\n        price: 2999.99,\n        compareAtPrice: 3499.99,\n        images: [\n            '/images/product-automation.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'IoT',\n            'Industry 4.0'\n        ],\n        stock: 50,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 150,\n        specifications: {\n            'Sensor Type': 'Multi-parameter',\n            'Connectivity': 'WiFi, Bluetooth, LoRaWAN',\n            'Battery Life': 'Up to 2 years',\n            'Data Rate': '1 sample/second',\n            'Operating Temperature': '-20°C to 85°C'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'ai-quality-system',\n        name: 'AI-Powered Quality Inspection System',\n        slug: 'ai-quality-system',\n        description: 'Advanced computer vision system for automated quality control in production lines.',\n        price: 8999.99,\n        compareAtPrice: 10999.99,\n        images: [\n            '/images/product-packaging.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Quality Control',\n        tags: [\n            'New Arrival',\n            'AI',\n            'Automation'\n        ],\n        stock: 15,\n        featured: true,\n        rating: 4.9,\n        reviewCount: 210,\n        specifications: {\n            'Camera Resolution': '4K Ultra HD',\n            'Processing Speed': 'Up to 100 items/minute',\n            'AI Model': 'Deep Learning CNN',\n            'Integration': 'REST API, MQTT',\n            'Accuracy Rate': '99.9%'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'smart-inventory',\n        name: 'Smart Inventory Management System',\n        slug: 'smart-inventory',\n        description: 'Cloud-based inventory tracking system with real-time analytics and AI-driven forecasting.',\n        price: 4999.99,\n        compareAtPrice: 5999.99,\n        images: [\n            '/images/product-manufacturing.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Warehouse Management',\n        tags: [\n            'New Arrival',\n            'Smart Systems',\n            'Cloud'\n        ],\n        stock: 30,\n        featured: true,\n        rating: 4.6,\n        reviewCount: 95,\n        specifications: {\n            'Storage Capacity': 'Unlimited',\n            'Real-time Tracking': 'Yes',\n            'Mobile App': 'iOS & Android',\n            'Barcode Support': '1D & 2D',\n            'Analytics': 'Advanced ML-based'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'collaborative-robot',\n        name: 'Next-Gen Collaborative Robot',\n        slug: 'collaborative-robot',\n        description: 'Advanced collaborative robot with enhanced safety features and intuitive programming.',\n        price: 29999.99,\n        compareAtPrice: 34999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Robotics',\n        tags: [\n            'New Arrival',\n            'Robotics',\n            'Safety'\n        ],\n        stock: 10,\n        featured: true,\n        rating: 4.8,\n        reviewCount: 75,\n        specifications: {\n            'Payload': 'Up to 10kg',\n            'Reach': '1.3m',\n            'Degrees of Freedom': '6-axis',\n            'Safety Features': 'Force limiting, Vision',\n            'Programming': 'Teach pendant, GUI'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'digital-twin-platform',\n        name: 'Digital Twin Manufacturing Platform',\n        slug: 'digital-twin-platform',\n        description: 'Complete digital twin solution for real-time production monitoring and optimization.',\n        price: 12999.99,\n        compareAtPrice: 15999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'Industry 4.0',\n            'Digital Twin'\n        ],\n        stock: 20,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 110,\n        specifications: {\n            'Simulation': 'Real-time 3D',\n            'Data Integration': 'OPC UA, MQTT',\n            'Analytics': 'Predictive maintenance',\n            'Visualization': 'AR/VR support',\n            'API': 'RESTful, GraphQL'\n        },\n        createdAt: new Date().toISOString()\n    }\n];\nvar productCategories = [\n    {\n        id: 'smart-manufacturing',\n        name: {\n            en: 'Smart Manufacturing',\n            ar: 'التصنيع الذكي'\n        },\n        description: {\n            en: 'Industry 4.0 solutions for modern factories',\n            ar: 'حلول الصناعة 4.0 للمصانع الحديثة'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'quality-control',\n        name: {\n            en: 'Quality Control',\n            ar: 'مراقبة الجودة'\n        },\n        description: {\n            en: 'Advanced quality assurance systems',\n            ar: 'أنظمة ضمان الجودة المتقدمة'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'warehouse-management',\n        name: {\n            en: 'Warehouse Management',\n            ar: 'إدارة المستودعات'\n        },\n        description: {\n            en: 'Smart warehouse and inventory solutions',\n            ar: 'حلول المستودعات والمخزون الذكية'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'robotics',\n        name: {\n            en: 'Robotics',\n            ar: 'الروبوتات'\n        },\n        description: {\n            en: 'Next-generation industrial robots',\n            ar: 'الجيل القادم من الروبوتات الصناعية'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'digital-solutions',\n        name: {\n            en: 'Digital Solutions',\n            ar: 'الحلول الرقمية'\n        },\n        description: {\n            en: 'Digital transformation tools',\n            ar: 'أدوات التحول الرقمي'\n        },\n        image: '/images/placeholder-light.svg'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/products.ts\n"));

/***/ })

}]);