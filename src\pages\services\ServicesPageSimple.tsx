'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Phone, Mail, ArrowRight } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { services } from '../../data/services';
import { useThemeStore } from '../../stores/themeStore';
import { useLanguageStore } from '../../stores/languageStore';
import { cn } from '../../lib/utils';

export default function ServicesPageSimple() {
  const router = useRouter();
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();

  return (
    <div>
      {/* Simple Hero Section */}
      <section className={cn(
        "py-16",
        isDarkMode ? "bg-slate-900" : "bg-white"
      )}>
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className={cn(
              "text-4xl md:text-5xl font-bold mb-6",
              isDarkMode ? "text-white" : "text-slate-900"
            )}>
              {language === 'ar' ? 'خدمات الأعمال' : 'Business Services'}
            </h1>
            <p className={cn(
              "text-lg md:text-xl mb-8",
              isDarkMode ? "text-slate-300" : "text-slate-600"
            )}>
              {language === 'ar'
                ? 'خدمات دعم شاملة لتبسيط عملياتك وتعزيز كفاءة أعمالك.'
                : 'Comprehensive support services to streamline your operations and enhance business efficiency.'}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                size="lg"
                variant="primary"
                onClick={() => router.push(`/${language}/contact`)}
              >
                <Phone className="mr-2 h-5 w-5" />
                {language === 'ar' ? 'اتصل بنا' : 'Contact Us'}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className={cn("py-16", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              {language === 'ar' ? 'خدماتنا' : 'Our Services'}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => (
              <Card key={service.id} className="h-full">
                <CardHeader className="text-center">
                  <CardTitle className="text-xl mb-2 text-slate-900 dark:text-white">
                    {language === 'ar' ? service.name_ar || service.name : service.name}
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col h-full">
                  <p className="text-slate-600 dark:text-slate-300 mb-6 flex-grow">
                    {language === 'ar' ? service.description_ar || service.description : service.description}
                  </p>
                  <div className="flex gap-2 mt-auto">
                    <Button
                      variant="primary"
                      className="flex-1"
                      onClick={() => router.push(`/${language}/contact`)}
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      {language === 'ar' ? 'احجز الخدمة' : 'Book Service'}
                    </Button>
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => router.push(`/${language}/services/${service.slug}`)}
                    >
                      {language === 'ar' ? 'معرفة المزيد' : 'Learn More'}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className={cn("py-16", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
              {language === 'ar' ? 'هل أنت مستعد لتعزيز عمليات عملك؟' : 'Ready to Enhance Your Business Operations?'}
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 mb-8">
              {language === 'ar'
                ? 'اتصل بفريقنا لمناقشة كيف يمكن لخدماتنا تلبية احتياجات عملك المحددة.'
                : 'Contact our team to discuss how our services can address your specific business needs.'}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                size="lg"
                variant="primary"
                onClick={() => router.push(`/${language}/contact`)}
              >
                <Phone className="mr-2 h-5 w-5" />
                {language === 'ar' ? 'اتصل بنا' : 'Contact Us'}
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => router.push(`/${language}/contact`)}
              >
                <Mail className="mr-2 h-5 w-5" />
                {language === 'ar' ? 'طلب عرض سعر' : 'Request Quote'}
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
