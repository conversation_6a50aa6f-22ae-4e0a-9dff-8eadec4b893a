import { useState, useEffect } from 'react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card } from '../ui/Card';
import { Send, Upload, CheckCircle } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';
import { useTranslation } from '../../translations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';
import { Product } from '../../types/index';

interface WholesaleQuoteFormProps {
  onClose: () => void;
  isCustomProduct?: boolean;
  serviceName?: string;
  product?: Product;
  selectedProduct?: Product;
  initialQuantity?: number;
}

export function WholesaleQuoteForm({ onClose, isCustomProduct = false, serviceName, product, selectedProduct, initialQuantity }: WholesaleQuoteFormProps) {
  const [formData, setFormData] = useState({
    companyName: '',
    contactName: '',
    email: '',
    phone: '',
    productType: '',
    specifications: '',
    targetQuantity: '',
    targetPrice: '',
    timeline: '',
    additionalNotes: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { user } = useAuthStore();
  const { t } = useTranslation();
  const { isDarkMode } = useThemeStore();

  // تعبئة بيانات المستخدم تلقائيًا إذا كان مسجل الدخول
  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        contactName: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : prev.contactName,
        email: user.email || prev.email,
      }));
    }
  }, [user]);

  // Pre-fill productType if product is provided
  useEffect(() => {
    const productToUse = selectedProduct || product;
    if (productToUse) {
      setFormData(prev => ({
        ...prev,
        productType: productToUse.name || prev.productType,
        specifications: productToUse.specifications ?
          Object.entries(productToUse.specifications)
            .map(([key, value]) => `${key}: ${value}`)
            .join('\n') :
          prev.specifications,
        targetQuantity: initialQuantity ? initialQuantity.toString() : prev.targetQuantity,
      }));
    }
  }, [product, selectedProduct, initialQuantity]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    try {
      // التحقق من حالة المصادقة
      if (!user) {
        setError(t('wholesale.authRequired'));
        setIsSubmitting(false);
        return;
      }

      // محاكاة إرسال النموذج
      console.log('Quote request submitted:', formData);

      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsSubmitted(true);

      // إغلاق النموذج بعد فترة قصيرة
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      console.error('Error submitting form:', err);
      setError(t('wholesale.submitError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // إذا تم إرسال النموذج بنجاح
  if (isSubmitted) {
    return (
      <Card className={cn("p-6", isDarkMode ? "bg-slate-800" : "bg-white")}>
        <div className="text-center py-8">
          <div className={cn(
            "w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4",
            isDarkMode ? "bg-green-900/20" : "bg-green-100"
          )}>
            <CheckCircle className={cn("h-8 w-8", isDarkMode ? "text-green-400" : "text-green-600")} />
          </div>
          <h3 className={cn("text-xl font-semibold mb-2", isDarkMode ? "text-white" : "text-slate-900")}>
            {t('wholesale.requestSubmitted')}
          </h3>
          <p className={cn("mb-6", isDarkMode ? "text-slate-300" : "text-slate-600")}>
            {t('wholesale.thankYou')}
          </p>
          <Button onClick={onClose}>
            {t('wholesale.close')}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card className={cn("p-6", isDarkMode ? "bg-slate-800" : "bg-white")}>
      <h3 className={cn("text-xl font-semibold mb-4", isDarkMode ? "text-white" : "text-slate-900")}>
        {isCustomProduct ? t('wholesale.customProductTitle') : t('wholesale.wholesaleTitle')}
      </h3>

      {error && (
        <div className={cn(
          "p-3 rounded-md mb-4",
          isDarkMode ? "bg-red-900/20 text-red-300" : "bg-red-50 text-red-600"
        )}>
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className={cn("block text-sm font-medium mb-1", isDarkMode ? "text-slate-300" : "text-slate-700")}>
              {t('wholesale.companyName')}
            </label>
            <Input
              name="companyName"
              value={formData.companyName}
              onChange={handleChange}
              required
            />
          </div>

          <div>
            <label className={cn("block text-sm font-medium mb-1", isDarkMode ? "text-slate-300" : "text-slate-700")}>
              {t('wholesale.contactName')}
            </label>
            <Input
              name="contactName"
              value={formData.contactName}
              onChange={handleChange}
              required
            />
          </div>

          <div>
            <label className={cn("block text-sm font-medium mb-1", isDarkMode ? "text-slate-300" : "text-slate-700")}>
              {t('wholesale.email')}
            </label>
            <Input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </div>

          <div>
            <label className={cn("block text-sm font-medium mb-1", isDarkMode ? "text-slate-300" : "text-slate-700")}>
              {t('wholesale.phone')}
            </label>
            <Input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              required
            />
          </div>
        </div>

        <div>
          <label className={cn("block text-sm font-medium mb-1", isDarkMode ? "text-slate-300" : "text-slate-700")}>
            {t('wholesale.productType')}
          </label>
          <Input
            name="productType"
            value={formData.productType}
            onChange={handleChange}
            required
            placeholder={t('wholesale.productTypePlaceholder')}
          />
        </div>

        <div>
          <label className={cn("block text-sm font-medium mb-1", isDarkMode ? "text-slate-300" : "text-slate-700")}>
            {t('wholesale.specifications')}
          </label>
          <textarea
            name="specifications"
            value={formData.specifications}
            onChange={handleChange}
            required
            className={cn(
              "w-full min-h-[100px] px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
              isDarkMode ? "bg-slate-700 border-slate-600 text-white" : "bg-white border-slate-300"
            )}
            placeholder={t('wholesale.specificationsPlaceholder')}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className={cn("block text-sm font-medium mb-1", isDarkMode ? "text-slate-300" : "text-slate-700")}>
              {t('wholesale.targetQuantity')}
            </label>
            <Input
              name="targetQuantity"
              value={formData.targetQuantity}
              onChange={handleChange}
              required
              placeholder={t('wholesale.targetQuantityPlaceholder')}
            />
          </div>

          <div>
            <label className={cn("block text-sm font-medium mb-1", isDarkMode ? "text-slate-300" : "text-slate-700")}>
              {t('wholesale.targetPrice')}
            </label>
            <Input
              name="targetPrice"
              value={formData.targetPrice}
              onChange={handleChange}
              placeholder={t('wholesale.targetPricePlaceholder')}
            />
          </div>
        </div>

        <div>
          <label className={cn("block text-sm font-medium mb-1", isDarkMode ? "text-slate-300" : "text-slate-700")}>
            {t('wholesale.timeline')}
          </label>
          <Input
            name="timeline"
            value={formData.timeline}
            onChange={handleChange}
            required
            placeholder={t('wholesale.timelinePlaceholder')}
          />
        </div>

        <div>
          <label className={cn("block text-sm font-medium mb-1", isDarkMode ? "text-slate-300" : "text-slate-700")}>
            {t('wholesale.additionalNotes')}
          </label>
          <textarea
            name="additionalNotes"
            value={formData.additionalNotes}
            onChange={handleChange}
            className={cn(
              "w-full min-h-[100px] px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",
              isDarkMode ? "bg-slate-700 border-slate-600 text-white" : "bg-white border-slate-300"
            )}
            placeholder={t('wholesale.additionalNotesPlaceholder')}
          />
        </div>

        {isCustomProduct && (
          <div className="border-t pt-4">
            <label className={cn("block text-sm font-medium mb-2", isDarkMode ? "text-slate-300" : "text-slate-700")}>
              {t('wholesale.uploadFiles')}
            </label>
            <div className={cn(
              "border-2 border-dashed rounded-lg p-4 text-center",
              isDarkMode ? "border-slate-600 bg-slate-700/30" : "border-slate-300 bg-slate-50"
            )}>
              <Upload className={cn("mx-auto h-8 w-8 mb-2", isDarkMode ? "text-slate-400" : "text-slate-400")} />
              <p className={cn("text-sm", isDarkMode ? "text-slate-300" : "text-slate-600")}>
                {t('wholesale.dropFilesHere')}
              </p>
              <input
                type="file"
                multiple
                className="hidden"
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                id="file-upload"
              />
              <Button
                type="button"
                variant="outline"
                className="mt-2"
                onClick={() => {
                  const fileInput = document.getElementById('file-upload') as HTMLInputElement;
                  if (fileInput) fileInput.click();
                }}
              >
                {t('wholesale.selectFiles')}
              </Button>
            </div>
          </div>
        )}

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onClose}>
            {t('wholesale.cancel')}
          </Button>
          <Button
            type="submit"
            className="flex items-center gap-2"
            isLoading={isSubmitting}
            disabled={isSubmitting}
          >
            {!isSubmitting && <Send className="w-4 h-4" />}
            {isSubmitting ? t('wholesale.submitting') : t('wholesale.submitRequest')}
          </Button>
        </div>
      </form>
    </Card>
  );
}