'use client';

import { useState, useEffect } from 'react';
import {
  Filter,
  Search,
  X,
  ChevronDown,
  ChevronUp,
  Star,
  Tag,
  Sliders,
  RefreshCw,
  Check
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Slider } from '../ui/Slider';
import { Badge } from '../ui/Badge';
import { Tooltip } from '../ui/Tooltip';
import { HoverAnimation } from '../ui/animations';
import { formatCurrency, cn } from '../../lib/utils';
import { ProductCategory, ProductFiltersState } from '../../types/index';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';

interface EnhancedProductFiltersProps {
  filters: ProductFiltersState;
  setFilters: React.Dispatch<React.SetStateAction<ProductFiltersState>>;
  resetFilters: () => void;
  maxPrice: number;
  productCategories: ProductCategory[];
  showMobileFilters: boolean;
  setShowMobileFilters: (show: boolean) => void;
  activeFiltersCount: number;
  tags?: string[];
}

export function EnhancedProductFilters({
  filters,
  setFilters,
  resetFilters,
  maxPrice,
  productCategories,
  showMobileFilters,
  setShowMobileFilters,
  activeFiltersCount,
  tags = []
}: EnhancedProductFiltersProps) {
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    price: true,
    availability: true,
    rating: true,
    tags: true
  });
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [priceRange, setPriceRange] = useState([filters.priceRange.min, filters.priceRange.max]);

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // تحديث الفلاتر عند تغيير التصنيفات المحددة
  useEffect(() => {
    if (selectedTags.length > 0) {
      setFilters(prev => ({ ...prev, tags: selectedTags }));
    } else {
      setFilters(prev => {
        const newFilters = { ...prev };
        delete newFilters.tags;
        return newFilters;
      });
    }
  }, [selectedTags, setFilters]);

  // تحديث الفلاتر عند تغيير التقييم المحدد
  useEffect(() => {
    if (selectedRating !== null) {
      setFilters(prev => ({ ...prev, rating: selectedRating }));
    } else {
      setFilters(prev => {
        const newFilters = { ...prev };
        delete newFilters.rating;
        return newFilters;
      });
    }
  }, [selectedRating, setFilters]);

  // تحديث الفلاتر عند تغيير نطاق السعر
  useEffect(() => {
    setFilters(prev => ({
      ...prev,
      priceRange: { min: priceRange[0], max: priceRange[1] }
    }));
  }, [priceRange, setFilters]);

  // تبديل حالة توسيع القسم
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // إضافة أو إزالة وسم من الوسوم المحددة
  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  // تحديد التقييم
  const handleRatingSelect = (rating: number) => {
    setSelectedRating(prev => prev === rating ? null : rating);
  };

  // إعادة تعيين جميع الفلاتر
  const handleResetFilters = () => {
    setSelectedTags([]);
    setSelectedRating(null);
    setPriceRange([0, maxPrice]);
    resetFilters();
  };

  return (
    <div className="mb-8">
      {/* رأس الفلاتر */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
            {t('shop.filters.title')}
          </h2>
          {activeFiltersCount > 0 && (
            <Badge variant="primary" className="text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          {activeFiltersCount > 0 && (
            <Tooltip content={t('shop.filters.reset')}>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleResetFilters}
                className="text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">
                  {t('shop.filters.reset')}
                </span>
              </Button>
            </Tooltip>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowMobileFilters(!showMobileFilters)}
            className="md:hidden"
          >
            <Filter className="h-4 w-4 mr-1" />
            <span>
              {showMobileFilters
                ? t('shop.filters.hideFilters')
                : t('shop.filters.showFilters')}
            </span>
          </Button>
        </div>
      </div>

      {/* محتوى الفلاتر */}
      <div className={cn(
        "bg-white dark:bg-slate-800 rounded-lg shadow-lg overflow-hidden transition-all duration-300 border border-slate-200 dark:border-slate-700",
        showMobileFilters ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0 md:max-h-[2000px] md:opacity-100"
      )}>
        <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* فلاتر نشطة */}
          {activeFiltersCount > 0 && (
            <div className="col-span-1 md:col-span-2 lg:col-span-3 mb-4">
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  {t('shop.filters.activeFilters')}:
                </span>
                {filters.category !== 'all' && (
                  <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1">
                    <span>
                      {currentLanguage === 'ar' ? 'الفئة: ' : 'Category: '}
                      {productCategories.find(c => c.id === filters.category)?.name[currentLanguage] || filters.category}
                    </span>
                    <X
                      className="h-3 w-3 ml-1 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        setFilters(prev => ({ ...prev, category: 'all' }));
                      }}
                    />
                  </Badge>
                )}
                {filters.searchQuery && (
                  <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1">
                    <span>
                      {currentLanguage === 'ar' ? 'بحث: ' : 'Search: '}
                      {filters.searchQuery}
                    </span>
                    <X
                      className="h-3 w-3 ml-1 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        setFilters(prev => ({ ...prev, searchQuery: '' }));
                      }}
                    />
                  </Badge>
                )}
                {(filters.priceRange.min > 0 || filters.priceRange.max < maxPrice) && (
                  <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1">
                    <span>
                      {currentLanguage === 'ar' ? 'السعر: ' : 'Price: '}
                      {formatCurrency(filters.priceRange.min)} - {formatCurrency(filters.priceRange.max)}
                    </span>
                    <X
                      className="h-3 w-3 ml-1 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        setPriceRange([0, maxPrice]);
                        setFilters(prev => ({ ...prev, priceRange: { min: 0, max: maxPrice } }));
                      }}
                    />
                  </Badge>
                )}
                {filters.inStock && (
                  <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1">
                    <span>{currentLanguage === 'ar' ? 'متوفر في المخزون' : 'In Stock'}</span>
                    <X
                      className="h-3 w-3 ml-1 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        setFilters(prev => ({ ...prev, inStock: false }));
                      }}
                    />
                  </Badge>
                )}
                {filters.onSale && (
                  <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1">
                    <span>{currentLanguage === 'ar' ? 'العروض' : 'On Sale'}</span>
                    <X
                      className="h-3 w-3 ml-1 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        setFilters(prev => ({ ...prev, onSale: false }));
                      }}
                    />
                  </Badge>
                )}
                {filters.featured && (
                  <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1">
                    <span>{currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured'}</span>
                    <X
                      className="h-3 w-3 ml-1 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        setFilters(prev => ({ ...prev, featured: false }));
                      }}
                    />
                  </Badge>
                )}
                {filters.newArrivals && (
                  <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1">
                    <span>{currentLanguage === 'ar' ? 'وصل حديثاً' : 'New Arrivals'}</span>
                    <X
                      className="h-3 w-3 ml-1 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        setFilters(prev => ({ ...prev, newArrivals: false }));
                      }}
                    />
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleResetFilters}
                  className="text-slate-600 dark:text-slate-400 hover:text-primary-600 dark:hover:text-primary-400 ml-2"
                >
                  <RefreshCw className="h-3.5 w-3.5 mr-1" />
                  <span>{t('shop.filters.clearAll')}</span>
                </Button>
              </div>
            </div>
          )}
          {/* البحث */}
          <div className="col-span-1 md:col-span-2 lg:col-span-3">
            <div className="relative">
              <Input
                type="text"
                placeholder={t('shop.search.placeholder')}
                value={filters.searchQuery}
                onChange={(e) => setFilters({ ...filters, searchQuery: e.target.value })}
                className={cn(
                  "pl-10 pr-10 py-2.5 bg-slate-50 dark:bg-slate-700 border-slate-200 dark:border-slate-600",
                  "focus:border-primary-500 focus:ring-primary-500 dark:focus:border-primary-400 dark:focus:ring-primary-400"
                )}
              />
              <Search className={cn(
                "absolute top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500",
                isRTL ? "right-3" : "left-3"
              )} />
              {filters.searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setFilters({ ...filters, searchQuery: '' })}
                  className={cn(
                    "absolute top-1/2 transform -translate-y-1/2 h-8 w-8 p-1.5 text-slate-400 hover:text-slate-600",
                    isRTL ? "left-3" : "right-3"
                  )}
                  aria-label={t('shop.search.clear')}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* الفئات */}
          <div className="col-span-1">
            <div className="border-b border-slate-200 dark:border-slate-700 pb-2 mb-3">
              <button
                onClick={() => toggleSection('categories')}
                className="flex items-center justify-between w-full text-left font-medium text-slate-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
              >
                <span className="flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  {t('shop.filters.categories')}
                </span>
                {expandedSections.categories ? (
                  <ChevronUp className="h-4 w-4 text-slate-500" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-slate-500" />
                )}
              </button>
            </div>
            {expandedSections.categories && (
              <div className="space-y-1 mt-3 max-h-60 overflow-y-auto pr-2">
                {/* جميع الفئات */}
                <HoverAnimation animation="scale">
                  <div
                    className={cn(
                      "flex items-center px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200",
                      "border border-transparent hover:border-slate-200 dark:hover:border-slate-600",
                      filters.category === 'all'
                        ? "bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 border-primary-200 dark:border-primary-800 shadow-sm"
                        : "hover:bg-slate-50 dark:hover:bg-slate-700/50"
                    )}
                    onClick={() => setFilters({ ...filters, category: 'all' })}
                  >
                    <div className={cn(
                      "flex items-center justify-center w-5 h-5 rounded-full border-2 mr-3 transition-colors",
                      filters.category === 'all'
                        ? "border-primary-500 bg-primary-500"
                        : "border-slate-300 dark:border-slate-600"
                    )}>
                      {filters.category === 'all' && (
                        <Check className="h-3 w-3 text-white" />
                      )}
                    </div>
                    <div className="flex-1">
                      <span className="font-medium">{t('shop.filters.allCategories')}</span>
                      <div className="text-xs text-slate-500 dark:text-slate-400 mt-0.5">
                        {productCategories.length} {currentLanguage === 'ar' ? 'فئة' : 'categories'}
                      </div>
                    </div>
                  </div>
                </HoverAnimation>

                {/* فئات المنتجات */}
                {productCategories.map(category => (
                  <HoverAnimation key={category.id} animation="scale">
                    <div
                      className={cn(
                        "flex items-center px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200",
                        "border border-transparent hover:border-slate-200 dark:hover:border-slate-600",
                        filters.category === category.id
                          ? "bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 border-primary-200 dark:border-primary-800 shadow-sm"
                          : "hover:bg-slate-50 dark:hover:bg-slate-700/50"
                      )}
                      onClick={() => setFilters({ ...filters, category: category.id })}
                    >
                      <div className={cn(
                        "flex items-center justify-center w-5 h-5 rounded-full border-2 mr-3 transition-colors",
                        filters.category === category.id
                          ? "border-primary-500 bg-primary-500"
                          : "border-slate-300 dark:border-slate-600"
                      )}>
                        {filters.category === category.id && (
                          <Check className="h-3 w-3 text-white" />
                        )}
                      </div>
                      <div className="flex-1">
                        <span className="font-medium">{category.name[currentLanguage]}</span>
                        <div className="text-xs text-slate-500 dark:text-slate-400 mt-0.5">
                          {category.description?.[currentLanguage] ||
                            (currentLanguage === 'ar' ? 'منتجات متنوعة' : 'Various products')}
                        </div>
                      </div>
                      {category.icon && (
                        <div className="w-6 h-6 ml-2 opacity-60">
                          <img src={category.icon} alt="" className="w-full h-full object-contain" />
                        </div>
                      )}
                    </div>
                  </HoverAnimation>
                ))}
              </div>
            )}
          </div>

          {/* نطاق السعر */}
          <div className="col-span-1">
            <div className="border-b border-slate-200 dark:border-slate-700 pb-2 mb-3">
              <button
                onClick={() => toggleSection('price')}
                className="flex items-center justify-between w-full text-left font-medium text-slate-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
              >
                <span className="flex items-center gap-2">
                  <Sliders className="h-4 w-4" />
                  {t('shop.filters.priceRange')}
                </span>
                {expandedSections.price ? (
                  <ChevronUp className="h-4 w-4 text-slate-500" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-slate-500" />
                )}
              </button>
            </div>
            {expandedSections.price && (
              <div className="mt-4 px-2">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-slate-600 dark:text-slate-400">
                    {formatCurrency(priceRange[0])}
                  </span>
                  <span className="text-sm text-slate-600 dark:text-slate-400">
                    {formatCurrency(priceRange[1])}
                  </span>
                </div>
                <Slider
                  min={0}
                  max={maxPrice}
                  step={1}
                  value={priceRange}
                  onValueChange={setPriceRange}
                  className="my-4"
                />
                <div className="flex items-center justify-between gap-4 mt-4">
                  <Input
                    type="number"
                    min={0}
                    max={priceRange[1]}
                    value={priceRange[0]}
                    onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                    className="w-full text-sm"
                  />
                  <span className="text-slate-400">-</span>
                  <Input
                    type="number"
                    min={priceRange[0]}
                    max={maxPrice}
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || maxPrice])}
                    className="w-full text-sm"
                  />
                </div>
              </div>
            )}
          </div>

          {/* التوفر والميزات */}
          <div className="col-span-1">
            <div className="border-b border-slate-200 dark:border-slate-700 pb-2 mb-3">
              <button
                onClick={() => toggleSection('availability')}
                className="flex items-center justify-between w-full text-left font-medium text-slate-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
              >
                <span className="flex items-center gap-2">
                  <Check className="h-4 w-4" />
                  {t('shop.filters.availability')}
                </span>
                {expandedSections.availability ? (
                  <ChevronUp className="h-4 w-4 text-slate-500" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-slate-500" />
                )}
              </button>
            </div>
            {expandedSections.availability && (
              <div className="space-y-2 mt-3 px-2">
                <HoverAnimation animation="scale">
                  <label className="flex items-center p-2 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/50 cursor-pointer transition-colors">
                    <input
                      id="inStock"
                      type="checkbox"
                      checked={filters.inStock}
                      onChange={(e) => setFilters({ ...filters, inStock: e.target.checked })}
                      className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500 focus:ring-2"
                    />
                    <span className="ml-3 text-sm text-slate-700 dark:text-slate-300 font-medium">
                      {t('shop.filters.inStockOnly')}
                    </span>
                  </label>
                </HoverAnimation>

                <HoverAnimation animation="scale">
                  <label className="flex items-center p-2 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/50 cursor-pointer transition-colors">
                    <input
                      id="onSale"
                      type="checkbox"
                      checked={filters.onSale}
                      onChange={(e) => setFilters({ ...filters, onSale: e.target.checked })}
                      className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500 focus:ring-2"
                    />
                    <span className="ml-3 text-sm text-slate-700 dark:text-slate-300 font-medium">
                      {t('shop.filters.onSaleOnly')}
                    </span>
                  </label>
                </HoverAnimation>

                <HoverAnimation animation="scale">
                  <label className="flex items-center p-2 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/50 cursor-pointer transition-colors">
                    <input
                      id="featured"
                      type="checkbox"
                      checked={filters.featured}
                      onChange={(e) => setFilters({ ...filters, featured: e.target.checked })}
                      className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500 focus:ring-2"
                    />
                    <span className="ml-3 text-sm text-slate-700 dark:text-slate-300 font-medium">
                      {t('shop.filters.featuredOnly')}
                    </span>
                  </label>
                </HoverAnimation>

                <HoverAnimation animation="scale">
                  <label className="flex items-center p-2 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/50 cursor-pointer transition-colors">
                    <input
                      id="newArrivals"
                      type="checkbox"
                      checked={filters.newArrivals}
                      onChange={(e) => setFilters({ ...filters, newArrivals: e.target.checked })}
                      className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500 focus:ring-2"
                    />
                    <span className="ml-3 text-sm text-slate-700 dark:text-slate-300 font-medium">
                      {t('shop.filters.newArrivalsOnly')}
                    </span>
                  </label>
                </HoverAnimation>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
