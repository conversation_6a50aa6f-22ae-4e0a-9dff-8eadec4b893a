'use client';

import { useState, useEffect } from 'react';
import {
  Filter,
  Search,
  X,
  ChevronDown,
  ChevronUp,
  Star,
  Tag,
  Sliders,
  RefreshCw,
  Check
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Slider } from '../ui/Slider';
import { Badge } from '../ui/Badge';
import { Tooltip } from '../ui/Tooltip';
import { HoverAnimation } from '../ui/animations';
import { formatCurrency, cn } from '../../lib/utils';
import { ProductCategory, ProductFiltersState } from '../../types/index';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';

interface EnhancedProductFiltersProps {
  filters: ProductFiltersState;
  setFilters: React.Dispatch<React.SetStateAction<ProductFiltersState>>;
  resetFilters: () => void;
  maxPrice: number;
  productCategories: ProductCategory[];
  showMobileFilters: boolean;
  setShowMobileFilters: (show: boolean) => void;
  activeFiltersCount: number;
  tags?: string[];
}

export function EnhancedProductFilters({
  filters,
  setFilters,
  resetFilters,
  maxPrice,
  productCategories,
  showMobileFilters,
  setShowMobileFilters,
  activeFiltersCount,
  tags = []
}: EnhancedProductFiltersProps) {
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    price: true,
    availability: true,
    rating: true,
    tags: true
  });
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [priceRange, setPriceRange] = useState([filters.priceRange.min, filters.priceRange.max]);

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // تحديث الفلاتر عند تغيير التصنيفات المحددة
  useEffect(() => {
    if (selectedTags.length > 0) {
      setFilters(prev => ({ ...prev, tags: selectedTags }));
    } else {
      setFilters(prev => {
        const newFilters = { ...prev };
        delete newFilters.tags;
        return newFilters;
      });
    }
  }, [selectedTags, setFilters]);

  // تحديث الفلاتر عند تغيير التقييم المحدد
  useEffect(() => {
    if (selectedRating !== null) {
      setFilters(prev => ({ ...prev, rating: selectedRating }));
    } else {
      setFilters(prev => {
        const newFilters = { ...prev };
        delete newFilters.rating;
        return newFilters;
      });
    }
  }, [selectedRating, setFilters]);

  // تحديث الفلاتر عند تغيير نطاق السعر
  useEffect(() => {
    setFilters(prev => ({
      ...prev,
      priceRange: { min: priceRange[0], max: priceRange[1] }
    }));
  }, [priceRange, setFilters]);

  // تبديل حالة توسيع القسم
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // إضافة أو إزالة وسم من الوسوم المحددة
  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  // تحديد التقييم
  const handleRatingSelect = (rating: number) => {
    setSelectedRating(prev => prev === rating ? null : rating);
  };

  // إعادة تعيين جميع الفلاتر
  const handleResetFilters = () => {
    setSelectedTags([]);
    setSelectedRating(null);
    setPriceRange([0, maxPrice]);
    resetFilters();
  };

  return (
    <div className="space-y-4">
      {/* رأس الفلاتر المحدث */}
      <div className="bg-gradient-to-r from-white to-slate-50 dark:from-slate-800 dark:to-slate-700 rounded-xl p-4 border border-slate-200/60 dark:border-slate-700/60 shadow-sm">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary-100 dark:bg-primary-900/30 rounded-lg">
              <Filter className="h-5 w-5 text-primary-600 dark:text-primary-400" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'تصفية المنتجات' : 'Filter Products'}
              </h2>
              {activeFiltersCount > 0 && (
                <p className="text-sm text-slate-600 dark:text-slate-400">
                  {activeFiltersCount} {currentLanguage === 'ar' ? 'فلتر نشط' : 'active filters'}
                </p>
              )}
            </div>
          </div>
          {activeFiltersCount > 0 && (
            <Tooltip content={currentLanguage === 'ar' ? 'إعادة تعيين الفلاتر' : 'Reset Filters'}>
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetFilters}
                className="hover:scale-105 transition-transform duration-200 border-red-200 text-red-600 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/20"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                <span className="hidden lg:inline">
                  {currentLanguage === 'ar' ? 'إعادة تعيين' : 'Reset'}
                </span>
              </Button>
            </Tooltip>
          )}
        </div>

        {/* عرض الفلاتر النشطة */}
        {activeFiltersCount > 0 && (
          <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-700">
            <div className="flex flex-wrap gap-2">
              {filters.category !== 'all' && (
                <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1.5 bg-primary-50 text-primary-700 border-primary-200 dark:bg-primary-900/30 dark:text-primary-300 dark:border-primary-800">
                  <span className="text-xs font-medium">
                    {productCategories.find(c => c.id === filters.category)?.name[currentLanguage] || filters.category}
                  </span>
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer hover:text-primary-800 dark:hover:text-primary-200"
                    onClick={() => setFilters(prev => ({ ...prev, category: 'all' }))}
                  />
                </Badge>
              )}
              {filters.inStock && (
                <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1.5 bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800">
                  <span className="text-xs font-medium">{currentLanguage === 'ar' ? 'متوفر' : 'In Stock'}</span>
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer hover:text-green-800 dark:hover:text-green-200"
                    onClick={() => setFilters(prev => ({ ...prev, inStock: false }))}
                  />
                </Badge>
              )}
              {filters.onSale && (
                <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1.5 bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-800">
                  <span className="text-xs font-medium">{currentLanguage === 'ar' ? 'عرض' : 'On Sale'}</span>
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer hover:text-orange-800 dark:hover:text-orange-200"
                    onClick={() => setFilters(prev => ({ ...prev, onSale: false }))}
                  />
                </Badge>
              )}
              {filters.featured && (
                <Badge variant="secondary" className="flex items-center gap-1 px-3 py-1.5 bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800">
                  <span className="text-xs font-medium">{currentLanguage === 'ar' ? 'مميز' : 'Featured'}</span>
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer hover:text-purple-800 dark:hover:text-purple-200"
                    onClick={() => setFilters(prev => ({ ...prev, featured: false }))}
                  />
                </Badge>
              )}
            </div>
          </div>
        )}
      </div>

      {/* محتوى الفلاتر المحدث */}
      <div className="space-y-3">
        {/* قسم الفئات */}
        <div className="bg-white dark:bg-slate-800 rounded-xl border border-slate-200/60 dark:border-slate-700/60 shadow-sm overflow-hidden">
          <button
            onClick={() => toggleSection('categories')}
            className="w-full px-4 py-4 flex items-center justify-between bg-gradient-to-r from-slate-50 to-white dark:from-slate-700 dark:to-slate-800 hover:from-slate-100 hover:to-slate-50 dark:hover:from-slate-600 dark:hover:to-slate-700 transition-all duration-200"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Tag className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="text-left">
                <h3 className="font-semibold text-slate-900 dark:text-white">
                  {currentLanguage === 'ar' ? 'الفئات' : 'Categories'}
                </h3>
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  {currentLanguage === 'ar' ? 'اختر فئة المنتج' : 'Select product category'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {filters.category !== 'all' && (
                <Badge variant="primary" className="text-xs px-2 py-1">
                  1
                </Badge>
              )}
              {expandedSections.categories ? (
                <ChevronUp className="h-5 w-5 text-slate-400 transition-transform duration-200" />
              ) : (
                <ChevronDown className="h-5 w-5 text-slate-400 transition-transform duration-200" />
              )}
            </div>
          </button>

          <div className={cn(
            "transition-all duration-300 ease-in-out overflow-hidden",
            expandedSections.categories ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
          )}>
            <div className="p-4 space-y-2 max-h-80 overflow-y-auto">
              {/* جميع الفئات */}
              <div
                className={cn(
                  "flex items-center p-3 rounded-lg cursor-pointer transition-all duration-200 group",
                  "border border-transparent hover:border-slate-200 dark:hover:border-slate-600",
                  filters.category === 'all'
                    ? "bg-primary-50 border-primary-200 dark:bg-primary-900/30 dark:border-primary-800"
                    : "hover:bg-slate-50 dark:hover:bg-slate-700/50"
                )}
                onClick={() => setFilters({ ...filters, category: 'all' })}
              >
                <div className={cn(
                  "flex items-center justify-center w-5 h-5 rounded-full border-2 mr-3 transition-all duration-200",
                  filters.category === 'all'
                    ? "border-primary-500 bg-primary-500 scale-110"
                    : "border-slate-300 dark:border-slate-600 group-hover:border-primary-300"
                )}>
                  {filters.category === 'all' && (
                    <Check className="h-3 w-3 text-white" />
                  )}
                </div>
                <div className="flex-1">
                  <span className={cn(
                    "font-medium transition-colors duration-200",
                    filters.category === 'all' ? "text-primary-700 dark:text-primary-300" : "text-slate-700 dark:text-slate-300"
                  )}>
                    {currentLanguage === 'ar' ? 'جميع الفئات' : 'All Categories'}
                  </span>
                </div>
              </div>

              {/* فئات المنتجات */}
              {productCategories.map(category => (
                <div
                  key={category.id}
                  className={cn(
                    "flex items-center p-3 rounded-lg cursor-pointer transition-all duration-200 group",
                    "border border-transparent hover:border-slate-200 dark:hover:border-slate-600",
                    filters.category === category.id
                      ? "bg-primary-50 border-primary-200 dark:bg-primary-900/30 dark:border-primary-800"
                      : "hover:bg-slate-50 dark:hover:bg-slate-700/50"
                  )}
                  onClick={() => setFilters({ ...filters, category: category.id })}
                >
                  <div className={cn(
                    "flex items-center justify-center w-5 h-5 rounded-full border-2 mr-3 transition-all duration-200",
                    filters.category === category.id
                      ? "border-primary-500 bg-primary-500 scale-110"
                      : "border-slate-300 dark:border-slate-600 group-hover:border-primary-300"
                  )}>
                    {filters.category === category.id && (
                      <Check className="h-3 w-3 text-white" />
                    )}
                  </div>
                  <div className="flex-1">
                    <span className={cn(
                      "font-medium transition-colors duration-200",
                      filters.category === category.id ? "text-primary-700 dark:text-primary-300" : "text-slate-700 dark:text-slate-300"
                    )}>
                      {category.name[currentLanguage]}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* قسم نطاق السعر */}
        <div className="bg-white dark:bg-slate-800 rounded-xl border border-slate-200/60 dark:border-slate-700/60 shadow-sm overflow-hidden">
          <button
            onClick={() => toggleSection('price')}
            className="w-full px-4 py-4 flex items-center justify-between bg-gradient-to-r from-slate-50 to-white dark:from-slate-700 dark:to-slate-800 hover:from-slate-100 hover:to-slate-50 dark:hover:from-slate-600 dark:hover:to-slate-700 transition-all duration-200"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Sliders className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
              <div className="text-left">
                <h3 className="font-semibold text-slate-900 dark:text-white">
                  {currentLanguage === 'ar' ? 'نطاق السعر' : 'Price Range'}
                </h3>
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  {formatCurrency(priceRange[0])} - {formatCurrency(priceRange[1])}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {(filters.priceRange.min > 0 || filters.priceRange.max < maxPrice) && (
                <Badge variant="primary" className="text-xs px-2 py-1">
                  1
                </Badge>
              )}
              {expandedSections.price ? (
                <ChevronUp className="h-5 w-5 text-slate-400 transition-transform duration-200" />
              ) : (
                <ChevronDown className="h-5 w-5 text-slate-400 transition-transform duration-200" />
              )}
            </div>
          </button>

          <div className={cn(
            "transition-all duration-300 ease-in-out overflow-hidden",
            expandedSections.price ? "max-h-64 opacity-100" : "max-h-0 opacity-0"
          )}>
            <div className="p-4">
              <div className="space-y-4">
                <Slider
                  min={0}
                  max={maxPrice}
                  step={1}
                  value={priceRange}
                  onValueChange={setPriceRange}
                  className="my-4"
                />
                <div className="flex items-center gap-3">
                  <div className="flex-1">
                    <label className="block text-xs font-medium text-slate-600 dark:text-slate-400 mb-1">
                      {currentLanguage === 'ar' ? 'الحد الأدنى' : 'Min'}
                    </label>
                    <Input
                      type="number"
                      min={0}
                      max={priceRange[1]}
                      value={priceRange[0]}
                      onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                      className="text-sm"
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-xs font-medium text-slate-600 dark:text-slate-400 mb-1">
                      {currentLanguage === 'ar' ? 'الحد الأقصى' : 'Max'}
                    </label>
                    <Input
                      type="number"
                      min={priceRange[0]}
                      max={maxPrice}
                      value={priceRange[1]}
                      onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || maxPrice])}
                      className="text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* قسم التوفر والميزات */}
        <div className="bg-white dark:bg-slate-800 rounded-xl border border-slate-200/60 dark:border-slate-700/60 shadow-sm overflow-hidden">
          <button
            onClick={() => toggleSection('availability')}
            className="w-full px-4 py-4 flex items-center justify-between bg-gradient-to-r from-slate-50 to-white dark:from-slate-700 dark:to-slate-800 hover:from-slate-100 hover:to-slate-50 dark:hover:from-slate-600 dark:hover:to-slate-700 transition-all duration-200"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <Check className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="text-left">
                <h3 className="font-semibold text-slate-900 dark:text-white">
                  {currentLanguage === 'ar' ? 'التوفر والميزات' : 'Availability & Features'}
                </h3>
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  {currentLanguage === 'ar' ? 'خيارات إضافية' : 'Additional options'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {(filters.inStock || filters.onSale || filters.featured) && (
                <Badge variant="primary" className="text-xs px-2 py-1">
                  {[filters.inStock, filters.onSale, filters.featured].filter(Boolean).length}
                </Badge>
              )}
              {expandedSections.availability ? (
                <ChevronUp className="h-5 w-5 text-slate-400 transition-transform duration-200" />
              ) : (
                <ChevronDown className="h-5 w-5 text-slate-400 transition-transform duration-200" />
              )}
            </div>
          </button>

          <div className={cn(
            "transition-all duration-300 ease-in-out overflow-hidden",
            expandedSections.availability ? "max-h-64 opacity-100" : "max-h-0 opacity-0"
          )}>
            <div className="p-4 space-y-3">
              {/* متوفر في المخزون */}
              <label className="flex items-center p-3 rounded-lg cursor-pointer transition-all duration-200 hover:bg-slate-50 dark:hover:bg-slate-700/50 group">
                <input
                  type="checkbox"
                  checked={filters.inStock}
                  onChange={(e) => setFilters({ ...filters, inStock: e.target.checked })}
                  className="sr-only"
                />
                <div className={cn(
                  "flex items-center justify-center w-5 h-5 rounded border-2 mr-3 transition-all duration-200",
                  filters.inStock
                    ? "border-green-500 bg-green-500 scale-110"
                    : "border-slate-300 dark:border-slate-600 group-hover:border-green-300"
                )}>
                  {filters.inStock && (
                    <Check className="h-3 w-3 text-white" />
                  )}
                </div>
                <span className="font-medium text-slate-700 dark:text-slate-300">
                  {currentLanguage === 'ar' ? 'متوفر في المخزون' : 'In Stock Only'}
                </span>
              </label>

              {/* العروض */}
              <label className="flex items-center p-3 rounded-lg cursor-pointer transition-all duration-200 hover:bg-slate-50 dark:hover:bg-slate-700/50 group">
                <input
                  type="checkbox"
                  checked={filters.onSale}
                  onChange={(e) => setFilters({ ...filters, onSale: e.target.checked })}
                  className="sr-only"
                />
                <div className={cn(
                  "flex items-center justify-center w-5 h-5 rounded border-2 mr-3 transition-all duration-200",
                  filters.onSale
                    ? "border-orange-500 bg-orange-500 scale-110"
                    : "border-slate-300 dark:border-slate-600 group-hover:border-orange-300"
                )}>
                  {filters.onSale && (
                    <Check className="h-3 w-3 text-white" />
                  )}
                </div>
                <span className="font-medium text-slate-700 dark:text-slate-300">
                  {currentLanguage === 'ar' ? 'العروض والخصومات' : 'On Sale'}
                </span>
              </label>

              {/* المنتجات المميزة */}
              <label className="flex items-center p-3 rounded-lg cursor-pointer transition-all duration-200 hover:bg-slate-50 dark:hover:bg-slate-700/50 group">
                <input
                  type="checkbox"
                  checked={filters.featured}
                  onChange={(e) => setFilters({ ...filters, featured: e.target.checked })}
                  className="sr-only"
                />
                <div className={cn(
                  "flex items-center justify-center w-5 h-5 rounded border-2 mr-3 transition-all duration-200",
                  filters.featured
                    ? "border-purple-500 bg-purple-500 scale-110"
                    : "border-slate-300 dark:border-slate-600 group-hover:border-purple-300"
                )}>
                  {filters.featured && (
                    <Check className="h-3 w-3 text-white" />
                  )}
                </div>
                <span className="font-medium text-slate-700 dark:text-slate-300">
                  {currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
