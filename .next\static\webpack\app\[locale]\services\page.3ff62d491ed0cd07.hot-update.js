"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/services/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check-circle.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckCircle)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.344.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst CheckCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CheckCircle\", [\n    [\n        \"path\",\n        {\n            d: \"M22 11.08V12a10 10 0 1 1-5.93-9.14\",\n            key: \"g774vq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n]);\n //# sourceMappingURL=check-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hlY2stY2lyY2xlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sb0JBQWMsZ0VBQWdCLENBQUMsYUFBZTtJQUNsRDtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBc0M7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQ25FO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFrQjtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7Q0FDaEQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc3JjXFxpY29uc1xcY2hlY2stY2lyY2xlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQ2hlY2tDaXJjbGVcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1qSWdNVEV1TURoV01USmhNVEFnTVRBZ01DQXhJREV0TlM0NU15MDVMakUwSWlBdlBnb2dJRHh3WVhSb0lHUTlJbTA1SURFeElETWdNMHd5TWlBMElpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZWNrLWNpcmNsZVxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZWNrQ2lyY2xlID0gY3JlYXRlTHVjaWRlSWNvbignQ2hlY2tDaXJjbGUnLCBbXG4gIFsncGF0aCcsIHsgZDogJ00yMiAxMS4wOFYxMmExMCAxMCAwIDEgMS01LjkzLTkuMTQnLCBrZXk6ICdnNzc0dnEnIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdtOSAxMSAzIDNMMjIgNCcsIGtleTogJzFwZmx6bCcgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hlY2tDaXJjbGU7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Clock)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.344.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Clock\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"12 6 12 12 16 14\",\n            key: \"68esgv\"\n        }\n    ]\n]);\n //# sourceMappingURL=clock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2xvY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFhTSxjQUFRLGdFQUFnQixDQUFDLE9BQVM7SUFDdEM7UUFBQyxRQUFVO1FBQUE7WUFBRSxFQUFJO1lBQU0sQ0FBSSxRQUFNO1lBQUEsQ0FBRztZQUFNLEdBQUs7UUFBQSxDQUFVO0tBQUE7SUFDekQ7UUFBQyxVQUFZO1FBQUE7WUFBRSxRQUFRLENBQW9CO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtDQUMzRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzcmNcXGljb25zXFxjbG9jay50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENsb2NrXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThZMmx5WTJ4bElHTjRQU0l4TWlJZ1kzazlJakV5SWlCeVBTSXhNQ0lnTHo0S0lDQThjRzlzZVd4cGJtVWdjRzlwYm5SelBTSXhNaUEySURFeUlERXlJREUySURFMElpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2Nsb2NrXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2xvY2sgPSBjcmVhdGVMdWNpZGVJY29uKCdDbG9jaycsIFtcbiAgWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzEyJywgcjogJzEwJywga2V5OiAnMW1nbGF5JyB9XSxcbiAgWydwb2x5bGluZScsIHsgcG9pbnRzOiAnMTIgNiAxMiAxMiAxNiAxNCcsIGtleTogJzY4ZXNndicgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2xvY2s7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/star.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Star)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.344.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Star = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Star\", [\n    [\n        \"polygon\",\n        {\n            points: \"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\",\n            key: \"8f66p6\"\n        }\n    ]\n]);\n //# sourceMappingURL=star.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/pages/services/ServicesPageSimple.tsx":
/*!***************************************************!*\
  !*** ./src/pages/services/ServicesPageSimple.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServicesPageSimple)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _data_services__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../data/services */ \"(app-pages-browser)/./src/data/services.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../stores/themeStore */ \"(app-pages-browser)/./src/stores/themeStore.ts\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Icon mapping for services\nconst icons = {\n    Search: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    Package: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    Truck: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    FileCheck: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    Users: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    ClipboardList: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n};\nfunction ServicesPageSimple() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_6__.useThemeStore)();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_7__.useLanguageStore)();\n    // State management\n    const [showBookingForm, setShowBookingForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedService, setSelectedService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        searchQuery: '',\n        category: 'all',\n        sortBy: 'name'\n    });\n    // Service categories for filtering\n    const categories = [\n        {\n            id: 'all',\n            name: language === 'ar' ? 'جميع الخدمات' : 'All Services'\n        },\n        {\n            id: 'inspection',\n            name: language === 'ar' ? 'خدمات الفحص' : 'Inspection Services'\n        },\n        {\n            id: 'storage',\n            name: language === 'ar' ? 'حلول التخزين' : 'Storage Solutions'\n        },\n        {\n            id: 'shipping',\n            name: language === 'ar' ? 'الشحن والخدمات اللوجستية' : 'Shipping & Logistics'\n        },\n        {\n            id: 'order-management',\n            name: language === 'ar' ? 'إدارة الطلبات' : 'Order Management'\n        },\n        {\n            id: 'certification',\n            name: language === 'ar' ? 'شهادات المنتجات' : 'Product Certification'\n        },\n        {\n            id: 'consulting',\n            name: language === 'ar' ? 'الاستشارات التجارية' : 'Business Consulting'\n        }\n    ];\n    // Filter and sort services\n    const filteredServices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ServicesPageSimple.useMemo[filteredServices]\": ()=>{\n            let filtered = _data_services__WEBPACK_IMPORTED_MODULE_5__.services.filter({\n                \"ServicesPageSimple.useMemo[filteredServices].filtered\": (service)=>{\n                    const matchesSearch = filters.searchQuery === '' || service.name.toLowerCase().includes(filters.searchQuery.toLowerCase()) || service.name_ar && service.name_ar.includes(filters.searchQuery) || service.description.toLowerCase().includes(filters.searchQuery.toLowerCase()) || service.description_ar && service.description_ar.includes(filters.searchQuery);\n                    const matchesCategory = filters.category === 'all' || service.id === filters.category;\n                    return matchesSearch && matchesCategory;\n                }\n            }[\"ServicesPageSimple.useMemo[filteredServices].filtered\"]);\n            // Sort services\n            filtered.sort({\n                \"ServicesPageSimple.useMemo[filteredServices]\": (a, b)=>{\n                    switch(filters.sortBy){\n                        case 'name':\n                            const nameA = language === 'ar' ? a.name_ar || a.name : a.name;\n                            const nameB = language === 'ar' ? b.name_ar || b.name : b.name;\n                            return nameA.localeCompare(nameB);\n                        case 'popularity':\n                            return b.id.localeCompare(a.id); // Mock popularity sort\n                        case 'recent':\n                            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                        default:\n                            return 0;\n                    }\n                }\n            }[\"ServicesPageSimple.useMemo[filteredServices]\"]);\n            return filtered;\n        }\n    }[\"ServicesPageSimple.useMemo[filteredServices]\"], [\n        _data_services__WEBPACK_IMPORTED_MODULE_5__.services,\n        filters,\n        language\n    ]);\n    // Event handlers\n    const handleBookService = (serviceName)=>{\n        setSelectedService(serviceName);\n        setShowBookingForm(true);\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            searchQuery: '',\n            category: 'all',\n            sortBy: 'name'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"relative py-16 overflow-hidden transition-colors duration-500\", isDarkMode ? \"bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900\" : \"bg-gradient-to-br from-slate-50 via-white to-slate-100\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"absolute inset-0 opacity-5 transition-opacity duration-500\", isDarkMode ? \"opacity-10\" : \"opacity-5\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:50px_50px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"text-4xl md:text-5xl font-bold leading-tight tracking-tight mb-6 animate-fade-in\", isDarkMode ? \"text-white\" : \"text-slate-900\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block\",\n                                            children: language === 'ar' ? 'خدمات' : 'Business'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"block bg-gradient-to-r bg-clip-text text-transparent\", isDarkMode ? \"from-primary-400 to-blue-400\" : \"from-primary-600 to-blue-600\"),\n                                            children: language === 'ar' ? 'الأعمال' : 'Services'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"text-lg md:text-xl leading-relaxed max-w-3xl mx-auto mb-8 animate-fade-in-delay-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\"),\n                                    children: language === 'ar' ? 'خدمات دعم شاملة لتبسيط عملياتك وتعزيز كفاءة أعمالك مع حلول متطورة ومخصصة.' : 'Comprehensive support services to streamline your operations and enhance business efficiency with advanced, tailored solutions.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 animate-fade-in-delay-2\",\n                                    children: [\n                                        {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 25\n                                            }, this),\n                                            value: '6+',\n                                            label: language === 'ar' ? 'خدمة متخصصة' : 'Expert Services'\n                                        },\n                                        {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 25\n                                            }, this),\n                                            value: '500+',\n                                            label: language === 'ar' ? 'عميل راضي' : 'Satisfied Clients'\n                                        },\n                                        {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 25\n                                            }, this),\n                                            value: '24/48h',\n                                            label: language === 'ar' ? 'وقت الاستجابة' : 'Response Time'\n                                        },\n                                        {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 25\n                                            }, this),\n                                            value: '50+',\n                                            label: language === 'ar' ? 'دولة' : 'Countries'\n                                        }\n                                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"p-4 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg\", isDarkMode ? \"bg-slate-800/50 hover:bg-slate-800/70\" : \"bg-white/50 hover:bg-white/70\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"flex items-center justify-center w-8 h-8 rounded-full mx-auto mb-2\", isDarkMode ? \"bg-primary-500/20 text-primary-400\" : \"bg-primary-100 text-primary-600\"),\n                                                    children: stat.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"text-lg font-bold mb-1\", isDarkMode ? \"text-white\" : \"text-slate-900\"),\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"text-xs font-medium\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\"),\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-center gap-4 animate-fade-in-delay-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"lg\",\n                                            variant: \"primary\",\n                                            className: \"px-8 py-3 text-lg font-semibold group transition-all duration-300 hover:scale-105\",\n                                            onClick: ()=>router.push(\"/\".concat(language, \"/contact\")),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"\".concat(language === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5 group-hover:scale-110 transition-transform\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this),\n                                                language === 'ar' ? 'استشارة مجانية' : 'Free Consultation'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"px-8 py-3 text-lg font-semibold group transition-all duration-300 hover:scale-105\",\n                                            onClick: ()=>{\n                                                var _document_getElementById;\n                                                return (_document_getElementById = document.getElementById('services-grid')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                    behavior: 'smooth'\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"\".concat(language === 'ar' ? 'mr-2 rotate-180' : 'ml-2', \" h-5 w-5 group-hover:translate-x-1 transition-transform\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                language === 'ar' ? 'استكشف الخدمات' : 'Explore Services'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"py-16\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-50\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-3xl mx-auto text-center mb-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\",\n                                children: language === 'ar' ? 'خدماتنا' : 'Our Services'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: _data_services__WEBPACK_IMPORTED_MODULE_5__.services.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-xl mb-2 text-slate-900 dark:text-white\",\n                                                children: language === 'ar' ? service.name_ar || service.name : service.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 dark:text-slate-300 mb-6 flex-grow\",\n                                                    children: language === 'ar' ? service.description_ar || service.description : service.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 mt-auto\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"primary\",\n                                                            className: \"flex-1\",\n                                                            onClick: ()=>router.push(\"/\".concat(language, \"/contact\")),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                language === 'ar' ? 'احجز الخدمة' : 'Book Service'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"flex-1\",\n                                                            onClick: ()=>router.push(\"/\".concat(language, \"/services/\").concat(service.slug)),\n                                                            children: [\n                                                                language === 'ar' ? 'معرفة المزيد' : 'Learn More',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"ml-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, service.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"py-16\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-3xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6\",\n                                children: language === 'ar' ? 'هل أنت مستعد لتعزيز عمليات عملك؟' : 'Ready to Enhance Your Business Operations?'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-slate-600 dark:text-slate-300 mb-8\",\n                                children: language === 'ar' ? 'اتصل بفريقنا لمناقشة كيف يمكن لخدماتنا تلبية احتياجات عملك المحددة.' : 'Contact our team to discuss how our services can address your specific business needs.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        variant: \"primary\",\n                                        onClick: ()=>router.push(\"/\".concat(language, \"/contact\")),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            language === 'ar' ? 'اتصل بنا' : 'Contact Us'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/\".concat(language, \"/contact\")),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            language === 'ar' ? 'طلب عرض سعر' : 'Request Quote'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(ServicesPageSimple, \"E/qd+g4WGyFQLp2CRcC6T6vZhHQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _stores_themeStore__WEBPACK_IMPORTED_MODULE_6__.useThemeStore,\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_7__.useLanguageStore\n    ];\n});\n_c = ServicesPageSimple;\nvar _c;\n$RefreshReg$(_c, \"ServicesPageSimple\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/pages/services/ServicesPageSimple.tsx\n"));

/***/ })

});