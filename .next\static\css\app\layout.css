/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/fonts.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/* تحسين عرض الخط العربي */
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
  letter-spacing: -0.025em;
  line-height: 1.5;
}

/* تحسين حجم الخط للعربية */
[dir="rtl"] {
  font-feature-settings: "calt" 1, "clig" 1, "dlig" 1, "kern" 1, "liga" 1, "salt" 1;
}

/* تحسين المسافات بين الأحرف العربية */
[dir="rtl"] p,
[dir="rtl"] span,
[dir="rtl"] div,
[dir="rtl"] button,
[dir="rtl"] a {
  letter-spacing: 0;
  word-spacing: 0.05em;
  line-height: 1.8;
}

/* تحسين اتجاه النص في المدخلات */
[dir="rtl"] input,
[dir="rtl"] textarea {
  text-align: right;
}

/* تحسين هوامش العناصر في وضع RTL */
[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

/* تحسين أيقونات الأسهم في وضع RTL */
[dir="rtl"] .rotate-rtl {
  transform: rotate(180deg);
}

/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/index.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/* استيراد ملف الخطوط */

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:root {
    /* أرجواني/وردي - اللون الأساسي من الشعار */
    --primary-50: 250 245 255;
    --primary-100: 243 232 255;
    --primary-200: 233 213 255;
    --primary-300: 216 180 254;
    --primary-400: 192 132 252;
    --primary-500: 168 85 247;
    --primary-600: 147 51 234;
    --primary-700: 126 34 206;
    --primary-800: 107 33 168;
    --primary-900: 88 28 135;
    --primary-950: 59 7 100;

    /* أزرق/سماوي - اللون الثانوي من الشعار */
    --secondary-50: 236 254 255;
    --secondary-100: 207 250 254;
    --secondary-200: 165 243 252;
    --secondary-300: 103 232 249;
    --secondary-400: 34 211 238;
    --secondary-500: 6 182 212;
    --secondary-600: 8 145 178;
    --secondary-700: 14 116 144;
    --secondary-800: 21 94 117;
    --secondary-900: 22 78 99;
    --secondary-950: 8 51 68;

    /* برتقالي/أصفر - اللون الثالث من الشعار */
    --accent-50: 255 247 237;
    --accent-100: 255 237 213;
    --accent-200: 254 215 170;
    --accent-300: 253 186 116;
    --accent-400: 251 146 60;
    --accent-500: 249 115 22;
    --accent-600: 234 88 12;
    --accent-700: 194 65 12;
    --accent-800: 154 52 18;
    --accent-900: 124 45 18;
    --accent-950: 67 20 7;

    --success-50: 240 253 244;
    --success-100: 220 252 231;
    --success-500: 34 197 94;
    --success-600: 22 163 74;
    --success-700: 21 128 61;

    --error-50: 254 242 242;
    --error-100: 254 226 226;
    --error-500: 239 68 68;
    --error-600: 220 38 38;
    --error-700: 185 28 28;
  }

html{
  scroll-behavior: smooth;
}

body{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dark body{
  --tw-bg-opacity: 1;
  background-color: rgb(2 6 23 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity, 1));
}

/* تحسينات للأجهزة المحمولة */

@media (max-width: 767px) {
    html {
      font-size: 15px;
    }

    .dark html {
      font-size: 14px;
    }

    body{
    touch-action: manipulation;
  }
  }

h1, h2, h3, h4, h5, h6{
  font-weight: 600;
  letter-spacing: -0.025em;
}

h1{
  font-size: 2.25rem;
  line-height: 2.5rem;
}

@media (min-width: 768px){

  h1{
    font-size: 3rem;
    line-height: 1;
  }
}

@media (min-width: 1024px){

  h1{
    font-size: 3.75rem;
    line-height: 1;
  }
}

h2{
  font-size: 1.875rem;
  line-height: 2.25rem;
}

@media (min-width: 768px){

  h2{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

@media (min-width: 1024px){

  h2{
    font-size: 3rem;
    line-height: 1;
  }
}

h3{
  font-size: 1.5rem;
  line-height: 2rem;
}

@media (min-width: 768px){

  h3{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

h4{
  font-size: 1.25rem;
  line-height: 1.75rem;
}

@media (min-width: 768px){

  h4{
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

h5{
  font-size: 1.125rem;
  line-height: 1.75rem;
}

@media (min-width: 768px){

  h5{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

h6{
  font-size: 1rem;
  line-height: 1.5rem;
}

@media (min-width: 768px){

  h6{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

a{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

a:hover{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.dark a{
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.dark a:hover{
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.container{
  width: 100%;
}
@media (min-width: 640px){

  .container{
    max-width: 640px;
  }
}
@media (min-width: 768px){

  .container{
    max-width: 768px;
  }
}
@media (min-width: 1024px){

  .container{
    max-width: 1024px;
  }
}
@media (min-width: 1280px){

  .container{
    max-width: 1280px;
  }
}
@media (min-width: 1536px){

  .container{
    max-width: 1536px;
  }
}
.dark .btn-primary{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.dark .btn-primary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}
.dark .btn-primary:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));
}
.dark .btn-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity, 1));
}
.dark .btn-secondary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.dark .btn-secondary:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(148 163 184 / var(--tw-ring-opacity, 1));
}
.dark .btn-accent{
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}
.dark .btn-accent:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity, 1));
}
.dark .btn-accent:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(251 146 60 / var(--tw-ring-opacity, 1));
}
/* تأثير التحويم للأزرار - تم إصلاح مشكلة اختفاء النص */
.dark .btn-primary::after,
  .dark .btn-accent::after{
  z-index: -10;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  opacity: 0;
}
.dark .btn-primary:hover::after,
  .dark .btn-accent:hover::after{
  opacity: 0.2;
}
.container-custom{
  margin-left: auto;
  margin-right: auto;
  max-width: 90rem;
  padding-left: 1rem;
  padding-right: 1rem;
}
@media (min-width: 640px){

  .container-custom{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
@media (min-width: 1024px){

  .container-custom{
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
.container-custom { /* زيادة العرض من 80rem (1280px) إلى 90rem (1440px) */
  }
.section{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
@media (min-width: 768px){

  .section{
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}
@media (min-width: 1024px){

  .section{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}
.\!card{
  overflow: hidden;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
.\!card:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.card{
  overflow: hidden;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
.card:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark .card{
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark .card:hover{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark .\!card{
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark .\!card:hover{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none{
  pointer-events: none;
}
.visible{
  visibility: visible;
}
.static{
  position: static;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.sticky{
  position: sticky;
}
.-inset-1{
  inset: -0.25rem;
}
.-inset-10{
  inset: -2.5rem;
}
.-inset-2{
  inset: -0.5rem;
}
.-inset-4{
  inset: -1rem;
}
.inset-0{
  inset: 0px;
}
.inset-y-0{
  top: 0px;
  bottom: 0px;
}
.-bottom-0\.5{
  bottom: -0.125rem;
}
.-bottom-1{
  bottom: -0.25rem;
}
.-bottom-2{
  bottom: -0.5rem;
}
.-bottom-20{
  bottom: -5rem;
}
.-bottom-24{
  bottom: -6rem;
}
.-bottom-4{
  bottom: -1rem;
}
.-bottom-40{
  bottom: -10rem;
}
.-bottom-5{
  bottom: -1.25rem;
}
.-left-20{
  left: -5rem;
}
.-left-24{
  left: -6rem;
}
.-left-3{
  left: -0.75rem;
}
.-left-40{
  left: -10rem;
}
.-left-5{
  left: -1.25rem;
}
.-right-1{
  right: -0.25rem;
}
.-right-2{
  right: -0.5rem;
}
.-right-20{
  right: -5rem;
}
.-right-24{
  right: -6rem;
}
.-right-3{
  right: -0.75rem;
}
.-right-40{
  right: -10rem;
}
.-right-5{
  right: -1.25rem;
}
.-top-1{
  top: -0.25rem;
}
.-top-10{
  top: -2.5rem;
}
.-top-2{
  top: -0.5rem;
}
.-top-20{
  top: -5rem;
}
.-top-24{
  top: -6rem;
}
.-top-4{
  top: -1rem;
}
.-top-40{
  top: -10rem;
}
.-top-5{
  top: -1.25rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-10{
  bottom: 2.5rem;
}
.bottom-16{
  bottom: 4rem;
}
.bottom-2{
  bottom: 0.5rem;
}
.bottom-24{
  bottom: 6rem;
}
.bottom-3{
  bottom: 0.75rem;
}
.bottom-4{
  bottom: 1rem;
}
.bottom-6{
  bottom: 1.5rem;
}
.bottom-8{
  bottom: 2rem;
}
.left-0{
  left: 0px;
}
.left-1{
  left: 0.25rem;
}
.left-1\/2{
  left: 50%;
}
.left-1\/4{
  left: 25%;
}
.left-10{
  left: 2.5rem;
}
.left-2{
  left: 0.5rem;
}
.left-3{
  left: 0.75rem;
}
.left-4{
  left: 1rem;
}
.left-6{
  left: 1.5rem;
}
.left-\[50\%\]{
  left: 50%;
}
.left-auto{
  left: auto;
}
.right-0{
  right: 0px;
}
.right-1{
  right: 0.25rem;
}
.right-1\/4{
  right: 25%;
}
.right-10{
  right: 2.5rem;
}
.right-2{
  right: 0.5rem;
}
.right-3{
  right: 0.75rem;
}
.right-4{
  right: 1rem;
}
.right-6{
  right: 1.5rem;
}
.top-0{
  top: 0px;
}
.top-1{
  top: 0.25rem;
}
.top-1\/2{
  top: 50%;
}
.top-10{
  top: 2.5rem;
}
.top-12{
  top: 3rem;
}
.top-16{
  top: 4rem;
}
.top-2{
  top: 0.5rem;
}
.top-24{
  top: 6rem;
}
.top-3{
  top: 0.75rem;
}
.top-4{
  top: 1rem;
}
.top-5{
  top: 1.25rem;
}
.top-6{
  top: 1.5rem;
}
.top-\[50\%\]{
  top: 50%;
}
.-z-0{
  z-index: 0;
}
.-z-10{
  z-index: -10;
}
.z-0{
  z-index: 0;
}
.z-10{
  z-index: 10;
}
.z-20{
  z-index: 20;
}
.z-30{
  z-index: 30;
}
.z-40{
  z-index: 40;
}
.z-50{
  z-index: 50;
}
.z-\[5\]{
  z-index: 5;
}
.col-span-1{
  grid-column: span 1 / span 1;
}
.col-span-2{
  grid-column: span 2 / span 2;
}
.col-span-5{
  grid-column: span 5 / span 5;
}
.m-auto{
  margin: auto;
}
.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-3{
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}
.mx-6{
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-6{
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-8{
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.mb-0\.5{
  margin-bottom: 0.125rem;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-1\.5{
  margin-bottom: 0.375rem;
}
.mb-10{
  margin-bottom: 2.5rem;
}
.mb-12{
  margin-bottom: 3rem;
}
.mb-16{
  margin-bottom: 4rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-20{
  margin-bottom: 5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-5{
  margin-bottom: 1.25rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-1\.5{
  margin-left: 0.375rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-3{
  margin-left: 0.75rem;
}
.ml-4{
  margin-left: 1rem;
}
.ml-7{
  margin-left: 1.75rem;
}
.ml-auto{
  margin-left: auto;
}
.mr-0{
  margin-right: 0px;
}
.mr-0\.5{
  margin-right: 0.125rem;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-1\.5{
  margin-right: 0.375rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mr-3{
  margin-right: 0.75rem;
}
.mr-4{
  margin-right: 1rem;
}
.mt-0\.5{
  margin-top: 0.125rem;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-1\.5{
  margin-top: 0.375rem;
}
.mt-12{
  margin-top: 3rem;
}
.mt-16{
  margin-top: 4rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-20{
  margin-top: 5rem;
}
.mt-3{
  margin-top: 0.75rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-5{
  margin-top: 1.25rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-8{
  margin-top: 2rem;
}
.mt-auto{
  margin-top: auto;
}
.line-clamp-1{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.hidden{
  display: none;
}
.aspect-\[16\/9\]{
  aspect-ratio: 16/9;
}
.aspect-\[21\/9\]{
  aspect-ratio: 21/9;
}
.aspect-\[4\/3\]{
  aspect-ratio: 4/3;
}
.aspect-square{
  aspect-ratio: 1 / 1;
}
.aspect-video{
  aspect-ratio: 16 / 9;
}
.h-0\.5{
  height: 0.125rem;
}
.h-1{
  height: 0.25rem;
}
.h-1\.5{
  height: 0.375rem;
}
.h-10{
  height: 2.5rem;
}
.h-11{
  height: 2.75rem;
}
.h-12{
  height: 3rem;
}
.h-14{
  height: 3.5rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-2\.5{
  height: 0.625rem;
}
.h-20{
  height: 5rem;
}
.h-24{
  height: 6rem;
}
.h-3{
  height: 0.75rem;
}
.h-3\.5{
  height: 0.875rem;
}
.h-32{
  height: 8rem;
}
.h-4{
  height: 1rem;
}
.h-40{
  height: 10rem;
}
.h-48{
  height: 12rem;
}
.h-5{
  height: 1.25rem;
}
.h-6{
  height: 1.5rem;
}
.h-64{
  height: 16rem;
}
.h-7{
  height: 1.75rem;
}
.h-8{
  height: 2rem;
}
.h-80{
  height: 20rem;
}
.h-9{
  height: 2.25rem;
}
.h-96{
  height: 24rem;
}
.h-\[500px\]{
  height: 500px;
}
.h-\[90vh\]{
  height: 90vh;
}
.h-auto{
  height: auto;
}
.h-full{
  height: 100%;
}
.max-h-0{
  max-height: 0px;
}
.max-h-40{
  max-height: 10rem;
}
.max-h-60{
  max-height: 15rem;
}
.max-h-80{
  max-height: 20rem;
}
.max-h-\[2000px\]{
  max-height: 2000px;
}
.max-h-\[60vh\]{
  max-height: 60vh;
}
.max-h-\[90vh\]{
  max-height: 90vh;
}
.min-h-\[100px\]{
  min-height: 100px;
}
.min-h-\[120px\]{
  min-height: 120px;
}
.min-h-\[150px\]{
  min-height: 150px;
}
.min-h-\[80vh\]{
  min-height: 80vh;
}
.min-h-screen{
  min-height: 100vh;
}
.w-0{
  width: 0px;
}
.w-0\.5{
  width: 0.125rem;
}
.w-1{
  width: 0.25rem;
}
.w-1\.5{
  width: 0.375rem;
}
.w-1\/2{
  width: 50%;
}
.w-1\/3{
  width: 33.333333%;
}
.w-1\/4{
  width: 25%;
}
.w-1\/5{
  width: 20%;
}
.w-10{
  width: 2.5rem;
}
.w-12{
  width: 3rem;
}
.w-14{
  width: 3.5rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-2\.5{
  width: 0.625rem;
}
.w-2\/3{
  width: 66.666667%;
}
.w-20{
  width: 5rem;
}
.w-24{
  width: 6rem;
}
.w-3{
  width: 0.75rem;
}
.w-3\.5{
  width: 0.875rem;
}
.w-3\/4{
  width: 75%;
}
.w-32{
  width: 8rem;
}
.w-4{
  width: 1rem;
}
.w-4\/6{
  width: 66.666667%;
}
.w-40{
  width: 10rem;
}
.w-48{
  width: 12rem;
}
.w-5{
  width: 1.25rem;
}
.w-5\/6{
  width: 83.333333%;
}
.w-56{
  width: 14rem;
}
.w-6{
  width: 1.5rem;
}
.w-64{
  width: 16rem;
}
.w-7{
  width: 1.75rem;
}
.w-8{
  width: 2rem;
}
.w-80{
  width: 20rem;
}
.w-9{
  width: 2.25rem;
}
.w-96{
  width: 24rem;
}
.w-\[280px\]{
  width: 280px;
}
.w-auto{
  width: auto;
}
.w-fit{
  width: -moz-fit-content;
  width: fit-content;
}
.w-full{
  width: 100%;
}
.min-w-0{
  min-width: 0px;
}
.min-w-\[180px\]{
  min-width: 180px;
}
.min-w-\[40px\]{
  min-width: 40px;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-3xl{
  max-width: 48rem;
}
.max-w-4xl{
  max-width: 56rem;
}
.max-w-\[100px\]{
  max-width: 100px;
}
.max-w-\[200px\]{
  max-width: 200px;
}
.max-w-\[80\%\]{
  max-width: 80%;
}
.max-w-full{
  max-width: 100%;
}
.max-w-lg{
  max-width: 32rem;
}
.max-w-md{
  max-width: 28rem;
}
.max-w-none{
  max-width: none;
}
.max-w-xs{
  max-width: 20rem;
}
.max-w-24{
  max-width: 6rem;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.flex-grow{
  flex-grow: 1;
}
.grow{
  flex-grow: 1;
}
.origin-center{
  transform-origin: center;
}
.origin-left{
  transform-origin: left;
}
.origin-right{
  transform-origin: right;
}
.origin-top-left{
  transform-origin: top left;
}
.origin-top-right{
  transform-origin: top right;
}
.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-full{
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-full{
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\]{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full{
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-2{
  --tw-translate-y: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\]{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-full{
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-2{
  --tw-translate-x: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-skew-y-1{
  --tw-skew-y: -1deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0{
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-0{
  --tw-scale-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-100{
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-gpu{
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes bounce{

  0%, 100%{
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50%{
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}
.animate-bounce{
  animation: bounce 1s infinite;
}
@keyframes bounceIn{

  0%{
    transform: scale(0.3);
    opacity: 0;
  }

  50%{
    transform: scale(1.05);
    opacity: 0.8;
  }

  100%{
    transform: scale(1);
    opacity: 1;
  }
}
.animate-bounce-in{
  animation: bounceIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
@keyframes pulse{

  50%{
    opacity: 0.5;
  }

  0%, 100%{
    opacity: 1;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes rotateSlow{

  0%{
    transform: rotate(0deg);
  }

  100%{
    transform: rotate(360deg);
  }
}
.animate-rotate-slow{
  animation: rotateSlow 8s linear infinite;
}
@keyframes slideRight{

  0%{
    transform: translateX(-10px);
    opacity: 0;
  }

  100%{
    transform: translateX(0);
    opacity: 1;
  }
}
.animate-slide-right{
  animation: slideRight 0.3s ease-in-out;
}
@keyframes spin{

  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-not-allowed{
  cursor: not-allowed;
}
.cursor-pointer{
  cursor: pointer;
}
.touch-none{
  touch-action: none;
}
.select-none{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none{
  resize: none;
}
.resize-y{
  resize: vertical;
}
.resize{
  resize: both;
}
.list-inside{
  list-style-position: inside;
}
.list-decimal{
  list-style-type: decimal;
}
.list-disc{
  list-style-type: disc;
}
.list-none{
  list-style-type: none;
}
.appearance-none{
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12{
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3{
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4{
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.flex-row{
  flex-direction: row;
}
.flex-row-reverse{
  flex-direction: row-reverse;
}
.flex-col{
  flex-direction: column;
}
.flex-col-reverse{
  flex-direction: column-reverse;
}
.flex-wrap{
  flex-wrap: wrap;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.items-baseline{
  align-items: baseline;
}
.justify-start{
  justify-content: flex-start;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.justify-around{
  justify-content: space-around;
}
.gap-1{
  gap: 0.25rem;
}
.gap-1\.5{
  gap: 0.375rem;
}
.gap-10{
  gap: 2.5rem;
}
.gap-12{
  gap: 3rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-4{
  gap: 1rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-8{
  gap: 2rem;
}
.gap-x-6{
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-y-2{
  row-gap: 0.5rem;
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.space-x-reverse > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 1;
}
.divide-y > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.divide-slate-200 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-divide-opacity, 1));
}
.self-center{
  align-self: center;
}
.overflow-auto{
  overflow: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.whitespace-pre-line{
  white-space: pre-line;
}
.whitespace-pre-wrap{
  white-space: pre-wrap;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-3xl{
  border-radius: 1.5rem;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: 0.5rem;
}
.rounded-md{
  border-radius: 0.375rem;
}
.rounded-none{
  border-radius: 0px;
}
.rounded-sm{
  border-radius: 0.125rem;
}
.rounded-xl{
  border-radius: 0.75rem;
}
.rounded-l-none{
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.rounded-r-none{
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.rounded-t{
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.rounded-t-full{
  border-top-left-radius: 9999px;
  border-top-right-radius: 9999px;
}
.rounded-t-lg{
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.rounded-t-md{
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}
.rounded-t-xl{
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}
.rounded-bl-md{
  border-bottom-left-radius: 0.375rem;
}
.rounded-tl-lg{
  border-top-left-radius: 0.5rem;
}
.rounded-tr-lg{
  border-top-right-radius: 0.5rem;
}
.border{
  border-width: 1px;
}
.border-0{
  border-width: 0px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-x-0{
  border-left-width: 0px;
  border-right-width: 0px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-b-2{
  border-bottom-width: 2px;
}
.border-l-2{
  border-left-width: 2px;
}
.border-l-4{
  border-left-width: 4px;
}
.border-r{
  border-right-width: 1px;
}
.border-r-2{
  border-right-width: 2px;
}
.border-t{
  border-top-width: 1px;
}
.border-t-2{
  border-top-width: 2px;
}
.border-dashed{
  border-style: dashed;
}
.border-blue-200{
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-400{
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-blue-500{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-current{
  border-color: currentColor;
}
.border-error-500{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-green-400{
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}
.border-green-500{
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.border-primary-100{
  --tw-border-opacity: 1;
  border-color: rgb(243 232 255 / var(--tw-border-opacity, 1));
}
.border-primary-200{
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}
.border-primary-500{
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.border-primary-600{
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}
.border-red-400{
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.border-red-500{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-slate-100{
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity, 1));
}
.border-slate-200{
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}
.border-slate-200\/50{
  border-color: rgb(226 232 240 / 0.5);
}
.border-slate-200\/80{
  border-color: rgb(226 232 240 / 0.8);
}
.border-slate-300{
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}
.border-slate-600{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}
.border-slate-700{
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));
}
.border-slate-800{
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));
}
.border-slate-800\/30{
  border-color: rgb(30 41 59 / 0.3);
}
.border-slate-800\/50{
  border-color: rgb(30 41 59 / 0.5);
}
.border-slate-800\/80{
  border-color: rgb(30 41 59 / 0.8);
}
.border-transparent{
  border-color: transparent;
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/20{
  border-color: rgb(255 255 255 / 0.2);
}
.border-green-300{
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}
.border-t-transparent{
  border-top-color: transparent;
}
.bg-accent-300{
  --tw-bg-opacity: 1;
  background-color: rgb(253 186 116 / var(--tw-bg-opacity, 1));
}
.bg-accent-300\/20{
  background-color: rgb(253 186 116 / 0.2);
}
.bg-accent-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-accent-500\/10{
  background-color: rgb(249 115 22 / 0.1);
}
.bg-accent-500\/20{
  background-color: rgb(249 115 22 / 0.2);
}
.bg-accent-500\/30{
  background-color: rgb(249 115 22 / 0.3);
}
.bg-amber-400{
  --tw-bg-opacity: 1;
  background-color: rgb(251 191 36 / var(--tw-bg-opacity, 1));
}
.bg-amber-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.bg-amber-500{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}
.bg-amber-500\/20{
  background-color: rgb(245 158 11 / 0.2);
}
.bg-amber-700{
  --tw-bg-opacity: 1;
  background-color: rgb(180 83 9 / var(--tw-bg-opacity, 1));
}
.bg-amber-900\/20{
  background-color: rgb(120 53 15 / 0.2);
}
.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/20{
  background-color: rgb(0 0 0 / 0.2);
}
.bg-black\/30{
  background-color: rgb(0 0 0 / 0.3);
}
.bg-black\/50{
  background-color: rgb(0 0 0 / 0.5);
}
.bg-black\/60{
  background-color: rgb(0 0 0 / 0.6);
}
.bg-black\/70{
  background-color: rgb(0 0 0 / 0.7);
}
.bg-blue-100{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-400{
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/20{
  background-color: rgb(59 130 246 / 0.2);
}
.bg-blue-500\/90{
  background-color: rgb(59 130 246 / 0.9);
}
.bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-blue-900\/20{
  background-color: rgb(30 58 138 / 0.2);
}
.bg-blue-900\/30{
  background-color: rgb(30 58 138 / 0.3);
}
.bg-cyan-100{
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));
}
.bg-cyan-500{
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));
}
.bg-error-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-green-100{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-400{
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}
.bg-green-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-500\/20{
  background-color: rgb(34 197 94 / 0.2);
}
.bg-green-500\/90{
  background-color: rgb(34 197 94 / 0.9);
}
.bg-green-600{
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-green-900\/20{
  background-color: rgb(20 83 45 / 0.2);
}
.bg-indigo-100{
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}
.bg-primary-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-primary-200{
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}
.bg-primary-300{
  --tw-bg-opacity: 1;
  background-color: rgb(216 180 254 / var(--tw-bg-opacity, 1));
}
.bg-primary-300\/20{
  background-color: rgb(216 180 254 / 0.2);
}
.bg-primary-400{
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}
.bg-primary-50{
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.bg-primary-50\/50{
  background-color: rgb(250 245 255 / 0.5);
}
.bg-primary-50\/90{
  background-color: rgb(250 245 255 / 0.9);
}
.bg-primary-500{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-primary-500\/10{
  background-color: rgb(168 85 247 / 0.1);
}
.bg-primary-500\/20{
  background-color: rgb(168 85 247 / 0.2);
}
.bg-primary-500\/30{
  background-color: rgb(168 85 247 / 0.3);
}
.bg-primary-500\/5{
  background-color: rgb(168 85 247 / 0.05);
}
.bg-primary-500\/90{
  background-color: rgb(168 85 247 / 0.9);
}
.bg-primary-600{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.bg-primary-700\/30{
  background-color: rgb(126 34 206 / 0.3);
}
.bg-primary-900{
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));
}
.bg-primary-900\/20{
  background-color: rgb(88 28 135 / 0.2);
}
.bg-primary-900\/30{
  background-color: rgb(88 28 135 / 0.3);
}
.bg-purple-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-400{
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}
.bg-purple-500{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-purple-500\/20{
  background-color: rgb(168 85 247 / 0.2);
}
.bg-red-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-500\/80{
  background-color: rgb(239 68 68 / 0.8);
}
.bg-red-500\/90{
  background-color: rgb(239 68 68 / 0.9);
}
.bg-red-600{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-red-900\/20{
  background-color: rgb(127 29 29 / 0.2);
}
.bg-secondary-100{
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));
}
.bg-secondary-50{
  --tw-bg-opacity: 1;
  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));
}
.bg-secondary-500{
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));
}
.bg-secondary-500\/30{
  background-color: rgb(6 182 212 / 0.3);
}
.bg-slate-100{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.bg-slate-100\/90{
  background-color: rgb(241 245 249 / 0.9);
}
.bg-slate-200{
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}
.bg-slate-300{
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity, 1));
}
.bg-slate-400{
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity, 1));
}
.bg-slate-50{
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}
.bg-slate-500{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}
.bg-slate-600{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.bg-slate-700{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}
.bg-slate-700\/30{
  background-color: rgb(51 65 85 / 0.3);
}
.bg-slate-700\/50{
  background-color: rgb(51 65 85 / 0.5);
}
.bg-slate-800{
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}
.bg-slate-800\/5{
  background-color: rgb(30 41 59 / 0.05);
}
.bg-slate-800\/50{
  background-color: rgb(30 41 59 / 0.5);
}
.bg-slate-800\/80{
  background-color: rgb(30 41 59 / 0.8);
}
.bg-slate-800\/95{
  background-color: rgb(30 41 59 / 0.95);
}
.bg-slate-900{
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}
.bg-slate-900\/90{
  background-color: rgb(15 23 42 / 0.9);
}
.bg-slate-950{
  --tw-bg-opacity: 1;
  background-color: rgb(2 6 23 / var(--tw-bg-opacity, 1));
}
.bg-success-50{
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}
.bg-transparent{
  background-color: transparent;
}
.bg-warning-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/10{
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/20{
  background-color: rgb(255 255 255 / 0.2);
}
.bg-white\/40{
  background-color: rgb(255 255 255 / 0.4);
}
.bg-white\/80{
  background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/90{
  background-color: rgb(255 255 255 / 0.9);
}
.bg-white\/95{
  background-color: rgb(255 255 255 / 0.95);
}
.bg-yellow-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-400{
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-yellow-900\/30{
  background-color: rgb(113 63 18 / 0.3);
}
.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-opacity-50{
  --tw-bg-opacity: 0.5;
}
.bg-opacity-75{
  --tw-bg-opacity: 0.75;
}
.bg-opacity-90{
  --tw-bg-opacity: 0.9;
}
.bg-opacity-95{
  --tw-bg-opacity: 0.95;
}
.bg-\[conic-gradient\(from_0deg\2c transparent_0\%\2c transparent_40\%\2c rgba\(255\2c 255\2c 255\2c 0\.2\)_45\%\2c transparent_50\%\2c transparent_100\%\)\]{
  background-image: conic-gradient(from 0deg,transparent 0%,transparent 40%,rgba(255,255,255,0.2) 45%,transparent 50%,transparent 100%);
}
.bg-\[conic-gradient\(from_180deg\2c transparent_0\%\2c transparent_40\%\2c rgba\(255\2c 255\2c 255\2c 0\.2\)_45\%\2c transparent_50\%\2c transparent_100\%\)\]{
  background-image: conic-gradient(from 180deg,transparent 0%,transparent 40%,rgba(255,255,255,0.2) 45%,transparent 50%,transparent 100%);
}
.bg-\[conic-gradient\(from_270deg\2c transparent_0\%\2c transparent_40\%\2c rgba\(255\2c 255\2c 255\2c 0\.2\)_45\%\2c transparent_50\%\2c transparent_100\%\)\]{
  background-image: conic-gradient(from 270deg,transparent 0%,transparent 40%,rgba(255,255,255,0.2) 45%,transparent 50%,transparent 100%);
}
.bg-\[conic-gradient\(from_90deg\2c transparent_0\%\2c transparent_40\%\2c rgba\(255\2c 255\2c 255\2c 0\.2\)_45\%\2c transparent_50\%\2c transparent_100\%\)\]{
  background-image: conic-gradient(from 90deg,transparent 0%,transparent 40%,rgba(255,255,255,0.2) 45%,transparent 50%,transparent 100%);
}
.bg-\[linear-gradient\(to_right\2c \#80808012_1px\2c transparent_1px\)\2c linear-gradient\(to_bottom\2c \#80808012_1px\2c transparent_1px\)\]{
  background-image: linear-gradient(to right,#80808012 1px,transparent 1px),linear-gradient(to bottom,#80808012 1px,transparent 1px);
}
.bg-\[radial-gradient\(circle_at_center\2c rgba\(255\2c 255\2c 255\2c 0\.15\)_0\%\2c transparent_70\%\)\]{
  background-image: radial-gradient(circle at center,rgba(255,255,255,0.15) 0%,transparent 70%);
}
.bg-\[url\(\'\/images\/pattern-dots\.svg\'\)\]{
  background-image: url('/images/pattern-dots.svg');
}
.bg-gradient-to-b{
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t{
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.from-accent-300{
  --tw-gradient-from: #FDBA74 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 186 116 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-accent-500{
  --tw-gradient-from: #F97316 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-accent-500\/0{
  --tw-gradient-from: rgb(249 115 22 / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-accent-500\/40{
  --tw-gradient-from: rgb(249 115 22 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-accent-600{
  --tw-gradient-from: #EA580C var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 88 12 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-300{
  --tw-gradient-from: #fcd34d var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(252 211 77 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-500\/0{
  --tw-gradient-from: rgb(245 158 11 / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-500\/10{
  --tw-gradient-from: rgb(245 158 11 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-500\/40{
  --tw-gradient-from: rgb(245 158 11 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/50{
  --tw-gradient-from: rgb(0 0 0 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/60{
  --tw-gradient-from: rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/70{
  --tw-gradient-from: rgb(0 0 0 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-300{
  --tw-gradient-from: #93c5fd var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 197 253 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50{
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500{
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500\/0{
  --tw-gradient-from: rgb(59 130 246 / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500\/10{
  --tw-gradient-from: rgb(59 130 246 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500\/40{
  --tw-gradient-from: rgb(59 130 246 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-300{
  --tw-gradient-from: #86efac var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(134 239 172 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500\/0{
  --tw-gradient-from: rgb(34 197 94 / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500\/10{
  --tw-gradient-from: rgb(34 197 94 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500\/40{
  --tw-gradient-from: rgb(34 197 94 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-100{
  --tw-gradient-from: #F3E8FF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(243 232 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-50{
  --tw-gradient-from: #FAF5FF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-50\/30{
  --tw-gradient-from: rgb(250 245 255 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-500{
  --tw-gradient-from: #A855F7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-500\/0{
  --tw-gradient-from: rgb(168 85 247 / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-500\/10{
  --tw-gradient-from: rgb(168 85 247 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-500\/20{
  --tw-gradient-from: rgb(168 85 247 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-600{
  --tw-gradient-from: #9333EA var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-800{
  --tw-gradient-from: #6B21A8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(107 33 168 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-900{
  --tw-gradient-from: #581C87 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-900\/80{
  --tw-gradient-from: rgb(88 28 135 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-900\/90{
  --tw-gradient-from: rgb(88 28 135 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-300{
  --tw-gradient-from: #d8b4fe var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(216 180 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500\/0{
  --tw-gradient-from: rgb(168 85 247 / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500\/10{
  --tw-gradient-from: rgb(168 85 247 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500\/40{
  --tw-gradient-from: rgb(168 85 247 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-secondary-500{
  --tw-gradient-from: #06B6D4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-50{
  --tw-gradient-from: #F8FAFC var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-800{
  --tw-gradient-from: #1E293B var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-900{
  --tw-gradient-from: #0F172A var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent{
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white{
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white\/0{
  --tw-gradient-from: rgb(255 255 255 / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-accent-500{
  --tw-gradient-to: rgb(249 115 22 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #F97316 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-accent-500\/20{
  --tw-gradient-to: rgb(249 115 22 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(249 115 22 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-accent-500\/80{
  --tw-gradient-to: rgb(249 115 22 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(249 115 22 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-black\/0{
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-black\/20{
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-black\/30{
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-black\/50{
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary-100{
  --tw-gradient-to: rgb(243 232 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #F3E8FF var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary-50\/30{
  --tw-gradient-to: rgb(250 245 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(250 245 255 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary-500\/0{
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary-700{
  --tw-gradient-to: rgb(126 34 206 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #7E22CE var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary-800{
  --tw-gradient-to: rgb(107 33 168 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #6B21A8 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary-900\/40{
  --tw-gradient-to: rgb(88 28 135 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(88 28 135 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-secondary-900\/90{
  --tw-gradient-to: rgb(22 78 99 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(22 78 99 / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent{
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white{
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/20{
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/30{
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-accent-100{
  --tw-gradient-to: #FFEDD5 var(--tw-gradient-to-position);
}
.to-accent-300{
  --tw-gradient-to: #FDBA74 var(--tw-gradient-to-position);
}
.to-accent-400{
  --tw-gradient-to: #FB923C var(--tw-gradient-to-position);
}
.to-accent-50{
  --tw-gradient-to: #FFF7ED var(--tw-gradient-to-position);
}
.to-accent-50\/30{
  --tw-gradient-to: rgb(255 247 237 / 0.3) var(--tw-gradient-to-position);
}
.to-accent-500{
  --tw-gradient-to: #F97316 var(--tw-gradient-to-position);
}
.to-accent-500\/0{
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
}
.to-accent-500\/10{
  --tw-gradient-to: rgb(249 115 22 / 0.1) var(--tw-gradient-to-position);
}
.to-accent-500\/20{
  --tw-gradient-to: rgb(249 115 22 / 0.2) var(--tw-gradient-to-position);
}
.to-accent-600{
  --tw-gradient-to: #EA580C var(--tw-gradient-to-position);
}
.to-accent-700{
  --tw-gradient-to: #C2410C var(--tw-gradient-to-position);
}
.to-amber-700\/0{
  --tw-gradient-to: rgb(180 83 9 / 0) var(--tw-gradient-to-position);
}
.to-amber-700\/10{
  --tw-gradient-to: rgb(180 83 9 / 0.1) var(--tw-gradient-to-position);
}
.to-amber-700\/40{
  --tw-gradient-to: rgb(180 83 9 / 0.4) var(--tw-gradient-to-position);
}
.to-black\/0{
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
}
.to-black\/10{
  --tw-gradient-to: rgb(0 0 0 / 0.1) var(--tw-gradient-to-position);
}
.to-black\/20{
  --tw-gradient-to: rgb(0 0 0 / 0.2) var(--tw-gradient-to-position);
}
.to-black\/30{
  --tw-gradient-to: rgb(0 0 0 / 0.3) var(--tw-gradient-to-position);
}
.to-black\/60{
  --tw-gradient-to: rgb(0 0 0 / 0.6) var(--tw-gradient-to-position);
}
.to-blue-100{
  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);
}
.to-blue-700\/0{
  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);
}
.to-blue-700\/10{
  --tw-gradient-to: rgb(29 78 216 / 0.1) var(--tw-gradient-to-position);
}
.to-blue-700\/40{
  --tw-gradient-to: rgb(29 78 216 / 0.4) var(--tw-gradient-to-position);
}
.to-green-500{
  --tw-gradient-to: #22c55e var(--tw-gradient-to-position);
}
.to-green-700\/0{
  --tw-gradient-to: rgb(21 128 61 / 0) var(--tw-gradient-to-position);
}
.to-green-700\/10{
  --tw-gradient-to: rgb(21 128 61 / 0.1) var(--tw-gradient-to-position);
}
.to-green-700\/40{
  --tw-gradient-to: rgb(21 128 61 / 0.4) var(--tw-gradient-to-position);
}
.to-primary-100{
  --tw-gradient-to: #F3E8FF var(--tw-gradient-to-position);
}
.to-primary-50{
  --tw-gradient-to: #FAF5FF var(--tw-gradient-to-position);
}
.to-primary-500{
  --tw-gradient-to: #A855F7 var(--tw-gradient-to-position);
}
.to-primary-500\/0{
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
}
.to-primary-500\/40{
  --tw-gradient-to: rgb(168 85 247 / 0.4) var(--tw-gradient-to-position);
}
.to-primary-600{
  --tw-gradient-to: #9333EA var(--tw-gradient-to-position);
}
.to-primary-700{
  --tw-gradient-to: #7E22CE var(--tw-gradient-to-position);
}
.to-primary-800{
  --tw-gradient-to: #6B21A8 var(--tw-gradient-to-position);
}
.to-primary-900\/90{
  --tw-gradient-to: rgb(88 28 135 / 0.9) var(--tw-gradient-to-position);
}
.to-purple-700\/0{
  --tw-gradient-to: rgb(126 34 206 / 0) var(--tw-gradient-to-position);
}
.to-purple-700\/10{
  --tw-gradient-to: rgb(126 34 206 / 0.1) var(--tw-gradient-to-position);
}
.to-purple-700\/40{
  --tw-gradient-to: rgb(126 34 206 / 0.4) var(--tw-gradient-to-position);
}
.to-secondary-500{
  --tw-gradient-to: #06B6D4 var(--tw-gradient-to-position);
}
.to-secondary-500\/10{
  --tw-gradient-to: rgb(6 182 212 / 0.1) var(--tw-gradient-to-position);
}
.to-secondary-600{
  --tw-gradient-to: #0891B2 var(--tw-gradient-to-position);
}
.to-secondary-800{
  --tw-gradient-to: #155E75 var(--tw-gradient-to-position);
}
.to-secondary-900{
  --tw-gradient-to: #164E63 var(--tw-gradient-to-position);
}
.to-secondary-900\/80{
  --tw-gradient-to: rgb(22 78 99 / 0.8) var(--tw-gradient-to-position);
}
.to-slate-50{
  --tw-gradient-to: #F8FAFC var(--tw-gradient-to-position);
}
.to-slate-800{
  --tw-gradient-to: #1E293B var(--tw-gradient-to-position);
}
.to-slate-900{
  --tw-gradient-to: #0F172A var(--tw-gradient-to-position);
}
.to-transparent{
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-white{
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}
.to-white\/0{
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
}
.bg-\[size\:24px_24px\]{
  background-size: 24px 24px;
}
.bg-cover{
  background-size: cover;
}
.bg-clip-text{
  -webkit-background-clip: text;
          background-clip: text;
}
.bg-center{
  background-position: center;
}
.bg-repeat{
  background-repeat: repeat;
}
.fill-current{
  fill: currentColor;
}
.fill-primary-500{
  fill: #A855F7;
}
.fill-yellow-400{
  fill: #facc15;
}
.fill-yellow-500{
  fill: #eab308;
}
.object-contain{
  -o-object-fit: contain;
     object-fit: contain;
}
.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}
.object-fill{
  -o-object-fit: fill;
     object-fit: fill;
}
.object-none{
  -o-object-fit: none;
     object-fit: none;
}
.object-center{
  -o-object-position: center;
     object-position: center;
}
.p-0{
  padding: 0px;
}
.p-1{
  padding: 0.25rem;
}
.p-1\.5{
  padding: 0.375rem;
}
.p-12{
  padding: 3rem;
}
.p-2{
  padding: 0.5rem;
}
.p-2\.5{
  padding: 0.625rem;
}
.p-3{
  padding: 0.75rem;
}
.p-4{
  padding: 1rem;
}
.p-5{
  padding: 1.25rem;
}
.p-6{
  padding: 1.5rem;
}
.p-8{
  padding: 2rem;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5{
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-10{
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10{
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20{
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-24{
  padding-top: 6rem;
  padding-bottom: 6rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-0{
  padding-bottom: 0px;
}
.pb-16{
  padding-bottom: 4rem;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pb-24{
  padding-bottom: 6rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pb-6{
  padding-bottom: 1.5rem;
}
.pl-0{
  padding-left: 0px;
}
.pl-10{
  padding-left: 2.5rem;
}
.pl-11{
  padding-left: 2.75rem;
}
.pl-2{
  padding-left: 0.5rem;
}
.pl-3{
  padding-left: 0.75rem;
}
.pl-4{
  padding-left: 1rem;
}
.pl-6{
  padding-left: 1.5rem;
}
.pl-7{
  padding-left: 1.75rem;
}
.pl-9{
  padding-left: 2.25rem;
}
.pr-1{
  padding-right: 0.25rem;
}
.pr-10{
  padding-right: 2.5rem;
}
.pr-11{
  padding-right: 2.75rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pr-3{
  padding-right: 0.75rem;
}
.pr-4{
  padding-right: 1rem;
}
.pr-7{
  padding-right: 1.75rem;
}
.pr-8{
  padding-right: 2rem;
}
.pr-9{
  padding-right: 2.25rem;
}
.pt-0{
  padding-top: 0px;
}
.pt-16{
  padding-top: 4rem;
}
.pt-2{
  padding-top: 0.5rem;
}
.pt-20{
  padding-top: 5rem;
}
.pt-24{
  padding-top: 6rem;
}
.pt-3{
  padding-top: 0.75rem;
}
.pt-4{
  padding-top: 1rem;
}
.pt-5{
  padding-top: 1.25rem;
}
.pt-8{
  padding-top: 2rem;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.font-sans{
  font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl{
  font-size: 3rem;
  line-height: 1;
}
.text-6xl{
  font-size: 3.75rem;
  line-height: 1;
}
.text-\[10px\]{
  font-size: 10px;
}
.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold{
  font-weight: 700;
}
.font-extrabold{
  font-weight: 800;
}
.font-medium{
  font-weight: 500;
}
.font-semibold{
  font-weight: 600;
}
.uppercase{
  text-transform: uppercase;
}
.italic{
  font-style: italic;
}
.leading-8{
  line-height: 2rem;
}
.leading-none{
  line-height: 1;
}
.leading-relaxed{
  line-height: 1.625;
}
.leading-tight{
  line-height: 1.25;
}
.tracking-tight{
  letter-spacing: -0.025em;
}
.tracking-wide{
  letter-spacing: 0.025em;
}
.tracking-wider{
  letter-spacing: 0.05em;
}
.text-accent-300{
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}
.text-accent-400{
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.text-accent-500{
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-amber-300{
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}
.text-amber-500{
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}
.text-amber-600{
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}
.text-amber-700{
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}
.text-blue-300{
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-cyan-500{
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity, 1));
}
.text-cyan-800{
  --tw-text-opacity: 1;
  color: rgb(21 94 117 / var(--tw-text-opacity, 1));
}
.text-error-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-300{
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.text-green-400{
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-800{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-indigo-800{
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}
.text-primary-300{
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.text-primary-400{
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.text-primary-50{
  --tw-text-opacity: 1;
  color: rgb(250 245 255 / var(--tw-text-opacity, 1));
}
.text-primary-500{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-primary-600{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-primary-700{
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.text-primary-800{
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-purple-300{
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.text-purple-500{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-purple-800{
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-red-300{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.text-red-400{
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-800{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-secondary-500{
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity, 1));
}
.text-secondary-700{
  --tw-text-opacity: 1;
  color: rgb(14 116 144 / var(--tw-text-opacity, 1));
}
.text-secondary-800{
  --tw-text-opacity: 1;
  color: rgb(21 94 117 / var(--tw-text-opacity, 1));
}
.text-sky-500{
  --tw-text-opacity: 1;
  color: rgb(14 165 233 / var(--tw-text-opacity, 1));
}
.text-slate-100{
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity, 1));
}
.text-slate-200{
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}
.text-slate-300{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}
.text-slate-400{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.text-slate-500{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.text-slate-600{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.text-slate-700{
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}
.text-slate-800{
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}
.text-slate-900{
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}
.text-success-500{
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}
.text-transparent{
  color: transparent;
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-white\/70{
  color: rgb(255 255 255 / 0.7);
}
.text-white\/80{
  color: rgb(255 255 255 / 0.8);
}
.text-white\/90{
  color: rgb(255 255 255 / 0.9);
}
.text-yellow-300{
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}
.text-yellow-400{
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-500{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-800{
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.line-through{
  text-decoration-line: line-through;
}
.underline-offset-2{
  text-underline-offset: 2px;
}
.placeholder-slate-500::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(100 116 139 / var(--tw-placeholder-opacity, 1));
}
.placeholder-slate-500::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(100 116 139 / var(--tw-placeholder-opacity, 1));
}
.placeholder-slate-400::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));
}
.placeholder-slate-400::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(148 163 184 / var(--tw-placeholder-opacity, 1));
}
.accent-primary-500{
  accent-color: #A855F7;
}
.opacity-0{
  opacity: 0;
}
.opacity-10{
  opacity: 0.1;
}
.opacity-100{
  opacity: 1;
}
.opacity-20{
  opacity: 0.2;
}
.opacity-30{
  opacity: 0.3;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-60{
  opacity: 0.6;
}
.opacity-70{
  opacity: 0.7;
}
.opacity-80{
  opacity: 0.8;
}
.opacity-90{
  opacity: 0.9;
}
.mix-blend-multiply{
  mix-blend-mode: multiply;
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-accent-500\/20{
  --tw-shadow-color: rgb(249 115 22 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-amber-500\/20{
  --tw-shadow-color: rgb(245 158 11 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-500\/10{
  --tw-shadow-color: rgb(59 130 246 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-500\/20{
  --tw-shadow-color: rgb(59 130 246 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-500\/20{
  --tw-shadow-color: rgb(34 197 94 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-primary-500\/10{
  --tw-shadow-color: rgb(168 85 247 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-primary-500\/20{
  --tw-shadow-color: rgb(168 85 247 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-500\/20{
  --tw-shadow-color: rgb(168 85 247 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-slate-200\/20{
  --tw-shadow-color: rgb(226 232 240 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-slate-300\/30{
  --tw-shadow-color: rgb(203 213 225 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-slate-900\/10{
  --tw-shadow-color: rgb(15 23 42 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-slate-900\/30{
  --tw-shadow-color: rgb(15 23 42 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-slate-900\/50{
  --tw-shadow-color: rgb(15 23 42 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.outline{
  outline-style: solid;
}
.ring-0{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-primary-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}
.ring-offset-white{
  --tw-ring-offset-color: #fff;
}
.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-3xl{
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[2px\]{
  --tw-blur: blur(2px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-lg{
  --tw-blur: blur(16px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-md{
  --tw-blur: blur(12px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-sm{
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-xl{
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.brightness-\[0\.8\]{
  --tw-brightness: brightness(0.8);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.contrast-\[1\.1\]{
  --tw-contrast: contrast(1.1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-md{
  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-lg{
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-md{
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-xl{
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow{
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.duration-500{
  transition-duration: 500ms;
}
.duration-700{
  transition-duration: 700ms;
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.will-change-transform{
  will-change: transform;
}
.\[animation-delay\:0\.2s\]{
  animation-delay: 0.2s;
}
.\[animation-delay\:0\.4s\]{
  animation-delay: 0.4s;
}
.\[mask-image\:linear-gradient\(to_bottom\2c transparent\2c black\)\]{
  -webkit-mask-image: linear-gradient(to bottom,transparent,black);
          mask-image: linear-gradient(to bottom,transparent,black);
}

@keyframes scroll {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(100%);
  }
}

.animate-scroll {
  animation: scroll 2s infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.backdrop-blur-sm {
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
  -webkit-backdrop-filter: blur(16px);
          backdrop-filter: blur(16px);
}

/* تحريكات للنوافذ المنبثقة والنماذج */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out forwards;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slideInLeft {
  animation: slideInLeft 0.3s ease-out forwards;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

[dir="rtl"] .ml-2,
[dir="rtl"] .ml-3,
[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-2,
[dir="rtl"] .mr-3,
[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

/* تحسينات إضافية للأجهزة المحمولة */
@media (max-width: 767px) {
  .mobile-stack{
    flex-direction: column;
  }

  .mobile-full-width{
    width: 100%;
  }

  .mobile-center{
    text-align: center;
  }

  .mobile-hidden{
    display: none;
  }

  .mobile-visible{
    display: block;
  }

  .mobile-touch-scroll{
    overflow: auto;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-no-scroll{
    overflow: hidden;
  }

  .mobile-spacing{
    margin-bottom: 1.5rem;
  }

  /* تحسين حجم الخط للقراءة على الأجهزة المحمولة */
  .mobile-text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .mobile-text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .mobile-text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

/* تأثيرات الحركة */
.hover-glow{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.hover-glow:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  box-shadow: 0 0 15px rgba(var(--primary-500), 0.5);
}

.dark .hover-glow:hover {
  box-shadow: 0 0 15px rgba(var(--primary-400), 0.5);
}

/* تأثير التحويم للصور */
.img-hover-zoom{
  overflow: hidden;
}

.img-hover-zoom img{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 500ms;
}

.img-hover-zoom:hover img{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* أنماط التباين العالي لتحسين إمكانية الوصول */
.high-contrast {
  --primary-500: 249 115 22;
  --primary-600: 234 88 12;
  --accent-500: 168 85 247;
  --accent-600: 147 51 234;
  --secondary-500: 6 182 212;
  --secondary-600: 8 145 178;

  /* زيادة تباين النصوص */
  --slate-500: 30 41 59;
  --slate-600: 15 23 42;
  --slate-700: 15 23 42;
  --slate-800: 15 23 42;
  --slate-900: 2 6 23;
}

.high-contrast.dark {
  --primary-500: 251 146 60;
  --primary-600: 249 115 22;
  --accent-500: 192 132 252;
  --accent-600: 168 85 247;
  --secondary-500: 34 211 238;
  --secondary-600: 6 182 212;

  /* زيادة تباين النصوص في الوضع المظلم */
  --slate-100: 255 255 255;
  --slate-200: 241 245 249;
  --slate-300: 226 232 240;
  --slate-400: 226 232 240;
  --slate-500: 226 232 240;
}

/* تحسينات إمكانية الوصول للروابط */
a:focus-visible{
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: #A855F7;
}

/* تحسين التركيز على العناصر التفاعلية */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible{
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: #A855F7;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
  --tw-ring-offset-width: 2px;
}
.file\:border-0::file-selector-button{
  border-width: 0px;
}
.file\:bg-transparent::file-selector-button{
  background-color: transparent;
}
.file\:font-medium::file-selector-button{
  font-weight: 500;
}
.placeholder\:text-slate-400::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.placeholder\:text-slate-400::placeholder{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.placeholder\:text-slate-500::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.placeholder\:text-slate-500::placeholder{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.after\:absolute::after{
  content: var(--tw-content);
  position: absolute;
}
.after\:bottom-0::after{
  content: var(--tw-content);
  bottom: 0px;
}
.after\:left-1\/2::after{
  content: var(--tw-content);
  left: 50%;
}
.after\:left-3::after{
  content: var(--tw-content);
  left: 0.75rem;
}
.after\:right-1\/2::after{
  content: var(--tw-content);
  right: 50%;
}
.after\:right-3::after{
  content: var(--tw-content);
  right: 0.75rem;
}
.after\:h-0\.5::after{
  content: var(--tw-content);
  height: 0.125rem;
}
.after\:rounded-full::after{
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\:bg-gradient-to-r::after{
  content: var(--tw-content);
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.after\:from-primary-500::after{
  content: var(--tw-content);
  --tw-gradient-from: #A855F7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.after\:to-accent-500::after{
  content: var(--tw-content);
  --tw-gradient-to: #F97316 var(--tw-gradient-to-position);
}
.after\:transition-all::after{
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.after\:duration-500::after{
  content: var(--tw-content);
  transition-duration: 500ms;
}
.hover\:-translate-x-1:hover{
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-1:hover{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-2:hover{
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:translate-x-1:hover{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:translate-y-\[-2px\]:hover{
  --tw-translate-y: -2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:translate-y-\[-3px\]:hover{
  --tw-translate-y: -3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-105:hover{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-110:hover{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:border-blue-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.hover\:border-blue-500:hover{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.hover\:border-pink-500:hover{
  --tw-border-opacity: 1;
  border-color: rgb(236 72 153 / var(--tw-border-opacity, 1));
}
.hover\:border-primary-200:hover{
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}
.hover\:border-primary-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}
.hover\:border-primary-500:hover{
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.hover\:border-primary-600:hover{
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}
.hover\:border-slate-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}
.hover\:border-slate-500:hover{
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.hover\:border-slate-600:hover{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}
.hover\:bg-blue-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-300:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}
.hover\:bg-pink-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(219 39 119 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-900\/20:hover{
  background-color: rgb(127 29 29 / 0.2);
}
.hover\:bg-secondary-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-100\/80:hover{
  background-color: rgb(241 245 249 / 0.8);
}
.hover\:bg-slate-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-200\/20:hover{
  background-color: rgb(226 232 240 / 0.2);
}
.hover\:bg-slate-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-50\/80:hover{
  background-color: rgb(248 250 252 / 0.8);
}
.hover\:bg-slate-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-800:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-900\/50:hover{
  background-color: rgb(15 23 42 / 0.5);
}
.hover\:bg-white:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-white\/10:hover{
  background-color: rgb(255 255 255 / 0.1);
}
.hover\:bg-white\/20:hover{
  background-color: rgb(255 255 255 / 0.2);
}
.hover\:bg-white\/30:hover{
  background-color: rgb(255 255 255 / 0.3);
}
.hover\:bg-white\/40:hover{
  background-color: rgb(255 255 255 / 0.4);
}
.hover\:bg-white\/60:hover{
  background-color: rgb(255 255 255 / 0.6);
}
.hover\:bg-blue-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.hover\:from-accent-600:hover{
  --tw-gradient-from: #EA580C var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 88 12 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-primary-600:hover{
  --tw-gradient-from: #9333EA var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-secondary-600:hover{
  --tw-gradient-from: #0891B2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(8 145 178 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:to-accent-700:hover{
  --tw-gradient-to: #C2410C var(--tw-gradient-to-position);
}
.hover\:to-primary-700:hover{
  --tw-gradient-to: #7E22CE var(--tw-gradient-to-position);
}
.hover\:to-secondary-700:hover{
  --tw-gradient-to: #0E7490 var(--tw-gradient-to-position);
}
.hover\:text-accent-400:hover{
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-400:hover{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-700:hover{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-800:hover{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.hover\:text-primary-300:hover{
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.hover\:text-primary-400:hover{
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.hover\:text-primary-500:hover{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.hover\:text-primary-600:hover{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.hover\:text-primary-700:hover{
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.hover\:text-red-300:hover{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.hover\:text-red-500:hover{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.hover\:text-red-700:hover{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.hover\:text-sky-600:hover{
  --tw-text-opacity: 1;
  color: rgb(2 132 199 / var(--tw-text-opacity, 1));
}
.hover\:text-slate-300:hover{
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}
.hover\:text-slate-600:hover{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.hover\:text-slate-700:hover{
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}
.hover\:text-slate-900:hover{
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}
.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.hover\:underline:hover{
  text-decoration-line: underline;
}
.hover\:opacity-100:hover{
  opacity: 1;
}
.hover\:shadow:hover{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-lg:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-md:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-xl:hover{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-accent-500\/30:hover{
  --tw-shadow-color: rgb(249 115 22 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.hover\:shadow-blue-500\/20:hover{
  --tw-shadow-color: rgb(59 130 246 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.hover\:shadow-pink-500\/20:hover{
  --tw-shadow-color: rgb(236 72 153 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.hover\:shadow-primary-500\/20:hover{
  --tw-shadow-color: rgb(168 85 247 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.hover\:after\:left-3:hover::after{
  content: var(--tw-content);
  left: 0.75rem;
}
.hover\:after\:right-3:hover::after{
  content: var(--tw-content);
  right: 0.75rem;
}
.focus\:not-sr-only:focus{
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
.focus\:absolute:focus{
  position: absolute;
}
.focus\:left-4:focus{
  left: 1rem;
}
.focus\:top-4:focus{
  top: 1rem;
}
.focus\:z-50:focus{
  z-index: 50;
}
.focus\:border-primary-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.focus\:bg-white:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-error-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}
.focus\:ring-primary-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}
.focus\:ring-primary-500\/30:focus{
  --tw-ring-color: rgb(168 85 247 / 0.3);
}
.focus\:ring-red-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}
.focus-visible\:outline-none:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:ring-2:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-accent-500:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-error-500:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-primary-500:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-red-500:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-secondary-500:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(6 182 212 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-slate-500:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-white:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-offset-2:focus-visible{
  --tw-ring-offset-width: 2px;
}
.active\:translate-y-0:active{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.active\:scale-95:active{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.active\:bg-slate-100:active{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.active\:shadow-none:active{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.disabled\:pointer-events-none:disabled{
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}
.disabled\:opacity-50:disabled{
  opacity: 0.5;
}
.group[open] .group-open\:rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:left-2{
  left: 0.5rem;
}
.group:hover .group-hover\:right-2{
  right: 0.5rem;
}
.group:hover .group-hover\:w-full{
  width: 100%;
}
.group:hover .group-hover\:-translate-y-1{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-x-1{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-x-2{
  --tw-translate-x: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-x-full{
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-y-\[-8px\]{
  --tw-translate-y: -8px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:rotate-12{
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:rotate-6{
  --tw-rotate: 6deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group\/feature:hover .group-hover\/feature\:scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-105{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-125{
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-\[1\.01\]{
  --tw-scale-x: 1.01;
  --tw-scale-y: 1.01;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-x-100{
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:border-accent-500{
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.group:hover .group-hover\:border-blue-500{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.group:hover .group-hover\:border-primary-300{
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}
.group:hover .group-hover\:border-primary-500{
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.group:hover .group-hover\:bg-accent-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-accent-600{
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-primary-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-primary-50{
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-primary-500{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-primary-600{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:from-accent-500\/20{
  --tw-gradient-from: rgb(249 115 22 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.group:hover .group-hover\:from-amber-500\/20{
  --tw-gradient-from: rgb(245 158 11 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.group:hover .group-hover\:from-black\/80{
  --tw-gradient-from: rgb(0 0 0 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.group:hover .group-hover\:from-blue-500\/20{
  --tw-gradient-from: rgb(59 130 246 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.group:hover .group-hover\:from-green-500\/20{
  --tw-gradient-from: rgb(34 197 94 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.group:hover .group-hover\:from-primary-500\/20{
  --tw-gradient-from: rgb(168 85 247 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.group:hover .group-hover\:from-purple-500\/20{
  --tw-gradient-from: rgb(168 85 247 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.group:hover .group-hover\:from-white\/5{
  --tw-gradient-from: rgb(255 255 255 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.group:hover .group-hover\:via-primary-500\/10{
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.group:hover .group-hover\:to-accent-500\/20{
  --tw-gradient-to: rgb(249 115 22 / 0.2) var(--tw-gradient-to-position);
}
.group:hover .group-hover\:to-amber-700\/20{
  --tw-gradient-to: rgb(180 83 9 / 0.2) var(--tw-gradient-to-position);
}
.group:hover .group-hover\:to-blue-700\/20{
  --tw-gradient-to: rgb(29 78 216 / 0.2) var(--tw-gradient-to-position);
}
.group:hover .group-hover\:to-green-700\/20{
  --tw-gradient-to: rgb(21 128 61 / 0.2) var(--tw-gradient-to-position);
}
.group:hover .group-hover\:to-primary-500\/20{
  --tw-gradient-to: rgb(168 85 247 / 0.2) var(--tw-gradient-to-position);
}
.group:hover .group-hover\:to-purple-700\/20{
  --tw-gradient-to: rgb(126 34 206 / 0.2) var(--tw-gradient-to-position);
}
.group:hover .group-hover\:to-white\/10{
  --tw-gradient-to: rgb(255 255 255 / 0.1) var(--tw-gradient-to-position);
}
.group\/feature:hover .group-hover\/feature\:text-primary-700{
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-amber-200{
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-blue-200{
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-green-200{
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-primary-500{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-primary-600{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-purple-200{
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-slate-700{
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:opacity-100{
  opacity: 1;
}
.group:hover .group-hover\:opacity-40{
  opacity: 0.4;
}
.group:hover .group-hover\:opacity-70{
  opacity: 0.7;
}
.group:hover .group-hover\:shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:hover .group-hover\:shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:hover .group-hover\:shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:hover .group-hover\:shadow-amber-500\/40{
  --tw-shadow-color: rgb(245 158 11 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.group:hover .group-hover\:shadow-blue-500\/40{
  --tw-shadow-color: rgb(59 130 246 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.group:hover .group-hover\:shadow-green-500\/40{
  --tw-shadow-color: rgb(34 197 94 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.group:hover .group-hover\:shadow-primary-500\/10{
  --tw-shadow-color: rgb(168 85 247 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.group:hover .group-hover\:shadow-primary-500\/20{
  --tw-shadow-color: rgb(168 85 247 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.group:hover .group-hover\:shadow-purple-500\/40{
  --tw-shadow-color: rgb(168 85 247 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.group:hover .group-hover\:blur-0{
  --tw-blur: blur(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.peer:disabled ~ .peer-disabled\:cursor-not-allowed{
  cursor: not-allowed;
}
.peer:disabled ~ .peer-disabled\:opacity-70{
  opacity: 0.7;
}
.dark\:divide-slate-600:is(.dark *) > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-divide-opacity, 1));
}
.dark\:divide-slate-700:is(.dark *) > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-divide-opacity, 1));
}
.dark\:border-blue-800\/50:is(.dark *){
  border-color: rgb(30 64 175 / 0.5);
}
.dark\:border-primary-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.dark\:border-primary-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}
.dark\:border-primary-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));
}
.dark\:border-primary-800\/50:is(.dark *){
  border-color: rgb(107 33 168 / 0.5);
}
.dark\:border-slate-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}
.dark\:border-slate-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));
}
.dark\:border-slate-700\/50:is(.dark *){
  border-color: rgb(51 65 85 / 0.5);
}
.dark\:border-slate-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));
}
.dark\:border-green-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}
.dark\:border-blue-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
}
.dark\:bg-accent-500\/10:is(.dark *){
  background-color: rgb(249 115 22 / 0.1);
}
.dark\:bg-blue-900\/30:is(.dark *){
  background-color: rgb(30 58 138 / 0.3);
}
.dark\:bg-cyan-900\/30:is(.dark *){
  background-color: rgb(22 78 99 / 0.3);
}
.dark\:bg-error-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-900\/30:is(.dark *){
  background-color: rgb(17 24 39 / 0.3);
}
.dark\:bg-green-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}
.dark\:bg-green-900\/30:is(.dark *){
  background-color: rgb(20 83 45 / 0.3);
}
.dark\:bg-indigo-900\/30:is(.dark *){
  background-color: rgb(49 46 129 / 0.3);
}
.dark\:bg-primary-400:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}
.dark\:bg-primary-500\/10:is(.dark *){
  background-color: rgb(168 85 247 / 0.1);
}
.dark\:bg-primary-500\/20:is(.dark *){
  background-color: rgb(168 85 247 / 0.2);
}
.dark\:bg-primary-500\/30:is(.dark *){
  background-color: rgb(168 85 247 / 0.3);
}
.dark\:bg-primary-800\/50:is(.dark *){
  background-color: rgb(107 33 168 / 0.5);
}
.dark\:bg-primary-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));
}
.dark\:bg-primary-900\/20:is(.dark *){
  background-color: rgb(88 28 135 / 0.2);
}
.dark\:bg-primary-900\/30:is(.dark *){
  background-color: rgb(88 28 135 / 0.3);
}
.dark\:bg-primary-900\/50:is(.dark *){
  background-color: rgb(88 28 135 / 0.5);
}
.dark\:bg-purple-900\/30:is(.dark *){
  background-color: rgb(88 28 135 / 0.3);
}
.dark\:bg-red-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.dark\:bg-red-900\/20:is(.dark *){
  background-color: rgb(127 29 29 / 0.2);
}
.dark\:bg-red-900\/30:is(.dark *){
  background-color: rgb(127 29 29 / 0.3);
}
.dark\:bg-secondary-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(22 78 99 / var(--tw-bg-opacity, 1));
}
.dark\:bg-secondary-900\/30:is(.dark *){
  background-color: rgb(22 78 99 / 0.3);
}
.dark\:bg-slate-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.dark\:bg-slate-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}
.dark\:bg-slate-700\/30:is(.dark *){
  background-color: rgb(51 65 85 / 0.3);
}
.dark\:bg-slate-700\/50:is(.dark *){
  background-color: rgb(51 65 85 / 0.5);
}
.dark\:bg-slate-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}
.dark\:bg-slate-800\/30:is(.dark *){
  background-color: rgb(30 41 59 / 0.3);
}
.dark\:bg-slate-800\/50:is(.dark *){
  background-color: rgb(30 41 59 / 0.5);
}
.dark\:bg-slate-800\/80:is(.dark *){
  background-color: rgb(30 41 59 / 0.8);
}
.dark\:bg-slate-800\/90:is(.dark *){
  background-color: rgb(30 41 59 / 0.9);
}
.dark\:bg-slate-800\/95:is(.dark *){
  background-color: rgb(30 41 59 / 0.95);
}
.dark\:bg-slate-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}
.dark\:bg-slate-900\/30:is(.dark *){
  background-color: rgb(15 23 42 / 0.3);
}
.dark\:bg-slate-900\/90:is(.dark *){
  background-color: rgb(15 23 42 / 0.9);
}
.dark\:bg-success-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(6 78 59 / var(--tw-bg-opacity, 1));
}
.dark\:bg-success-900\/30:is(.dark *){
  background-color: rgb(6 78 59 / 0.3);
}
.dark\:bg-warning-900\/30:is(.dark *){
  background-color: rgb(120 53 15 / 0.3);
}
.dark\:bg-white\/10:is(.dark *){
  background-color: rgb(255 255 255 / 0.1);
}
.dark\:bg-yellow-900\/30:is(.dark *){
  background-color: rgb(113 63 18 / 0.3);
}
.dark\:bg-green-900\/20:is(.dark *){
  background-color: rgb(20 83 45 / 0.2);
}
.dark\:bg-blue-900\/20:is(.dark *){
  background-color: rgb(30 58 138 / 0.2);
}
.dark\:from-blue-900\/30:is(.dark *){
  --tw-gradient-from: rgb(30 58 138 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-primary-400:is(.dark *){
  --tw-gradient-from: #C084FC var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-primary-900\/10:is(.dark *){
  --tw-gradient-from: rgb(88 28 135 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-primary-900\/20:is(.dark *){
  --tw-gradient-from: rgb(88 28 135 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-primary-900\/30:is(.dark *){
  --tw-gradient-from: rgb(88 28 135 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-primary-900\/50:is(.dark *){
  --tw-gradient-from: rgb(88 28 135 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-primary-900\/60:is(.dark *){
  --tw-gradient-from: rgb(88 28 135 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-slate-900:is(.dark *){
  --tw-gradient-from: #0F172A var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:via-slate-800:is(.dark *){
  --tw-gradient-to: rgb(30 41 59 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1E293B var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:via-slate-900\/80:is(.dark *){
  --tw-gradient-to: rgb(15 23 42 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(15 23 42 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:to-accent-400:is(.dark *){
  --tw-gradient-to: #FB923C var(--tw-gradient-to-position);
}
.dark\:to-accent-900\/10:is(.dark *){
  --tw-gradient-to: rgb(124 45 18 / 0.1) var(--tw-gradient-to-position);
}
.dark\:to-accent-900\/20:is(.dark *){
  --tw-gradient-to: rgb(124 45 18 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-accent-900\/50:is(.dark *){
  --tw-gradient-to: rgb(124 45 18 / 0.5) var(--tw-gradient-to-position);
}
.dark\:to-blue-800\/30:is(.dark *){
  --tw-gradient-to: rgb(30 64 175 / 0.3) var(--tw-gradient-to-position);
}
.dark\:to-primary-800\/30:is(.dark *){
  --tw-gradient-to: rgb(107 33 168 / 0.3) var(--tw-gradient-to-position);
}
.dark\:to-primary-900\/20:is(.dark *){
  --tw-gradient-to: rgb(88 28 135 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-secondary-900\/20:is(.dark *){
  --tw-gradient-to: rgb(22 78 99 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-slate-800:is(.dark *){
  --tw-gradient-to: #1E293B var(--tw-gradient-to-position);
}
.dark\:to-slate-800\/50:is(.dark *){
  --tw-gradient-to: rgb(30 41 59 / 0.5) var(--tw-gradient-to-position);
}
.dark\:to-slate-900:is(.dark *){
  --tw-gradient-to: #0F172A var(--tw-gradient-to-position);
}
.dark\:to-slate-900\/20:is(.dark *){
  --tw-gradient-to: rgb(15 23 42 / 0.2) var(--tw-gradient-to-position);
}
.dark\:text-accent-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.dark\:text-blue-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.dark\:text-blue-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.dark\:text-cyan-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(34 211 238 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.dark\:text-green-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.dark\:text-green-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.dark\:text-indigo-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
}
.dark\:text-primary-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.dark\:text-primary-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.dark\:text-primary-700:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.dark\:text-purple-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.dark\:text-red-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.dark\:text-secondary-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(103 232 249 / var(--tw-text-opacity, 1));
}
.dark\:text-sky-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(56 189 248 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.dark\:text-success-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}
.dark\:text-white:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.dark\:text-yellow-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.dark\:text-green-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}
.dark\:accent-primary-400:is(.dark *){
  accent-color: #C084FC;
}
.dark\:ring-primary-400:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));
}
.dark\:ring-offset-slate-800:is(.dark *){
  --tw-ring-offset-color: #1E293B;
}
.dark\:placeholder\:text-slate-500:is(.dark *)::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.dark\:placeholder\:text-slate-500:is(.dark *)::placeholder{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.dark\:hover\:border-primary-400:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-primary-500:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-primary-700:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-primary-800\/70:hover:is(.dark *){
  border-color: rgb(107 33 168 / 0.7);
}
.dark\:hover\:border-slate-600:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}
.dark\:hover\:bg-gray-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-primary-800\/50:hover:is(.dark *){
  background-color: rgb(107 33 168 / 0.5);
}
.dark\:hover\:bg-primary-900\/20:hover:is(.dark *){
  background-color: rgb(88 28 135 / 0.2);
}
.dark\:hover\:bg-primary-900\/30:hover:is(.dark *){
  background-color: rgb(88 28 135 / 0.3);
}
.dark\:hover\:bg-primary-900\/50:hover:is(.dark *){
  background-color: rgb(88 28 135 / 0.5);
}
.dark\:hover\:bg-red-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-secondary-900\/50:hover:is(.dark *){
  background-color: rgb(22 78 99 / 0.5);
}
.dark\:hover\:bg-slate-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-slate-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-slate-700\/50:hover:is(.dark *){
  background-color: rgb(51 65 85 / 0.5);
}
.dark\:hover\:bg-slate-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-slate-800\/30:hover:is(.dark *){
  background-color: rgb(30 41 59 / 0.3);
}
.dark\:hover\:bg-slate-800\/50:hover:is(.dark *){
  background-color: rgb(30 41 59 / 0.5);
}
.dark\:hover\:bg-success-900\/50:hover:is(.dark *){
  background-color: rgb(6 78 59 / 0.5);
}
.dark\:hover\:bg-warning-900\/50:hover:is(.dark *){
  background-color: rgb(120 53 15 / 0.5);
}
.dark\:hover\:bg-blue-900\/20:hover:is(.dark *){
  background-color: rgb(30 58 138 / 0.2);
}
.dark\:hover\:bg-red-900\/20:hover:is(.dark *){
  background-color: rgb(127 29 29 / 0.2);
}
.dark\:hover\:text-blue-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-blue-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-primary-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-primary-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-red-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-sky-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(125 211 252 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-slate-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-slate-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-white:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.dark\:focus\:border-primary-400:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.dark\:focus\:ring-primary-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-primary-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 51 234 / var(--tw-ring-opacity, 1));
}
.dark\:focus-visible\:ring-primary-400:focus-visible:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));
}
.dark\:active\:bg-slate-800:active:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}
.group:hover .dark\:group-hover\:bg-slate-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.group\/feature:hover .dark\:group-hover\/feature\:text-primary-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.group:hover .dark\:group-hover\:text-primary-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.group:hover .dark\:group-hover\:text-slate-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}
@media (min-width: 640px){

  .sm\:mb-0{
    margin-bottom: 0px;
  }

  .sm\:mt-0{
    margin-top: 0px;
  }

  .sm\:block{
    display: block;
  }

  .sm\:inline{
    display: inline;
  }

  .sm\:flex{
    display: flex;
  }

  .sm\:w-auto{
    width: auto;
  }

  .sm\:max-w-3xl{
    max-width: 48rem;
  }

  .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:items-center{
    align-items: center;
  }

  .sm\:justify-start{
    justify-content: flex-start;
  }

  .sm\:justify-end{
    justify-content: flex-end;
  }

  .sm\:justify-between{
    justify-content: space-between;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:text-left{
    text-align: left;
  }

  .sm\:text-right{
    text-align: right;
  }
}
@media (min-width: 768px){

  .md\:absolute{
    position: absolute;
  }

  .md\:left-1\/2{
    left: 50%;
  }

  .md\:top-0{
    top: 0px;
  }

  .md\:col-span-1{
    grid-column: span 1 / span 1;
  }

  .md\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3{
    grid-column: span 3 / span 3;
  }

  .md\:mb-0{
    margin-bottom: 0px;
  }

  .md\:mb-12{
    margin-bottom: 3rem;
  }

  .md\:ml-64{
    margin-left: 16rem;
  }

  .md\:mt-0{
    margin-top: 0px;
  }

  .md\:block{
    display: block;
  }

  .md\:flex{
    display: flex;
  }

  .md\:grid{
    display: grid;
  }

  .md\:hidden{
    display: none;
  }

  .md\:aspect-auto{
    aspect-ratio: auto;
  }

  .md\:h-80{
    height: 20rem;
  }

  .md\:h-full{
    height: 100%;
  }

  .md\:max-h-\[2000px\]{
    max-height: 2000px;
  }

  .md\:w-48{
    width: 12rem;
  }

  .md\:w-64{
    width: 16rem;
  }

  .md\:w-auto{
    width: auto;
  }

  .md\:min-w-\[400px\]{
    min-width: 400px;
  }

  .md\:max-w-md{
    max-width: 28rem;
  }

  .md\:-translate-x-1\/2{
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:-translate-y-1\/2{
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-x-0{
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:transform{
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row{
    flex-direction: row;
  }

  .md\:items-center{
    align-items: center;
  }

  .md\:justify-start{
    justify-content: flex-start;
  }

  .md\:justify-between{
    justify-content: space-between;
  }

  .md\:gap-8{
    gap: 2rem;
  }

  .md\:p-12{
    padding: 3rem;
  }

  .md\:p-6{
    padding: 1.5rem;
  }

  .md\:p-8{
    padding: 2rem;
  }

  .md\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:py-16{
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .md\:py-20{
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .md\:py-24{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .md\:py-28{
    padding-top: 7rem;
    padding-bottom: 7rem;
  }

  .md\:pb-20{
    padding-bottom: 5rem;
  }

  .md\:pt-16{
    padding-top: 4rem;
  }

  .md\:pt-28{
    padding-top: 7rem;
  }

  .md\:pt-4{
    padding-top: 1rem;
  }

  .md\:text-left{
    text-align: left;
  }

  .md\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:opacity-100{
    opacity: 1;
  }
}
@media (min-width: 1024px){

  .lg\:col-span-1{
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3{
    grid-column: span 3 / span 3;
  }

  .lg\:block{
    display: block;
  }

  .lg\:flex{
    display: flex;
  }

  .lg\:hidden{
    display: none;
  }

  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-6{
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:gap-10{
    gap: 2.5rem;
  }

  .lg\:gap-8{
    gap: 2rem;
  }

  .lg\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-7xl{
    font-size: 4.5rem;
    line-height: 1;
  }
}
.rtl\:space-x-reverse:where([dir="rtl"], [dir="rtl"] *) > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 1;
}
