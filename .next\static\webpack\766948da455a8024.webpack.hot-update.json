{"c": ["app/layout", "app/[locale]/not-found", "app/[locale]/page", "app/[locale]/terms-of-service/page", "app/[locale]/privacy-policy/page", "app/[locale]/returns/page", "app/[locale]/shipping/page", "app/[locale]/faq/page", "app/[locale]/contact/page", "app/[locale]/shop/page", "app/[locale]/production-lines/page", "app/[locale]/services/page", "app/[locale]/blog/page", "app/[locale]/clearance/page", "app/[locale]/shop/[slug]/page", "webpack"], "r": ["app/_not-found/page"], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CAkramYahya%5CDesktop%5Cecommercepro%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!", "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js", "(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js"]}