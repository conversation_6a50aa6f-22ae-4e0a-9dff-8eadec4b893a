/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false, // Temporarily disable strict mode
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      },
      {
        protocol: 'https',
        hostname: 'images.pexels.com',
      },
    ],
  },
  // Simplified configuration
  experimental: {
    optimizeCss: false, // Disable experimental features
    scrollRestoration: false,
  },
  webpack: (config, { isServer }) => {
    // Minimal webpack configuration
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  },
};

// Temporarily disable PWA to fix webpack issues
// const withPWA = require('./next-pwa.config');
// module.exports = withPWA(nextConfig);
module.exports = nextConfig;
