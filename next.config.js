/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      },
      {
        protocol: 'https',
        hostname: 'images.pexels.com',
      },
    ],
  },
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
  },
  webpack: (config, { isServer, webpack }) => {
    // Exclude better-sqlite3 from client-side bundle by treating it as an external
    // that will be available in the Node.js environment.
    if (!isServer) {
      config.externals.push({ // Ensure this is config.externals, not config.resolve.alias for this purpose
        'better-sqlite3': 'commonjs better-sqlite3',
      });
    }

    // Add rule for .node files (native modules)
    config.module.rules.push({
      test: /\.node$/,
      use: [
        {
          loader: 'node-loader',
          options: {
            name: '[name].[ext]', // Copies the .node file to the output directory
          },
        },
      ],
    });

    // Required for Next.js 12+ to correctly handle some Node.js built-in modules
    // if they are still being pulled in by some client-side dependency indirectly.
    // This might not be strictly necessary if 'externals' works as expected for better-sqlite3.
    if (!isServer) {
        config.resolve.fallback = {
            ...config.resolve.fallback, 
            fs: false, 
            path: false, 
            net: false, 
            tls: false, 
            child_process: false,
            // any other Node.js core modules that might cause issues
        };
    }

    return config;
  },
};

const withPWA = require('./next-pwa.config');
module.exports = withPWA(nextConfig);
