"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/services/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/services/page.tsx":
/*!********************************************!*\
  !*** ./src/app/[locale]/services/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Services)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/layout/MainLayout */ \"(app-pages-browser)/./src/components/layout/MainLayout.tsx\");\n/* harmony import */ var _pages_services_ServicesPageSimple__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../pages/services/ServicesPageSimple */ \"(app-pages-browser)/./src/pages/services/ServicesPageSimple.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Loading fallback component\nconst LoadingFallback = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\app\\\\[locale]\\\\services\\\\page.tsx\",\n            lineNumber: 10,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\app\\\\[locale]\\\\services\\\\page.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n_c = LoadingFallback;\nfunction Services() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MainLayout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingFallback, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\app\\\\[locale]\\\\services\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 27\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_services_ServicesPageSimple__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\app\\\\[locale]\\\\services\\\\page.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\app\\\\[locale]\\\\services\\\\page.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\app\\\\[locale]\\\\services\\\\page.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Services;\nvar _c, _c1;\n$RefreshReg$(_c, \"LoadingFallback\");\n$RefreshReg$(_c1, \"Services\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vc2VydmljZXMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVpQztBQUNrQztBQUNTO0FBRTVFLDZCQUE2QjtBQUM3QixNQUFNRyxrQkFBa0Isa0JBQ3RCLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs7Ozs7Ozs7OztLQUZiRjtBQU1TLFNBQVNHO0lBQ3RCLHFCQUNFLDhEQUFDTCxxRUFBVUE7a0JBQ1QsNEVBQUNELDJDQUFRQTtZQUFDTyx3QkFBVSw4REFBQ0o7Ozs7O3NCQUNuQiw0RUFBQ0QsMEVBQWtCQTs7Ozs7Ozs7Ozs7Ozs7O0FBSTNCO01BUndCSSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBa3JhbVlhaHlhXFxEZXNrdG9wXFxlY29tbWVyY2Vwcm9cXHNyY1xcYXBwXFxbbG9jYWxlXVxcc2VydmljZXNcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgU3VzcGVuc2UgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBNYWluTGF5b3V0IH0gZnJvbSAnLi4vLi4vLi4vY29tcG9uZW50cy9sYXlvdXQvTWFpbkxheW91dCc7XG5pbXBvcnQgU2VydmljZXNQYWdlU2ltcGxlIGZyb20gJy4uLy4uLy4uL3BhZ2VzL3NlcnZpY2VzL1NlcnZpY2VzUGFnZVNpbXBsZSc7XG5cbi8vIExvYWRpbmcgZmFsbGJhY2sgY29tcG9uZW50XG5jb25zdCBMb2FkaW5nRmFsbGJhY2sgPSAoKSA9PiAoXG4gIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItdC0yIGJvcmRlci1iLTIgYm9yZGVyLXByaW1hcnktNTAwXCI+PC9kaXY+XG4gIDwvZGl2PlxuKTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2VydmljZXMoKSB7XG4gIHJldHVybiAoXG4gICAgPE1haW5MYXlvdXQ+XG4gICAgICA8U3VzcGVuc2UgZmFsbGJhY2s9ezxMb2FkaW5nRmFsbGJhY2sgLz59PlxuICAgICAgICA8U2VydmljZXNQYWdlU2ltcGxlIC8+XG4gICAgICA8L1N1c3BlbnNlPlxuICAgIDwvTWFpbkxheW91dD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTdXNwZW5zZSIsIk1haW5MYXlvdXQiLCJTZXJ2aWNlc1BhZ2VTaW1wbGUiLCJMb2FkaW5nRmFsbGJhY2siLCJkaXYiLCJjbGFzc05hbWUiLCJTZXJ2aWNlcyIsImZhbGxiYWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/services/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/data/services.ts":
/*!******************************!*\
  !*** ./src/data/services.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   services: () => (/* binding */ services)\n/* harmony export */ });\nconst services = [\n    {\n        id: 'inspection',\n        name: 'Inspection Services',\n        name_ar: 'خدمات الفحص',\n        slug: 'inspection',\n        description: 'Comprehensive quality control and product inspection services at every stage of your supply chain.',\n        description_ar: 'خدمات شاملة لمراقبة الجودة وفحص المنتجات في كل مرحلة من مراحل سلسلة التوريد الخاصة بك.',\n        icon: 'Search',\n        features: [\n            'Pre-shipment quality inspections',\n            'During production checks',\n            'Container loading supervision',\n            'Factory audits',\n            'Product testing and verification',\n            'Detailed inspection reports with photos',\n            'Quality control consulting'\n        ],\n        features_ar: [\n            'فحص الجودة قبل الشحن',\n            'فحوصات أثناء الإنتاج',\n            'الإشراف على تحميل الحاويات',\n            'تدقيق المصانع',\n            'اختبار المنتجات والتحقق منها',\n            'تقارير فحص مفصلة مع صور',\n            'استشارات مراقبة الجودة'\n        ],\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'storage',\n        name: 'Storage Solutions',\n        name_ar: 'حلول التخزين',\n        slug: 'storage',\n        description: 'Secure, climate-controlled warehousing with advanced inventory management systems.',\n        description_ar: 'مستودعات آمنة ومتحكم في مناخها مع أنظمة متقدمة لإدارة المخزون.',\n        icon: 'Package',\n        features: [\n            'Climate-controlled facilities',\n            'Advanced security systems',\n            'Real-time inventory tracking',\n            'Short and long-term storage options',\n            'Fulfillment services',\n            'Cross-docking capabilities',\n            'Inventory management software'\n        ],\n        features_ar: [\n            'مرافق متحكم في مناخها',\n            'أنظمة أمان متقدمة',\n            'تتبع المخزون في الوقت الفعلي',\n            'خيارات تخزين قصيرة وطويلة الأجل',\n            'خدمات الوفاء بالطلبات',\n            'قدرات النقل المتقاطع',\n            'برامج إدارة المخزون'\n        ],\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'shipping',\n        name: 'Shipping & Logistics',\n        name_ar: 'الشحن والخدمات اللوجستية',\n        slug: 'shipping',\n        description: 'End-to-end shipping solutions including air freight, sea shipping, and door-to-door delivery.',\n        description_ar: 'حلول شحن متكاملة تشمل الشحن الجوي والبحري والتوصيل من الباب إلى الباب.',\n        icon: 'Truck',\n        features: [\n            'International air freight',\n            'Ocean freight services',\n            'Door-to-door delivery',\n            'Customs clearance',\n            'Cargo insurance',\n            'Track and trace systems',\n            'Specialized handling'\n        ],\n        features_ar: [\n            'الشحن الجوي الدولي',\n            'خدمات الشحن البحري',\n            'التوصيل من الباب إلى الباب',\n            'التخليص الجمركي',\n            'تأمين البضائع',\n            'أنظمة التتبع والتعقب',\n            'المناولة المتخصصة'\n        ],\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'order-management',\n        name: 'Order Management',\n        name_ar: 'إدارة الطلبات',\n        slug: 'order-management',\n        description: 'Streamlined wholesale and bulk order processing with dedicated support and competitive pricing.',\n        description_ar: 'معالجة مبسطة لطلبات الجملة والطلبات الكبيرة مع دعم مخصص وأسعار تنافسية.',\n        icon: 'ClipboardList',\n        features: [\n            'Volume-based pricing',\n            'Dedicated account management',\n            'Customized catalogs',\n            'Flexible payment terms',\n            'Priority support',\n            'Order tracking system',\n            'Bulk order processing'\n        ],\n        features_ar: [\n            'تسعير على أساس الحجم',\n            'إدارة حساب مخصصة',\n            'كتالوجات مخصصة',\n            'شروط دفع مرنة',\n            'دعم ذو أولوية',\n            'نظام تتبع الطلبات',\n            'معالجة الطلبات الكبيرة'\n        ],\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'certification',\n        name: 'Product Certification',\n        name_ar: 'شهادات المنتجات',\n        slug: 'certification',\n        description: 'Expert assistance with product certification and regulatory compliance requirements.',\n        description_ar: 'مساعدة خبيرة في شهادات المنتجات ومتطلبات الامتثال التنظيمية.',\n        icon: 'FileCheck',\n        features: [\n            'CE certification assistance',\n            'FDA compliance support',\n            'ISO certification guidance',\n            'Product testing coordination',\n            'Documentation preparation',\n            'Regulatory consulting',\n            'Compliance monitoring'\n        ],\n        features_ar: [\n            'المساعدة في شهادة CE',\n            'دعم الامتثال لمعايير FDA',\n            'توجيه شهادة ISO',\n            'تنسيق اختبار المنتجات',\n            'إعداد الوثائق',\n            'الاستشارات التنظيمية',\n            'مراقبة الامتثال'\n        ],\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'consulting',\n        name: 'Business Consulting',\n        name_ar: 'الاستشارات التجارية',\n        slug: 'consulting',\n        description: 'Strategic consulting services to optimize your operations and expand market presence.',\n        description_ar: 'خدمات استشارية استراتيجية لتحسين عملياتك وتوسيع تواجدك في السوق.',\n        icon: 'Users',\n        features: [\n            'Supply chain optimization',\n            'Market entry strategies',\n            'Operational efficiency',\n            'Cost reduction analysis',\n            'Vendor management',\n            'Risk assessment',\n            'Growth planning'\n        ],\n        features_ar: [\n            'تحسين سلسلة التوريد',\n            'استراتيجيات دخول السوق',\n            'كفاءة العمليات',\n            'تحليل خفض التكاليف',\n            'إدارة الموردين',\n            'تقييم المخاطر',\n            'تخطيط النمو'\n        ],\n        createdAt: new Date().toISOString()\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/services.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/pages/services/ServicesPageSimple.tsx":
/*!***************************************************!*\
  !*** ./src/pages/services/ServicesPageSimple.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServicesPageSimple)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Mail,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _data_services__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../data/services */ \"(app-pages-browser)/./src/data/services.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../stores/themeStore */ \"(app-pages-browser)/./src/stores/themeStore.ts\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ServicesPageSimple() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_5__.useThemeStore)();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_6__.useLanguageStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"py-16\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"text-4xl md:text-5xl font-bold mb-6\", isDarkMode ? \"text-white\" : \"text-slate-900\"),\n                                children: language === 'ar' ? 'خدمات الأعمال' : 'Business Services'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"text-lg md:text-xl mb-8\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\"),\n                                children: language === 'ar' ? 'خدمات دعم شاملة لتبسيط عملياتك وتعزيز كفاءة أعمالك.' : 'Comprehensive support services to streamline your operations and enhance business efficiency.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-center gap-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"primary\",\n                                    onClick: ()=>router.push(\"/\".concat(language, \"/contact\")),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"mr-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this),\n                                        language === 'ar' ? 'اتصل بنا' : 'Contact Us'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"py-16\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-50\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-3xl mx-auto text-center mb-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\",\n                                children: language === 'ar' ? 'خدماتنا' : 'Our Services'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: _data_services__WEBPACK_IMPORTED_MODULE_4__.services.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-xl mb-2 text-slate-900 dark:text-white\",\n                                                children: language === 'ar' ? service.name_ar || service.name : service.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 dark:text-slate-300 mb-6 flex-grow\",\n                                                    children: language === 'ar' ? service.description_ar || service.description : service.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 mt-auto\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"primary\",\n                                                            className: \"flex-1\",\n                                                            onClick: ()=>router.push(\"/\".concat(language, \"/contact\")),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                    lineNumber: 82,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                language === 'ar' ? 'احجز الخدمة' : 'Book Service'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                            lineNumber: 77,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"flex-1\",\n                                                            onClick: ()=>router.push(\"/\".concat(language, \"/services/\").concat(service.slug)),\n                                                            children: [\n                                                                language === 'ar' ? 'معرفة المزيد' : 'Learn More',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"ml-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                    lineNumber: 91,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, service.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"py-16\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-3xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6\",\n                                children: language === 'ar' ? 'هل أنت مستعد لتعزيز عمليات عملك؟' : 'Ready to Enhance Your Business Operations?'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-slate-600 dark:text-slate-300 mb-8\",\n                                children: language === 'ar' ? 'اتصل بفريقنا لمناقشة كيف يمكن لخدماتنا تلبية احتياجات عملك المحددة.' : 'Contact our team to discuss how our services can address your specific business needs.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        variant: \"primary\",\n                                        onClick: ()=>router.push(\"/\".concat(language, \"/contact\")),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this),\n                                            language === 'ar' ? 'اتصل بنا' : 'Contact Us'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        variant: \"outline\",\n                                        onClick: ()=>router.push(\"/\".concat(language, \"/contact\")),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            language === 'ar' ? 'طلب عرض سعر' : 'Request Quote'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_s(ServicesPageSimple, \"u6GZARKPQyVtUGiObkaOBRy+AdE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _stores_themeStore__WEBPACK_IMPORTED_MODULE_5__.useThemeStore,\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_6__.useLanguageStore\n    ];\n});\n_c = ServicesPageSimple;\nvar _c;\n$RefreshReg$(_c, \"ServicesPageSimple\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/pages/services/ServicesPageSimple.tsx\n"));

/***/ })

});