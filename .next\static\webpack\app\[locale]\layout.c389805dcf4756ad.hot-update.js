"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./src/data/clearanceItems.ts":
/*!************************************!*\
  !*** ./src/data/clearanceItems.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearanceItems: () => (/* binding */ clearanceItems)\n/* harmony export */ });\nconst clearanceItems = [\n    {\n        id: '1',\n        name: 'Bulk Electronics Components',\n        description: 'Mixed lot of electronic components including resistors, capacitors, and LED lights. Perfect for manufacturers and electronics workshops.',\n        originalPrice: 5000,\n        clearancePrice: 2000,\n        minOrder: 1000,\n        availableQuantity: 50000,\n        image: '/images/placeholder-light.svg',\n        category: 'Electronics',\n        condition: 'new',\n        location: 'Central Warehouse'\n    },\n    {\n        id: '2',\n        name: 'Industrial Packaging Materials',\n        description: 'Bulk lot of industrial-grade cardboard boxes, bubble wrap, and packing tape. Ideal for shipping and logistics companies.',\n        originalPrice: 3000,\n        clearancePrice: 1200,\n        minOrder: 500,\n        availableQuantity: 10000,\n        image: 'https://images.pexels.com/photos/4484078/pexels-photo-4484078.jpeg',\n        category: 'Packaging',\n        condition: 'new',\n        location: 'South Distribution Center'\n    },\n    {\n        id: '3',\n        name: 'Office Furniture Set',\n        description: 'Bulk lot of modern office furniture including desks, chairs, and filing cabinets. Perfect for office renovations or new setups.',\n        originalPrice: 15000,\n        clearancePrice: 6000,\n        minOrder: 50,\n        availableQuantity: 200,\n        image: 'https://images.pexels.com/photos/1957478/pexels-photo-1957478.jpeg',\n        category: 'Furniture',\n        condition: 'like-new',\n        location: 'East Warehouse'\n    },\n    {\n        id: '4',\n        name: 'Industrial Safety Equipment',\n        description: 'Bulk lot of safety gear including helmets, vests, and goggles. Essential for construction and industrial operations.',\n        originalPrice: 8000,\n        clearancePrice: 3500,\n        minOrder: 100,\n        availableQuantity: 1000,\n        image: 'https://images.pexels.com/photos/1474993/pexels-photo-1474993.jpeg',\n        category: 'Safety',\n        condition: 'new',\n        location: 'West Distribution Center'\n    },\n    {\n        id: '5',\n        name: 'Networking Equipment',\n        description: 'Bulk lot of networking equipment including routers, switches, and cables. Suitable for IT infrastructure upgrades.',\n        originalPrice: 12000,\n        clearancePrice: 4800,\n        minOrder: 50,\n        availableQuantity: 500,\n        image: 'https://images.pexels.com/photos/159304/network-cable-ethernet-computer-159304.jpeg',\n        category: 'IT Equipment',\n        condition: 'new',\n        location: 'North Warehouse'\n    },\n    {\n        id: '6',\n        name: 'Manufacturing Tools',\n        description: 'Bulk lot of industrial tools including power tools, hand tools, and measuring instruments. Essential for manufacturing facilities.',\n        originalPrice: 10000,\n        clearancePrice: 4000,\n        minOrder: 100,\n        availableQuantity: 1000,\n        image: 'https://images.pexels.com/photos/162553/keys-workshop-mechanic-tools-162553.jpeg',\n        category: 'Tools',\n        condition: 'new',\n        location: 'Central Warehouse'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/clearanceItems.ts\n"));

/***/ })

});