/* استيراد ملف الخطوط */
@import './fonts.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* أرجواني/وردي - اللون الأساسي من الشعار */
    --primary-50: 250 245 255;
    --primary-100: 243 232 255;
    --primary-200: 233 213 255;
    --primary-300: 216 180 254;
    --primary-400: 192 132 252;
    --primary-500: 168 85 247;
    --primary-600: 147 51 234;
    --primary-700: 126 34 206;
    --primary-800: 107 33 168;
    --primary-900: 88 28 135;
    --primary-950: 59 7 100;

    /* أزرق/سماوي - اللون الثانوي من الشعار */
    --secondary-50: 236 254 255;
    --secondary-100: 207 250 254;
    --secondary-200: 165 243 252;
    --secondary-300: 103 232 249;
    --secondary-400: 34 211 238;
    --secondary-500: 6 182 212;
    --secondary-600: 8 145 178;
    --secondary-700: 14 116 144;
    --secondary-800: 21 94 117;
    --secondary-900: 22 78 99;
    --secondary-950: 8 51 68;

    /* برتقالي/أصفر - اللون الثالث من الشعار */
    --accent-50: 255 247 237;
    --accent-100: 255 237 213;
    --accent-200: 254 215 170;
    --accent-300: 253 186 116;
    --accent-400: 251 146 60;
    --accent-500: 249 115 22;
    --accent-600: 234 88 12;
    --accent-700: 194 65 12;
    --accent-800: 154 52 18;
    --accent-900: 124 45 18;
    --accent-950: 67 20 7;

    --success-50: 240 253 244;
    --success-100: 220 252 231;
    --success-500: 34 197 94;
    --success-600: 22 163 74;
    --success-700: 21 128 61;

    --error-50: 254 242 242;
    --error-100: 254 226 226;
    --error-500: 239 68 68;
    --error-600: 220 38 38;
    --error-700: 185 28 28;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-white text-slate-900 font-sans antialiased;
  }

  .dark body {
    @apply bg-slate-950 text-slate-100;
  }

  /* تحسينات للأجهزة المحمولة */
  @media (max-width: 767px) {
    html {
      font-size: 15px;
    }

    .dark html {
      font-size: 14px;
    }

    body {
      @apply touch-manipulation;
    }
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }

  h5 {
    @apply text-lg md:text-xl;
  }

  h6 {
    @apply text-base md:text-lg;
  }

  a {
    @apply text-primary-500 hover:text-primary-600 transition-colors duration-200;
  }

  .dark a {
    @apply text-primary-400 hover:text-primary-300;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 relative overflow-hidden;
  }

  .btn-primary {
    @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
  }

  .dark .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 focus:ring-primary-400;
  }

  .btn-secondary {
    @apply btn bg-slate-100 text-slate-900 hover:bg-slate-200 focus:ring-slate-500;
  }

  .dark .btn-secondary {
    @apply bg-slate-700 text-slate-100 hover:bg-slate-600 focus:ring-slate-400;
  }

  .btn-accent {
    @apply btn bg-accent-500 text-white hover:bg-accent-600 focus:ring-accent-500;
  }

  .dark .btn-accent {
    @apply bg-accent-600 hover:bg-accent-700 focus:ring-accent-400;
  }

  /* تأثير التحويم للأزرار - تم إصلاح مشكلة اختفاء النص */
  .btn-primary::after,
  .btn-accent::after {
    content: "";
    @apply absolute inset-0 bg-white opacity-0 hover:opacity-10 transition-opacity duration-300 -z-10;
  }

  .dark .btn-primary::after,
  .dark .btn-accent::after {
    @apply bg-white opacity-0 hover:opacity-20 -z-10;
  }

  .container-custom {
    @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-[90rem]; /* زيادة العرض من 80rem (1280px) إلى 90rem (1440px) */
  }

  .section {
    @apply py-12 md:py-16 lg:py-24;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden transition-all duration-300 hover:shadow-md;
  }

  .dark .card {
    @apply bg-slate-900 border-slate-800 shadow-lg hover:shadow-xl;
  }
}

@keyframes scroll {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(100%);
  }
}

.animate-scroll {
  animation: scroll 2s infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
}

/* تحريكات للنوافذ المنبثقة والنماذج */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out forwards;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slideInLeft {
  animation: slideInLeft 0.3s ease-out forwards;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

[dir="rtl"] .ml-2,
[dir="rtl"] .ml-3,
[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 0.5rem;
  margin-right: 0.75rem;
  margin-right: 1rem;
}

[dir="rtl"] .mr-2,
[dir="rtl"] .mr-3,
[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 0.5rem;
  margin-left: 0.75rem;
  margin-left: 1rem;
}

/* تحسينات إضافية للأجهزة المحمولة */
@media (max-width: 767px) {
  .mobile-stack {
    @apply flex-col;
  }

  .mobile-full-width {
    @apply w-full;
  }

  .mobile-center {
    @apply text-center;
  }

  .mobile-hidden {
    @apply hidden;
  }

  .mobile-visible {
    @apply block;
  }

  .mobile-touch-scroll {
    @apply overflow-auto;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-no-scroll {
    @apply overflow-hidden;
  }

  .mobile-spacing {
    @apply mb-6;
  }

  /* تحسين حجم الخط للقراءة على الأجهزة المحمولة */
  .mobile-text-sm {
    @apply text-sm;
  }

  .mobile-text-base {
    @apply text-base;
  }

  .mobile-text-lg {
    @apply text-lg;
  }
}

/* تأثيرات الحركة */
.hover-glow {
  @apply transition-all duration-300;
}

.hover-glow:hover {
  @apply shadow-lg;
  box-shadow: 0 0 15px rgba(var(--primary-500), 0.5);
}

.dark .hover-glow:hover {
  box-shadow: 0 0 15px rgba(var(--primary-400), 0.5);
}

/* تأثير التحويم للصور */
.img-hover-zoom {
  @apply overflow-hidden;
}

.img-hover-zoom img {
  @apply transition-transform duration-500;
}

.img-hover-zoom:hover img {
  @apply scale-110;
}

/* أنماط التباين العالي لتحسين إمكانية الوصول */
.high-contrast {
  --primary-500: 249 115 22;
  --primary-600: 234 88 12;
  --accent-500: 168 85 247;
  --accent-600: 147 51 234;
  --secondary-500: 6 182 212;
  --secondary-600: 8 145 178;

  /* زيادة تباين النصوص */
  --slate-500: 30 41 59;
  --slate-600: 15 23 42;
  --slate-700: 15 23 42;
  --slate-800: 15 23 42;
  --slate-900: 2 6 23;
}

.high-contrast.dark {
  --primary-500: 251 146 60;
  --primary-600: 249 115 22;
  --accent-500: 192 132 252;
  --accent-600: 168 85 247;
  --secondary-500: 34 211 238;
  --secondary-600: 6 182 212;

  /* زيادة تباين النصوص في الوضع المظلم */
  --slate-100: 255 255 255;
  --slate-200: 241 245 249;
  --slate-300: 226 232 240;
  --slate-400: 226 232 240;
  --slate-500: 226 232 240;
}

/* تحسينات إمكانية الوصول للروابط */
a:focus-visible {
  @apply outline-2 outline-offset-2 outline-primary-500;
}

/* تحسين التركيز على العناصر التفاعلية */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  @apply outline-2 outline-offset-2 outline-primary-500 ring-2 ring-primary-500 ring-offset-2;
}