/**
 * تعريف هيكل قاعدة بيانات SQLite
 * يحتوي هذا الملف على تعريفات الجداول والعلاقات بينها
 */

// تعريف جدول المستخدمين
export const USERS_TABLE = `
CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  first_name TEXT,
  last_name TEXT,
  role TEXT NOT NULL DEFAULT 'user',
  created_at TEXT NOT NULL,
  updated_at TEXT,
  avatar TEXT,
  phone TEXT,
  street TEXT,
  city TEXT,
  state TEXT,
  postal_code TEXT,
  country TEXT,
  language TEXT DEFAULT 'ar',
  theme TEXT DEFAULT 'light',
  notifications INTEGER DEFAULT 1,
  newsletter INTEGER DEFAULT 0
)
`;

// تعريف جدول المنتجات
export const PRODUCTS_TABLE = `
CREATE TABLE IF NOT EXISTS products (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  name_ar TEXT,
  slug TEXT UNIQUE NOT NULL,
  description TEXT,
  description_ar TEXT,
  price REAL NOT NULL,
  sale_price REAL,
  category TEXT,
  tags TEXT,
  images TEXT,
  featured INTEGER DEFAULT 0,
  in_stock INTEGER DEFAULT 1,
  rating REAL DEFAULT 0,
  reviews INTEGER DEFAULT 0,
  specifications TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT
)
`;

// تعريف جدول الخدمات
export const SERVICES_TABLE = `
CREATE TABLE IF NOT EXISTS services (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  name_ar TEXT,
  slug TEXT UNIQUE NOT NULL,
  description TEXT,
  description_ar TEXT,
  icon TEXT,
  image TEXT,
  features TEXT,
  features_ar TEXT,
  price REAL,
  price_unit TEXT,
  category TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT
)
`;

// تعريف جدول خطوط الإنتاج
export const PRODUCTION_LINES_TABLE = `
CREATE TABLE IF NOT EXISTS production_lines (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  name_ar TEXT,
  slug TEXT UNIQUE NOT NULL,
  description TEXT,
  description_ar TEXT,
  capacity TEXT,
  specifications TEXT,
  images TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT
)
`;

// تعريف جدول منشورات المدونة
export const BLOG_POSTS_TABLE = `
CREATE TABLE IF NOT EXISTS blog_posts (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  title_ar TEXT,
  slug TEXT UNIQUE NOT NULL,
  excerpt TEXT,
  excerpt_ar TEXT,
  content TEXT,
  content_ar TEXT,
  author TEXT,
  author_title TEXT,
  author_image TEXT,
  cover_image TEXT,
  category TEXT,
  tags TEXT,
  published_at TEXT NOT NULL,
  updated_at TEXT,
  read_time TEXT
)
`;

// تعريف جدول المراجعات
export const REVIEWS_TABLE = `
CREATE TABLE IF NOT EXISTS reviews (
  id TEXT PRIMARY KEY,
  product_id TEXT NOT NULL,
  user_id TEXT NOT NULL,
  user_name TEXT NOT NULL,
  user_avatar TEXT,
  rating INTEGER NOT NULL,
  title TEXT,
  comment TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT,
  helpful INTEGER DEFAULT 0,
  verified INTEGER DEFAULT 0,
  images TEXT,
  FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
)
`;

// تعريف جدول الطلبات
export const ORDERS_TABLE = `
CREATE TABLE IF NOT EXISTS orders (
  id TEXT PRIMARY KEY,
  user_id TEXT,
  status TEXT NOT NULL,
  total REAL NOT NULL,
  shipping_fee REAL DEFAULT 0,
  tax REAL DEFAULT 0,
  discount REAL DEFAULT 0,
  payment_method TEXT,
  payment_status TEXT,
  shipping_address TEXT,
  billing_address TEXT,
  notes TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
)
`;

// تعريف جدول عناصر الطلبات
export const ORDER_ITEMS_TABLE = `
CREATE TABLE IF NOT EXISTS order_items (
  id TEXT PRIMARY KEY,
  order_id TEXT NOT NULL,
  product_id TEXT NOT NULL,
  quantity INTEGER NOT NULL,
  price REAL NOT NULL,
  total REAL NOT NULL,
  created_at TEXT NOT NULL,
  FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
)
`;

// تعريف جدول طلبات الجملة
export const WHOLESALE_QUOTES_TABLE = `
CREATE TABLE IF NOT EXISTS wholesale_quotes (
  id TEXT PRIMARY KEY,
  user_id TEXT,
  company_name TEXT,
  contact_name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT,
  product_interest TEXT,
  quantity INTEGER,
  message TEXT,
  status TEXT DEFAULT 'pending',
  created_at TEXT NOT NULL,
  updated_at TEXT,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
)
`;

// تعريف جدول طرق الدفع
export const PAYMENT_METHODS_TABLE = `
CREATE TABLE IF NOT EXISTS payment_methods (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  name_ar TEXT,
  type TEXT NOT NULL,
  icon TEXT,
  supported_currencies TEXT,
  processing_fee REAL DEFAULT 0,
  processing_fee_type TEXT DEFAULT 'percentage',
  enabled INTEGER DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT
)
`;

// تعريف جدول العملات
export const CURRENCIES_TABLE = `
CREATE TABLE IF NOT EXISTS currencies (
  code TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  name_ar TEXT,
  symbol TEXT NOT NULL,
  rate REAL NOT NULL,
  is_default INTEGER DEFAULT 0,
  created_at TEXT NOT NULL,
  updated_at TEXT
)
`;

// تعريف جدول طرق الشحن
export const SHIPPING_METHODS_TABLE = `
CREATE TABLE IF NOT EXISTS shipping_methods (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  name_ar TEXT,
  description TEXT,
  description_ar TEXT,
  price REAL NOT NULL,
  estimated_delivery TEXT,
  countries TEXT,
  icon TEXT,
  enabled INTEGER DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT
)
`;

// تعريف جدول قائمة المفضلة
export const WISHLIST_TABLE = `
CREATE TABLE IF NOT EXISTS wishlist (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  product_id TEXT NOT NULL,
  created_at TEXT NOT NULL,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
)
`;

// تعريف جدول سلة التسوق
export const CART_TABLE = `
CREATE TABLE IF NOT EXISTS cart (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  product_id TEXT NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT,
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
)
`;

// تعريف جدول الإعدادات
export const SETTINGS_TABLE = `
CREATE TABLE IF NOT EXISTS settings (
  key TEXT PRIMARY KEY,
  value TEXT,
  updated_at TEXT
)
`;

// تجميع كل تعريفات الجداول
export const ALL_TABLES = [
  USERS_TABLE,
  PRODUCTS_TABLE,
  SERVICES_TABLE,
  PRODUCTION_LINES_TABLE,
  BLOG_POSTS_TABLE,
  REVIEWS_TABLE,
  ORDERS_TABLE,
  ORDER_ITEMS_TABLE,
  WHOLESALE_QUOTES_TABLE,
  PAYMENT_METHODS_TABLE,
  CURRENCIES_TABLE,
  SHIPPING_METHODS_TABLE,
  WISHLIST_TABLE,
  CART_TABLE,
  SETTINGS_TABLE
];

// وظيفة لإنشاء جميع الجداول
export function createAllTables(db: any) {
  ALL_TABLES.forEach(tableSchema => {
    db.exec(tableSchema);
  });
}
