'use client';

import { useState } from 'react';
import { ArrowRight, Package, Truck, BarChart, Shield, CheckCircle } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { ServiceBookingForm } from '../../components/forms/ServiceBookingForm';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';

export default function StoragePage() {
  const [showBookingForm, setShowBookingForm] = useState(false);
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-800 to-slate-900 text-white py-20">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.1}>
            <div className="max-w-3xl mx-auto text-center">
              <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-primary-500/20 text-primary-300 mb-6">
                <Package size={36} />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'حلول التخزين' : 'Storage Solutions'}
              </h1>
              <p className="text-xl mb-8 text-slate-300 max-w-2xl mx-auto">
                {currentLanguage === 'ar'
                  ? 'تخزين آمن ومتحكم في المناخ مع أنظمة متقدمة لإدارة المخزون.'
                  : 'Secure, climate-controlled warehousing with advanced inventory management systems.'}
              </p>
              <HoverAnimation animation="scale">
                <Button
                  size="lg"
                  variant="accent"
                  className="px-8"
                  onClick={() => setShowBookingForm(true)}
                >
                  {currentLanguage === 'ar' ? 'حجز تخزين' : 'Book Storage'}
                  <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5`} />
                </Button>
              </HoverAnimation>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Features Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'ميزات التخزين المتقدمة' : 'Advanced Storage Features'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'مرافق وأنظمة إدارة متطورة'
                  : 'State-of-the-art facilities and management systems'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {[
              {
                icon: <Shield className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "التحكم في المناخ" : "Climate Control",
                description: currentLanguage === 'ar'
                  ? "بيئات متحكم في درجة الحرارة والرطوبة"
                  : "Temperature and humidity-controlled environments",
              },
              {
                icon: <BarChart className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "تتبع المخزون" : "Inventory Tracking",
                description: currentLanguage === 'ar'
                  ? "نظام إدارة المخزون في الوقت الفعلي"
                  : "Real-time inventory management system",
              },
              {
                icon: <Truck className="h-10 w-10 text-primary-500" />,
                title: currentLanguage === 'ar' ? "تكامل الخدمات اللوجستية" : "Logistics Integration",
                description: currentLanguage === 'ar'
                  ? "اتصال سلس مع خدمات الشحن"
                  : "Seamless connection with shipping services",
              },
            ].map((feature, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-6 h-full">
                  <div className="flex flex-col items-center mb-4 text-center">
                    <div className={cn(
                      "mb-4 p-3 rounded-full",
                      isDarkMode ? "bg-primary-900/20" : "bg-primary-50"
                    )}>
                      {feature.icon}
                    </div>
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                      {feature.title}
                    </h3>
                  </div>
                  <p className="text-slate-600 dark:text-slate-300 text-center">
                    {feature.description}
                  </p>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Facilities Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'مرافق التخزين لدينا' : 'Our Storage Facilities'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'مستودعات حديثة مجهزة بأنظمة أمان وإدارة متطورة'
                  : 'Modern warehouses equipped with advanced security and management systems'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {[
              {
                title: currentLanguage === 'ar' ? "التخزين العام" : "General Storage",
                specs: currentLanguage === 'ar'
                  ? [
                      "التحكم في درجة الحرارة",
                      "أمان على مدار الساعة",
                      "الحماية من الحرائق",
                      "مكافحة الآفات",
                    ]
                  : [
                      "Temperature controlled",
                      "24/7 security",
                      "Fire protection",
                      "Pest control",
                    ],
              },
              {
                title: currentLanguage === 'ar' ? "التخزين المتخصص" : "Specialized Storage",
                specs: currentLanguage === 'ar'
                  ? [
                      "معتمد للمواد الخطرة",
                      "التخزين البارد",
                      "البضائع عالية القيمة",
                      "المواد الحساسة",
                    ]
                  : [
                      "Hazmat certified",
                      "Cold storage",
                      "High-value goods",
                      "Sensitive materials",
                    ],
              },
              {
                title: currentLanguage === 'ar' ? "مركز التوزيع" : "Distribution Center",
                specs: currentLanguage === 'ar'
                  ? [
                      "التفريغ المتقاطع",
                      "تنفيذ الطلبات",
                      "التسليم للميل الأخير",
                      "معالجة المرتجعات",
                    ]
                  : [
                      "Cross-docking",
                      "Order fulfillment",
                      "Last-mile delivery",
                      "Returns processing",
                    ],
              },
            ].map((facility, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-6 h-full">
                  <h3 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white text-center">
                    {facility.title}
                  </h3>
                  <ul className="space-y-3">
                    {facility.specs.map((spec, i) => (
                      <li key={i} className="flex items-center">
                        <CheckCircle className={`h-5 w-5 text-primary-500 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0`} />
                        <span className="text-slate-700 dark:text-slate-300">{spec}</span>
                      </li>
                    ))}
                  </ul>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Booking Form Modal */}
      {showBookingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <ScrollAnimation animation="scale" duration={0.3}>
            <div className="max-w-2xl w-full">
              <ServiceBookingForm
                serviceName={currentLanguage === 'ar' ? 'خدمات التخزين' : 'Storage Services'}
                onClose={() => setShowBookingForm(false)}
              />
            </div>
          </ScrollAnimation>
        </div>
      )}
    </div>
  );
}