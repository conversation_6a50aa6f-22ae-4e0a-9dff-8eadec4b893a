'use client';

import { Suspense, lazy } from 'react';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { ArrowRight, ShoppingCart, Users, Package, Truck, FileText, Tag, BarChart as ChartBar, Globe, Clock, Shield, CheckCircle, ArrowUpRight, Heart, Star, Eye, Settings, Gauge, PenTool as Tool, Calendar, FileCheck, Search, Building2 } from 'lucide-react';
import { Button } from '../components/ui/Button';
import { HeroButton } from '../components/ui/HeroButton';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { LazyImage } from '../components/ui/LazyImage';
import { EnhancedImage } from '../components/ui/EnhancedImage';
import { FadeIn, ScaleIn, StaggerChildren, ScrollAnimation, ScrollStagger, HoverAnimation } from '../components/ui/animations';
import { useCartStore } from '../stores/cartStore';
import { useWishlistStore } from '../stores/wishlistStore';
import { useAuthStore } from '../stores/authStore';
import { useLanguageStore } from '../stores/languageStore';
import { useTranslation } from '../translations';
import { useAuthModalStore } from '../stores/authModalStore';
import { WholesaleQuoteForm } from '../components/forms/WholesaleQuoteForm';
import { QuickView } from '../components/shop/QuickView';
import { ExitIntentPopup } from '../components/marketing/ExitIntentPopup';
import { useABTesting } from '../components/marketing/ABTestingProvider';
import { products } from '../data/products';
import { clearanceItems } from '../data/clearanceItems';
import { productionLines } from '../data/productionLines';
import { services } from '../data/services';
import { blogPosts } from '../data/blogPosts';
import { formatCurrency } from '../lib/utils';

// صور القسم الرئيسي حسب اللغة - تم تحديثها لتكون أكثر تعبيراً عن المحتوى النصي
const heroImagesData = {
  en: [
    {
      url: 'https://images.unsplash.com/photo-1661956602116-aa6865609028?ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1664&q=80',
      alt: 'Digital commerce platform with product showcase',
      title: 'Digital Commerce Solutions',
      subtitle: 'Cutting-edge technology for seamless online retail experiences'
    },
    {
      url: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      alt: 'Smart logistics and warehouse management',
      title: 'Smart Logistics',
      subtitle: 'Integrated supply chain and warehouse management solutions'
    },
    {
      url: 'https://images.unsplash.com/photo-1494412574643-ff11b0a5c1c3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      alt: 'Global shipping and distribution network',
      title: 'Global Distribution',
      subtitle: 'Worldwide shipping and distribution network with real-time tracking'
    }
  ],
  ar: [
    {
      url: 'https://images.unsplash.com/photo-1661956602116-aa6865609028?ixlib=rb-4.0.3&ixid=M3wxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1664&q=80',
      alt: 'منصة تجارة رقمية مع عرض المنتجات',
      title: 'حلول التجارة الرقمية',
      subtitle: 'تكنولوجيا متطورة لتجارب تسوق إلكترونية سلسة'
    },
    {
      url: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      alt: 'لوجستيات ذكية وإدارة المستودعات',
      title: 'لوجستيات ذكية',
      subtitle: 'حلول متكاملة لسلسلة التوريد وإدارة المستودعات'
    },
    {
      url: 'https://images.unsplash.com/photo-1494412574643-ff11b0a5c1c3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      alt: 'شبكة شحن وتوزيع عالمية',
      title: 'توزيع عالمي',
      subtitle: 'شبكة شحن وتوزيع عالمية مع تتبع في الوقت الحقيقي'
    }
  ]
};

// حلول الأعمال حسب اللغة
const solutionsData = {
  en: [
    {
      icon: <ShoppingCart size={24} />,
      title: "Retail (B2C)",
      description: "Shop our extensive catalog of products with competitive pricing and fast delivery options.",
      features: [
        "Premium product selection",
        "Fast worldwide shipping",
        "Secure payment processing",
        "Customer satisfaction guarantee"
      ],
      image: "https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg"
    },
    {
      icon: <Users size={24} />,
      title: "Wholesale (B2B)",
      description: "Request quotes for bulk orders with special pricing available for business customers.",
      features: [
        "Volume discounts",
        "Dedicated account manager",
        "Flexible payment terms",
        "Custom packaging options"
      ],
      image: "https://images.pexels.com/photos/4483610/pexels-photo-4483610.jpeg"
    },
    {
      icon: <Package size={24} />,
      title: "Production Lines",
      description: "Browse our selection of turnkey manufacturing solutions with detailed specifications.",
      features: [
        "Automated systems",
        "Quality control integration",
        "Technical support",
        "Installation services"
      ],
      image: "https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg"
    },
    {
      icon: <Truck size={24} />,
      title: "Business Services",
      description: "Inspection, storage, shipping, certification, and consulting services to support your operations.",
      features: [
        "Professional consulting",
        "Quality inspections",
        "Logistics solutions",
        "Certification support"
      ],
      image: "https://images.pexels.com/photos/4483608/pexels-photo-4483608.jpeg"
    },
    {
      icon: <Tag size={24} />,
      title: "Clearance Sales",
      description: "Take advantage of special deals on bulk liquidation items and overstock products.",
      features: [
        "Exclusive discounts",
        "Regular new arrivals",
        "Bulk opportunities",
        "Quick shipping"
      ],
      image: "https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg"
    },
    {
      icon: <FileText size={24} />,
      title: "Industry Insights",
      description: "Stay informed with our blog featuring the latest trends, tips, and best practices.",
      features: [
        "Expert analysis",
        "Market trends",
        "Success stories",
        "Industry updates"
      ],
      image: "https://images.pexels.com/photos/4483602/pexels-photo-4483602.jpeg"
    }
  ],
  ar: [
    {
      icon: <ShoppingCart size={24} />,
      title: "البيع بالتجزئة (B2C)",
      description: "تسوق من كتالوج منتجاتنا الواسع بأسعار تنافسية وخيارات توصيل سريعة.",
      features: [
        "اختيار منتجات متميزة",
        "شحن عالمي سريع",
        "معالجة آمنة للمدفوعات",
        "ضمان رضا العملاء"
      ],
      image: "https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg"
    },
    {
      icon: <Users size={24} />,
      title: "البيع بالجملة (B2B)",
      description: "اطلب عروض أسعار للطلبات الكبيرة مع أسعار خاصة متاحة لعملاء الأعمال.",
      features: [
        "خصومات على الكميات",
        "مدير حساب مخصص",
        "شروط دفع مرنة",
        "خيارات تغليف مخصصة"
      ],
      image: "https://images.pexels.com/photos/4483610/pexels-photo-4483610.jpeg"
    },
    {
      icon: <Package size={24} />,
      title: "خطوط الإنتاج",
      description: "تصفح مجموعتنا من حلول التصنيع الجاهزة مع مواصفات مفصلة.",
      features: [
        "أنظمة آلية",
        "تكامل مراقبة الجودة",
        "دعم فني",
        "خدمات التركيب"
      ],
      image: "https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg"
    },
    {
      icon: <Truck size={24} />,
      title: "خدمات الأعمال",
      description: "خدمات الفحص والتخزين والشحن والشهادات والاستشارات لدعم عملياتك.",
      features: [
        "استشارات احترافية",
        "فحوصات الجودة",
        "حلول لوجستية",
        "دعم الشهادات"
      ],
      image: "https://images.pexels.com/photos/4483608/pexels-photo-4483608.jpeg"
    },
    {
      icon: <Tag size={24} />,
      title: "تصفية المبيعات",
      description: "استفد من العروض الخاصة على عناصر التصفية بالجملة والمنتجات الفائضة.",
      features: [
        "خصومات حصرية",
        "وصول منتجات جديدة بانتظام",
        "فرص للشراء بالجملة",
        "شحن سريع"
      ],
      image: "https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg"
    },
    {
      icon: <FileText size={24} />,
      title: "رؤى الصناعة",
      description: "ابق على اطلاع من خلال مدونتنا التي تعرض أحدث الاتجاهات والنصائح وأفضل الممارسات.",
      features: [
        "تحليل الخبراء",
        "اتجاهات السوق",
        "قصص النجاح",
        "تحديثات الصناعة"
      ],
      image: "https://images.pexels.com/photos/4483602/pexels-photo-4483602.jpeg"
    }
  ]
};

function HomePage() {
  const { locale } = useTranslation();
  const { language } = useLanguageStore();
  const { t } = useTranslation();

  // استخدام البيانات المناسبة حسب اللغة المحددة
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const heroImages = heroImagesData[currentLanguage];
  const solutions = solutionsData[currentLanguage];

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [hoveredSolution, setHoveredSolution] = useState<number | null>(null);
  const [showWholesaleForm, setShowWholesaleForm] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);

  // تحسين مدة الانتقال بين الصور وإضافة تأثيرات انتقالية أكثر سلاسة
  useEffect(() => {
    // زيادة مدة عرض كل صورة إلى 7 ثوانٍ لإعطاء المستخدم وقتًا كافيًا لقراءة المحتوى
    const interval = setInterval(() => {
      setIsTransitioning(true);

      // زيادة مدة التأثير الانتقالي إلى 800 مللي ثانية لتأثير بصري أكثر سلاسة
      setTimeout(() => {
        setCurrentImageIndex((prev) => (prev + 1) % heroImages.length);

        // تأخير إعادة تعيين حالة الانتقال لإعطاء وقت للمحتوى للظهور بشكل تدريجي
        setTimeout(() => {
          setIsTransitioning(false);
        }, 200);
      }, 800);
    }, 7000);

    return () => clearInterval(interval);
  }, [heroImages.length]);

  // استخدام A/B Testing للصور البارزة
  const { getVariant, trackConversion } = useABTesting();
  const heroVariant = getVariant('heroImage');

  // تتبع التحويل عند النقر على زر "استكشف المنتجات"
  const handleExploreClick = () => {
    trackConversion('heroImage');
  };

  return (
    <div>
      {/* نافذة منبثقة عند محاولة المغادرة */}
      <ExitIntentPopup delay={10000} />

      {/* Hero Section - تم تحسينه ليعكس توجه المنصة بشكل أفضل مع ضبط الارتفاع */}
      <section className="relative h-[600px] md:h-[650px] lg:h-[700px] flex items-center justify-center overflow-hidden">
        {/* Background Images */}
        {heroImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-all duration-1500 ${
              index === currentImageIndex
                ? 'opacity-100 scale-100'
                : 'opacity-0 scale-110'
            }`}
          >
            {/* تحسين التدرجات اللونية لتناسب ألوان المنصة */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary-900/80 via-black/60 to-secondary-900/70 z-10 mix-blend-multiply" />

            {/* إضافة تأثير شبكة للخلفية - تم إزالته مؤقتًا لحل مشكلة التحميل */}
            <div className="absolute inset-0 bg-gradient-to-r from-black/10 to-black/10 opacity-20 z-10 mix-blend-overlay" />

            {/* تحسين نسبة العرض والارتفاع للصورة مع استخدام EnhancedImage */}
            <EnhancedImage
              src={image.url}
              alt={image.alt}
              fill={true}
              objectFit="cover"
              priority={index === 0}
              loading={index === 0 ? "eager" : "lazy"}
              effect="fade"
              progressive={true}
              placeholder="shimmer"
              className="object-center"
              containerClassName="w-full h-full"
              sizes="100vw"
              style={{ objectPosition: '50% 30%' }} // تركيز على الجزء العلوي من الصورة
            />
          </div>
        ))}

        {/* Content */}
        <div className="container-custom relative z-20">
          <div className="max-w-5xl mx-auto text-center text-white">
            <div className="mb-8 space-y-4">
              {/* تحسين حجم وتأثيرات العنوان */}
              <h1
                className={`text-4xl md:text-5xl lg:text-6xl font-bold mb-4 transition-all duration-700 transform ${
                  isTransitioning ? 'opacity-0 translate-y-4' : 'opacity-100 translate-y-0'
                }`}
              >
                {heroImages[currentImageIndex].title}
              </h1>

              {/* تحسين حجم وتأثيرات النص الفرعي */}
              <p
                className={`text-lg md:text-xl text-white/95 mb-6 max-w-2xl mx-auto transition-all duration-700 delay-100 ${
                  isTransitioning ? 'opacity-0 translate-y-4' : 'opacity-100 translate-y-0'
                }`}
              >
                {heroImages[currentImageIndex].subtitle}
              </p>
            </div>

            {/* تحسين أزرار الدعوة للعمل */}
            <div className={`flex flex-col sm:flex-row justify-center gap-4 mb-10 transition-all duration-700 delay-200 ${
              isTransitioning ? 'opacity-0 translate-y-4' : 'opacity-100 translate-y-0'
            }`}>
              <HeroButton
                variant={heroVariant === 'A' ? "accent" : "primary"}
                className={`flex items-center py-3 px-6 relative overflow-hidden shadow-lg hover:shadow-xl ${
                  heroVariant === 'B' ? 'animate-pulse' : ''
                }`}
                as={Link}
                href={`/${currentLanguage}/shop`}
                onClick={handleExploreClick}
              >
                <span className="relative z-10 font-medium">{t('hero.cta.explore')}</span>
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5 relative z-10 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''} transition-transform group-hover:translate-x-1`} />
              </HeroButton>
              <HeroButton
                variant="outline"
                className="py-3 px-6 relative overflow-hidden border-2"
                as={Link}
                to={`/${currentLanguage}/services`}
              >
                <span className="relative z-10 font-medium">{t('hero.cta.services')}</span>
              </HeroButton>
            </div>

            {/* تحسين مؤشرات الشرائح */}
            <div className="flex justify-center gap-2">
              {heroImages.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`h-2 rounded-full transition-all duration-500 ${
                    index === currentImageIndex
                      ? 'bg-primary-400 w-8'
                      : 'bg-white/40 hover:bg-white/60 w-2'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* تحسين مؤشر التمرير */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20">
          <div className="animate-bounce">
            <div className="w-1 h-10 rounded-full bg-white/20 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1/2 bg-primary-400 rounded-full animate-scroll" />
            </div>
          </div>
        </div>
      </section>

      {/* Business Solutions Section */}
      <section className="py-20 md:py-28 relative overflow-hidden dark:bg-slate-900 bg-white">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white">
                {t('solutions.title')}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {t('solutions.subtitle')}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            delay={0.3}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {solutions.map((solution, index) => (
              <div
                key={index}
                className="group relative"
                onMouseEnter={() => setHoveredSolution(index)}
                onMouseLeave={() => setHoveredSolution(null)}
              >
                <HoverAnimation animation="lift" scale={1.02}>
                  <Card className="h-full relative z-10 shadow-sm hover:shadow-xl transition-shadow duration-300">
                    <div className="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-transparent dark:from-primary-500/10 rounded-lg" />
                    <div className="p-8 relative">
                      <div className="flex items-center justify-between mb-6">
                        <div className="w-12 h-12 bg-primary-500/10 dark:bg-primary-500/20 rounded-lg flex items-center justify-center text-primary-500 dark:text-primary-400">
                          {solution.icon}
                        </div>
                        <ArrowUpRight className={`w-5 h-5 text-primary-500 dark:text-primary-400 transform transition-all duration-300 ${hoveredSolution === index ? 'translate-x-1 -translate-y-1' : ''}`} />
                      </div>
                      <h3 className="text-xl font-semibold mb-3 text-slate-900 dark:text-white">
                        {solution.title}
                      </h3>
                      <p className="text-slate-600 dark:text-slate-300 mb-6">
                        {solution.description}
                      </p>
                      <ul className="space-y-3">
                        {solution.features.map((feature, i) => (
                          <li key={i} className="flex items-center text-slate-700 dark:text-slate-300">
                            <CheckCircle className="w-5 h-5 text-primary-500 dark:text-primary-400 mr-2 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </Card>
                </HoverAnimation>
              </div>
            ))}
          </div>
        </div>

        {/* Background Decorations */}
        <div className="absolute top-0 left-0 w-64 h-64 bg-primary-500/5 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-accent-500/5 rounded-full blur-3xl translate-x-1/2 translate-y-1/2" />
      </section>

      {/* Featured Products */}
      <section className="py-20 md:py-28 dark:bg-slate-900 bg-white">
        <div className="container-custom">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12">
            <ScrollAnimation animation="fade" delay={0.2}>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                  {t('products.title')}
                </h2>
                <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl">
                  {t('products.subtitle')}
                </p>
              </div>
            </ScrollAnimation>
            <ScrollAnimation animation="fade" delay={0.4}>
              <div className="flex items-center gap-4 mt-6 md:mt-0">
                <HoverAnimation animation="scale">
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Link href={`/${currentLanguage}/shop`}>
                      {t('products.viewAll')}
                      <ArrowRight className={`w-4 h-4 ${language === 'ar' ? 'rotate-rtl' : ''}`} />
                    </Link>
                  </Button>
                </HoverAnimation>
                <HoverAnimation animation="scale">
                  <Button
                    variant="primary"
                    className="flex items-center gap-2"
                  >
                    <Link href={`/${currentLanguage}/shop/new-arrivals`}>
                      {t('products.newArrivals')}
                      <ArrowRight className={`w-4 h-4 ${language === 'ar' ? 'rotate-rtl' : ''}`} />
                    </Link>
                  </Button>
                </HoverAnimation>
              </div>
            </ScrollAnimation>
          </div>

          <ScrollStagger
            animation="scale"
            staggerDelay={0.1}
            delay={0.3}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {products.filter(product => product.featured).slice(0, 4).map((product) => (
              <HoverAnimation key={product.id} animation="lift">
                <Card className="group relative overflow-hidden">
                  <div className="relative aspect-square overflow-hidden">
                    <EnhancedImage
                      src={product.images[0]}
                      alt={product.name}
                      fill={true}
                      objectFit="cover"
                      effect="zoom"
                      progressive={true}
                      placeholder="shimmer"
                      className="relative w-full h-full"
                      containerClassName="w-full h-full"
                      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                    />
                    <div className="absolute top-4 right-4 z-10 space-y-2">
                      <WishlistButton product={product} />
                      <QuickViewButton product={product} />
                    </div>
                    {product.compareAtPrice && (
                      <div className="absolute top-4 left-4 bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Save {Math.round((1 - product.price / product.compareAtPrice) * 100)}%
                      </div>
                    )}
                  </div>

                  <div className="p-6">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium px-2.5 py-0.5 rounded bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300">
                        {product.category}
                      </span>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="ml-1 text-sm text-slate-600 dark:text-slate-400">4.8</span>
                      </div>
                    </div>

                    <Link
                      href={`/shop/product/${product.slug}`}
                      className="block group-hover:text-primary-600 transition-colors"
                    >
                      <h3 className="text-lg font-semibold mb-2 line-clamp-2 dark:text-white">
                        {product.name}
                      </h3>
                    </Link>

                    <p className="text-slate-600 dark:text-slate-400 text-sm mb-4 line-clamp-2">
                      {product.description}
                    </p>

                    <div className="flex items-end justify-between mb-4">
                      <div>
                        <div className="text-2xl font-bold text-slate-900 dark:text-white">
                          {formatCurrency(product.price)}
                        </div>
                        {product.compareAtPrice && (
                          <div className="text-sm text-slate-500 dark:text-slate-500 line-through">
                            {formatCurrency(product.compareAtPrice)}
                          </div>
                        )}
                      </div>
                      <div className="text-sm text-slate-600 dark:text-slate-400">
                        {product.stock > 0 ? (
                          <span className="text-success-600 dark:text-success-500">{t('products.inStock')}</span>
                        ) : (
                          <span className="text-error-600 dark:text-error-500">{t('products.outOfStock')}</span>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <AddToCartButton product={product} />
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowWholesaleForm(true);
                        }}
                      >
                        Request Quote
                      </Button>
                    </div>
                  </div>
                </Card>
              </ScaleIn>
            ))}
          </StaggerChildren>

          <FadeIn className="mt-12 text-center" delay={0.8}>
            <Button
              variant="outline"
              size="lg"
              className="px-8"
              as={Link}
              to={`/${currentLanguage}/shop`}
            >
              {t('products.viewAll')}
              <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />
            </Button>
          </FadeIn>
        </div>
      </section>

      {/* Best Deals Section */}
      <section className="py-20 md:py-28 bg-slate-50 dark:bg-slate-800">
        <div className="container-custom">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12">
            <ScrollAnimation animation="fade" delay={0.2}>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                  {language === 'ar' ? 'أفضل العروض والصفقات' : 'Best Deals & Offers'}
                </h2>
                <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl">
                  {language === 'ar' ? 'استفد من عروضنا المحدودة والصفقات الحصرية على الحلول الصناعية المتميزة.' : 'Take advantage of our limited-time offers and exclusive deals on premium industrial solutions.'}
                </p>
              </div>
            </ScrollAnimation>
            <ScrollAnimation animation="fade" delay={0.4}>
              <div className="flex items-center gap-4 mt-6 md:mt-0">
                <HoverAnimation animation="scale">
                  <Button
                    variant="primary"
                    className="flex items-center gap-2"
                  >
                    <Link href={`/${currentLanguage}/clearance`}>
                      {currentLanguage === 'ar' ? 'عرض جميع العروض' : 'View All Deals'}
                      <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />
                    </Link>
                  </Button>
                </HoverAnimation>
              </div>
            </ScrollAnimation>
          </div>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            delay={0.3}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {clearanceItems.slice(0, 3).map((item) => (
              <HoverAnimation key={item.id} animation="lift">
                <Card className="group relative overflow-hidden">
                  <div className="relative aspect-video overflow-hidden">
                    <EnhancedImage
                      src={item.image}
                      alt={item.name}
                      fill={true}
                      objectFit="cover"
                      effect="zoom"
                      progressive={true}
                      placeholder="shimmer"
                      className="relative w-full h-full"
                      containerClassName="w-full h-full"
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                    <div className="absolute top-4 right-4">
                      <WishlistButton product={item} />
                    </div>
                    <div className="absolute top-4 left-4 bg-error-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      Save {Math.round((1 - item.clearancePrice / item.originalPrice) * 100)}%
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium px-2.5 py-0.5 rounded bg-accent-50 text-accent-700">
                        {item.category}
                      </span>
                      <span className={`text-sm font-medium px-2.5 py-0.5 rounded ${
                        item.condition === 'new' ? 'bg-success-50 text-success-700' :
                        item.condition === 'like-new' ? 'bg-info-50 text-info-700' :
                        'bg-warning-50 text-warning-700'
                      }`}>
                        {item.condition}
                      </span>
                    </div>

                    <Link
                      href={`/clearance/${item.id}`}
                      className="block group-hover:text-primary-600 transition-colors"
                    >
                      <h3 className="text-lg font-semibold mb-2 line-clamp-2">
                        {item.name}
                      </h3>
                    </Link>

                    <p className="text-slate-600 text-sm mb-4 line-clamp-2">
                      {item.description}
                    </p>

                    <div className="flex items-end justify-between mb-4">
                      <div>
                        <div className="text-2xl font-bold text-slate-900">
                          {formatCurrency(item.clearancePrice)}
                        </div>
                        <div className="text-sm text-slate-500 line-through">
                          {formatCurrency(item.originalPrice)}
                        </div>
                      </div>
                      <div className="text-sm">
                        <div className="text-slate-600">Min. Order:</div>
                        <div className="font-medium text-slate-900">{item.minOrder} units</div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Button
                        variant="primary"
                        className="w-full"
                        onClick={() => setShowWholesaleForm(true)}
                      >
                        Request Quote
                      </Button>
                      <Link
                        href={`/clearance/${item.id}`}
                        className="block text-center text-sm text-primary-600 hover:text-primary-700"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>

                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-white dark:from-slate-900 to-transparent h-16 pointer-events-none" />
                </Card>
              </ScaleIn>
            ))}
          </StaggerChildren>

          <FadeIn className="mt-12 text-center" delay={0.8}>
            <Button
              variant="outline"
              size="lg"
              className="px-8"
            >
              <Link href={`/${currentLanguage}/clearance`}>
                {currentLanguage === 'ar' ? 'عرض جميع العروض الخاصة' : 'View All Special Offers'}
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />
              </Link>
            </Button>
          </FadeIn>
        </div>
      </section>

      {/* Most Requested Services */}
      <section className="py-20 md:py-28 bg-slate-50 dark:bg-slate-800">
        <div className="container-custom">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12">
            <ScrollAnimation animation="fade" delay={0.2}>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                  {language === 'ar' ? 'الخدمات الأكثر طلباً' : 'Most Requested Services'}
                </h2>
                <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl">
                  {language === 'ar' ? 'اكتشف مجموعتنا الشاملة من خدمات الأعمال المصممة لتحسين عملياتك ودفع النمو.' : 'Discover our comprehensive range of business services designed to optimize your operations and drive growth.'}
                </p>
              </div>
            </ScrollAnimation>
            <ScrollAnimation animation="fade" delay={0.4}>
              <div className="flex items-center gap-4 mt-6 md:mt-0">
                <HoverAnimation animation="scale">
                  <Button
                    variant="primary"
                    className="flex items-center gap-2"
                    as={Link}
                    to={`/${currentLanguage}/services`}
                  >
                    {currentLanguage === 'ar' ? 'عرض جميع الخدمات' : 'View All Services'}
                    <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />
                  </Button>
                </HoverAnimation>
              </div>
            </ScrollAnimation>
          </div>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            delay={0.3}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {services.slice(0, 6).map((service) => {
              const Icon = service.icon === 'Search' ? Search :
                          service.icon === 'Package' ? Package :
                          service.icon === 'Truck' ? Truck :
                          service.icon === 'FileCheck' ? FileCheck :
                          service.icon === 'Users' ? Users :
                          Building2;

              return (
                <HoverAnimation key={service.id} animation="lift">
                  <Card className="group relative overflow-hidden hover:shadow-lg transition-all duration-300">
                    <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 to-accent-500 transform origin-left transition-transform duration-300 scale-x-0 group-hover:scale-x-100" />

                    <div className="p-6">
                      <div className="flex items-center justify-between mb-6">
                        <div className="w-12 h-12 bg-primary-50 dark:bg-primary-900/50 rounded-lg flex items-center justify-center text-primary-500 dark:text-primary-400 transition-all duration-300 group-hover:scale-110">
                          <Icon size={24} />
                        </div>
                        <ArrowUpRight className="w-5 h-5 text-primary-500 dark:text-primary-400 transform transition-all duration-300 group-hover:translate-x-1 group-hover:-translate-y-1" />
                      </div>

                      <h3 className="text-xl font-semibold mb-4 group-hover:text-primary-500 dark:text-white dark:group-hover:text-primary-400 transition-colors">
                        {currentLanguage === 'ar' ? service.name_ar || service.name : service.name}
                      </h3>

                      <p className="text-slate-600 dark:text-slate-300 mb-6 line-clamp-2">
                        {currentLanguage === 'ar' ? service.description_ar || service.description : service.description}
                      </p>

                      <div className="space-y-3 mb-6">
                        {(currentLanguage === 'ar' ? service.features_ar || service.features : service.features).slice(0, 4).map((feature, index) => (
                          <div key={index} className="flex items-center text-sm text-slate-700 dark:text-slate-300">
                            <CheckCircle className="w-4 h-4 text-primary-500 dark:text-primary-400 mr-2 flex-shrink-0" />
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>

                      <div className="space-y-2">
                        <HoverAnimation animation="scale">
                          <Button
                            className="w-full"
                            as={Link}
                            to={`/${currentLanguage}/services/${service.slug}`}
                          >
                            {currentLanguage === 'ar' ? 'احجز الآن' : 'Book Now'}
                          </Button>
                        </HoverAnimation>
                        <HoverAnimation animation="scale">
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={() => setShowWholesaleForm(true)}
                          >
                            {language === 'ar' ? 'طلب عرض سعر' : 'Request Quote'}
                          </Button>
                        </HoverAnimation>
                      </div>
                    </div>

                    <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-accent-500 to-primary-500 transform origin-right transition-transform duration-300 scale-x-0 group-hover:scale-x-100" />
                  </Card>
                </HoverAnimation>
              );
            })}
          </div>

          <div className="mt-12 text-center">
            <Button
              variant="outline"
              size="lg"
              className="px-8"
            >
              <Link href="/services">
                {language === 'ar' ? 'استكشاف جميع الخدمات' : 'Explore All Services'}
                <ArrowRight className={`${language === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5 ${language === 'ar' ? 'rotate-rtl' : ''}`} />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 md:py-28 bg-primary-500 text-white">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                {language === 'ar' ? 'هل أنت مستعد لتحويل أعمالك؟' : 'Ready to Transform Your Business?'}
              </h2>
              <p className="text-lg md:text-xl text-primary-100 mb-8">
                {language === 'ar' ? 'انضم إلى آلاف العملاء الراضين الذين رفعوا مستوى عملياتهم مع حلولنا الشاملة.' : 'Join thousands of satisfied customers who have elevated their operations with our comprehensive solutions.'}
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <HoverAnimation animation="scale">
                  <HeroButton
                    variant="accent"
                    as={Link}
                    to={`/${currentLanguage}/shop`}
                  >
                    {currentLanguage === 'ar' ? 'ابدأ التسوق' : 'Start Shopping'}
                  </HeroButton>
                </HoverAnimation>
                <HoverAnimation animation="scale">
                  <HeroButton
                    variant="outline"
                    as={Link}
                    to={`/${currentLanguage}/contact`}
                  >
                    {currentLanguage === 'ar' ? 'طلب استشارة' : 'Request a Consultation'}
                  </HeroButton>
                </HoverAnimation>
              </div>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Blog Preview */}
      <section className="py-20 md:py-28 dark:bg-slate-900 bg-white">
        <div className="container-custom">
          <div className="flex justify-between items-center mb-12">
            <ScrollAnimation animation="fade" delay={0.2}>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                  {t('blog.title')}
                </h2>
                <p className="text-lg text-slate-600 dark:text-slate-300">
                  {t('blog.subtitle')}
                </p>
              </div>
            </ScrollAnimation>
            <ScrollAnimation animation="fade" delay={0.4} className="hidden md:block">
              <div className="flex items-center gap-4">
                <HoverAnimation animation="scale">
                  <Button
                    variant="outline"
                    className="flex items-center"
                    as={Link}
                    to={`/${currentLanguage}/blog`}
                  >
                    {t('blog.viewAll')}
                    <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-4 w-4 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />
                  </Button>
                </HoverAnimation>
                <HoverAnimation animation="scale">
                  <Button
                    variant="primary"
                    className="flex items-center"
                    as={Link}
                    to={`/${currentLanguage}/blog/featured`}
                  >
                    {currentLanguage === 'ar' ? 'المحتوى المميز' : 'Featured Content'}
                    <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-4 w-4 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />
                  </Button>
                </HoverAnimation>
              </div>
            </ScrollAnimation>
          </div>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            delay={0.3}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {blogPosts.slice(0, 3).map((post, index) => (
              <HoverAnimation key={post.id} animation="lift">
                <Link href={`/${currentLanguage}/blog/${post.slug}`} className="group block h-full">
                  <article className="bg-white dark:bg-slate-800 rounded-lg overflow-hidden shadow-sm border border-slate-200 dark:border-slate-700 transition-all duration-300 group-hover:shadow-md h-full flex flex-col">
                    <div className="relative aspect-video overflow-hidden">
                      <EnhancedImage
                        src={post.coverImage}
                        alt={post.title}
                        fill={true}
                        objectFit="cover"
                        effect="zoom"
                        progressive={true}
                        placeholder="shimmer"
                        className="relative w-full h-full"
                        containerClassName="w-full h-full"
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0" />
                      <div className="absolute bottom-4 left-4 right-4">
                        <div className="flex items-center gap-2 text-white/90 text-sm mb-2">
                          <Calendar className="w-4 h-4" />
                          {new Date(post.publishedAt).toLocaleDateString()}
                          <span className="w-1 h-1 bg-white/90 rounded-full" />
                          {post.readTime}
                        </div>
                      </div>
                    </div>

                  <div className="p-6 flex-grow flex flex-col">
                    <div className="flex items-center gap-2 mb-3">
                      <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-50 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300">
                        {post.category}
                      </span>
                      {post.tags.slice(0, 2).map((tag, i) => (
                        <span key={i} className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300">
                          {tag}
                        </span>
                      ))}
                    </div>

                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-primary-500 dark:group-hover:text-primary-400 transition-colors line-clamp-2">
                      {post.title}
                    </h3>

                    <p className="text-slate-600 dark:text-slate-300 mb-6 line-clamp-2">
                      {post.excerpt}
                    </p>

                    <div className="mt-auto flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full overflow-hidden">
                          <EnhancedImage
                            src={post.authorImage}
                            alt={post.author}
                            fill={true}
                            objectFit="cover"
                            rounded="full"
                            progressive={true}
                            placeholder="blur"
                            className="w-full h-full"
                            containerClassName="w-full h-full"
                          />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-slate-900 dark:text-white">
                            {post.author}
                          </p>
                          <p className="text-xs text-slate-500 dark:text-slate-400">
                            {post.authorTitle}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center text-primary-500 dark:text-primary-400 font-medium group-hover:translate-x-1 transition-transform">
                        <ArrowRight className="w-4 h-4" />
                      </div>
                    </div>
                  </div>
                </article>
              </Link>
            ))}
          </div>

          <div className="mt-12 text-center md:hidden">
            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full"
                as={Link}
                to={`/${currentLanguage}/blog`}
              >
                {t('blog.viewAll')}
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-4 w-4 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />
              </Button>
              <Button
                variant="primary"
                className="w-full"
                as={Link}
                to={`/${currentLanguage}/blog/featured`}
              >
                {currentLanguage === 'ar' ? 'المحتوى المميز' : 'Featured Content'}
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-4 w-4 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {showWholesaleForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full">
            <WholesaleQuoteForm
              onClose={() => {
                setShowWholesaleForm(false);
                setSelectedProduct(null);
              }}
              isCustomProduct={false}
            />
          </div>
        </div>
      )}
    </div>
  );
}

// Helper Components
const WishlistButton = ({ product }) => {
  const { user } = useAuthStore();
  const { addItem, removeItem, isInWishlist } = useWishlistStore();
  const { openModal } = useAuthModalStore();

  const handleWishlist = () => {
    if (!user) {
      openModal('sign-in');
      return;
    }

    if (isInWishlist(product.id)) {
      removeItem(product.id);
    } else {
      addItem(product);
    }
  };

  return (
    <button
      onClick={handleWishlist}
      className={`p-2 rounded-full shadow-lg transition-all duration-300 ${
        isInWishlist(product.id)
          ? 'bg-primary-500 text-white'
          : 'bg-white dark:bg-slate-800 text-slate-500 dark:text-slate-300 hover:scale-110'
      }`}
      aria-label="Add to wishlist"
    >
      <Heart
        size={20}
        className={isInWishlist(product.id) ? 'fill-current' : ''}
      />
    </button>
  );
};

const QuickViewButton = ({ product }) => {
  const [showQuickView, setShowQuickView] = useState(false);

  return (
    <>
      <button
        onClick={() => setShowQuickView(true)}
        className="p-2 rounded-full bg-white dark:bg-slate-800 text-slate-500 dark:text-slate-300 shadow-lg hover:scale-110 transition-all duration-300"
        aria-label="Quick view"
      >
        <Eye size={20} />
      </button>
      {showQuickView && (
        <QuickView product={product} onClose={() => setShowQuickView(false)} />
      )}
    </>
  );
};

const AddToCartButton = ({ product }) => {
  const { user } = useAuthStore();
  const { addItem } = useCartStore();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const { openModal } = useAuthModalStore();
  const [isAdding, setIsAdding] = useState(false);

  const handleAddToCart = async () => {
    if (!user) {
      openModal('sign-in');
      return;
    }

    setIsAdding(true);
    addItem(product, 1);

    setTimeout(() => {
      setIsAdding(false);
    }, 1000);
  };

  return (
    <Button
      className="w-full flex items-center justify-center"
      onClick={handleAddToCart}
      disabled={isAdding || product.stock === 0}
    >
      {isAdding ? (
        <span className="flex items-center">
          <CheckCircle className={`w-4 h-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
          {currentLanguage === 'ar' ? 'تمت الإضافة!' : 'Added!'}
        </span>
      ) : (
        <span className="flex items-center">
          <ShoppingCart className={`w-4 h-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
          {t('products.addToCart')}
        </span>
      )}
    </Button>
  );
};

export default HomePage;