import type { Metadata, Viewport } from 'next';
import { <PERSON>, <PERSON><PERSON><PERSON> } from 'next/font/google';
import { RootLayout } from '../../components/layout/RootLayout';
import { Providers } from '../providers';
import '../../index.css'; // Keep for Tailwind base, components, utilities, and CSS variables

// Initialize fonts
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter', // For Tailwind CSS variable integration
  display: 'swap',
});

const tajawal = Tajawal({
  subsets: ['arabic'],
  weight: ['200', '300', '400', '500', '700', '800', '900'],
  variable: '--font-tajawal', // For Tailwind CSS variable integration
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'ARTAL | Your Complete Business Solution',
  description: 'Full-service commercial platform offering B2C retail, B2B wholesale, production lines, business services, and more.',
  manifest: '/manifest.json',
};

export const viewport: Viewport = {
  themeColor: '#9C27B0',
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
};

// Initialize theme will be handled in client components

export default function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  // تحديد فئة الخط بناءً على اللغة
  const fontUtilityClass = "font-sans";

  const themeInitializerScript = `
    (function() {
      function getInitialTheme() {
        try {
          const storedTheme = localStorage.getItem('ui-theme');
          if (storedTheme) {
            return storedTheme;
          }
          const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
          if (mediaQuery.matches) {
            return 'dark';
          }
        } catch (e) {
          // localStorage is not available or other error
        }
        return 'light'; // Default theme if no preference or error
      }

      const theme = getInitialTheme();
      if (theme === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        // Ensure dark class is removed if theme is light or system preference is light
        // and no specific theme is stored.
        document.documentElement.classList.remove('dark');
      }
    })();
  `;

  return (
    // Apply font variables and the determined font utility class to HTML tag
    <html lang="en" dir="ltr" className={`${inter.variable} ${tajawal.variable} ${fontUtilityClass}`} suppressHydrationWarning>
      <head>
        <script dangerouslySetInnerHTML={{ __html: themeInitializerScript }} />
      </head>
      {/* Body tag will inherit font from HTML. Styling like background, text color, and antialiasing
          are applied globally via CSS (e.g., in index.css or Tailwind base styles) */}
      <body suppressHydrationWarning>
        <Providers locale="en">
          <RootLayout>
            {children}
          </RootLayout>
        </Providers>
      </body>
    </html>
  );
}
