'use client';

import { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowRight,
  Search,
  Package,
  Truck,
  FileCheck,
  Users,
  ClipboardList,
  X,
  Star,
  Clock,
  CheckCircle,
  Phone,
  Mail,
  MapPin
} from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { ServiceBookingForm } from '../../components/forms/ServiceBookingForm';
import { services } from '../../data/services';
import { useThemeStore } from '../../stores/themeStore';
import { useLanguageStore } from '../../stores/languageStore';
import { cn } from '../../lib/utils';

const icons = {
  Search,
  Package,
  Truck,
  FileCheck,
  Users,
  ClipboardList
};

interface ServiceFilters {
  searchQuery: string;
  category: string;
  sortBy: 'name' | 'popularity' | 'recent';
}

export default function ServicesPage() {
  const router = useRouter();
  const [showBookingForm, setShowBookingForm] = useState(false);
  const [selectedService, setSelectedService] = useState<string | undefined>();
  const [filters, setFilters] = useState<ServiceFilters>({
    searchQuery: '',
    category: 'all',
    sortBy: 'name'
  });
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();

  // استخدام اللغة من المتجر
  const currentLanguage = language;

  // Service categories for filtering
  const categories = [
    { id: 'all', name: currentLanguage === 'ar' ? 'جميع الخدمات' : 'All Services' },
    { id: 'inspection', name: currentLanguage === 'ar' ? 'خدمات الفحص' : 'Inspection Services' },
    { id: 'logistics', name: currentLanguage === 'ar' ? 'الخدمات اللوجستية' : 'Logistics Services' },
    { id: 'consulting', name: currentLanguage === 'ar' ? 'الاستشارات' : 'Consulting Services' },
    { id: 'certification', name: currentLanguage === 'ar' ? 'الشهادات' : 'Certification Services' }
  ];

  // Filter and sort services
  const filteredServices = useMemo(() => {
    let filtered = services.filter(service => {
      const searchTerm = filters.searchQuery.toLowerCase();
      const serviceName = currentLanguage === 'ar' ? service.name_ar || service.name : service.name;
      const serviceDesc = currentLanguage === 'ar' ? service.description_ar || service.description : service.description;

      const matchesSearch = !searchTerm ||
        serviceName.toLowerCase().includes(searchTerm) ||
        serviceDesc.toLowerCase().includes(searchTerm);

      const matchesCategory = filters.category === 'all' ||
        service.id.includes(filters.category) ||
        (filters.category === 'logistics' && ['shipping', 'storage'].includes(service.id)) ||
        (filters.category === 'inspection' && service.id === 'inspection') ||
        (filters.category === 'consulting' && ['consulting', 'order-management'].includes(service.id)) ||
        (filters.category === 'certification' && service.id === 'certification');

      return matchesSearch && matchesCategory;
    });

    // Sort services
    filtered.sort((a, b) => {
      const aName = currentLanguage === 'ar' ? a.name_ar || a.name : a.name;
      const bName = currentLanguage === 'ar' ? b.name_ar || b.name : b.name;

      switch (filters.sortBy) {
        case 'name':
          return aName.localeCompare(bName);
        case 'popularity':
          // Mock popularity sorting - in real app, this would be based on actual data
          return Math.random() - 0.5;
        case 'recent':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [services, filters, currentLanguage]);

  const handleBookService = (serviceName: string) => {
    setSelectedService(serviceName);
    setShowBookingForm(true);
  };

  const clearFilters = () => {
    setFilters({
      searchQuery: '',
      category: 'all',
      sortBy: 'name'
    });
  };

  return (
    <div>
      {/* Compact Professional Hero Section */}
      <section className={cn(
        "relative py-16 overflow-hidden transition-colors duration-500",
        isDarkMode
          ? "bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900"
          : "bg-gradient-to-br from-slate-50 via-white to-slate-100"
      )}>
        {/* Subtle Background Pattern */}
        <div className="absolute inset-0">
          <div className={cn(
            "absolute inset-0 opacity-5 transition-opacity duration-500",
            isDarkMode ? "opacity-10" : "opacity-5"
          )}>
            <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
          </div>
        </div>

        <div className="container-custom relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            {/* Main Heading */}
            <h1 className={cn(
              "text-4xl md:text-5xl font-bold leading-tight tracking-tight mb-6",
              isDarkMode ? "text-white" : "text-slate-900"
            )}>
              <span className="block">
                {currentLanguage === 'ar' ? 'خدمات' : 'Business'}
              </span>
              <span className={cn(
                "block bg-gradient-to-r bg-clip-text text-transparent",
                isDarkMode
                  ? "from-primary-400 to-blue-400"
                  : "from-primary-600 to-blue-600"
              )}>
                {currentLanguage === 'ar' ? 'الأعمال' : 'Services'}
              </span>
            </h1>

            {/* Description */}
            <p className={cn(
              "text-lg md:text-xl leading-relaxed max-w-3xl mx-auto mb-8",
              isDarkMode ? "text-slate-300" : "text-slate-600"
            )}>
              {currentLanguage === 'ar'
                ? 'خدمات دعم شاملة لتبسيط عملياتك وتعزيز كفاءة أعمالك مع حلول متطورة ومخصصة.'
                : 'Comprehensive support services to streamline your operations and enhance business efficiency with advanced, tailored solutions.'}
            </p>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              {[
                {
                  icon: <CheckCircle className="h-5 w-5" />,
                  value: '6+',
                  label: currentLanguage === 'ar' ? 'خدمة متخصصة' : 'Expert Services'
                },
                {
                  icon: <Star className="h-5 w-5" />,
                  value: '500+',
                  label: currentLanguage === 'ar' ? 'عميل راضي' : 'Satisfied Clients'
                },
                {
                  icon: <Clock className="h-5 w-5" />,
                  value: '24/48h',
                  label: currentLanguage === 'ar' ? 'وقت الاستجابة' : 'Response Time'
                },
                {
                  icon: <MapPin className="h-5 w-5" />,
                  value: '50+',
                  label: currentLanguage === 'ar' ? 'دولة' : 'Countries'
                }
              ].map((stat, index) => (
                <div key={index} className={cn(
                  "p-4 rounded-lg transition-colors duration-300",
                  isDarkMode
                    ? "bg-slate-800/50 hover:bg-slate-800/70"
                    : "bg-white/50 hover:bg-white/70"
                )}>
                  <div className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full mx-auto mb-2",
                    isDarkMode ? "bg-primary-500/20 text-primary-400" : "bg-primary-100 text-primary-600"
                  )}>
                    {stat.icon}
                  </div>
                  <div className={cn(
                    "text-lg font-bold mb-1",
                    isDarkMode ? "text-white" : "text-slate-900"
                  )}>
                    {stat.value}
                  </div>
                  <div className={cn(
                    "text-xs font-medium",
                    isDarkMode ? "text-slate-300" : "text-slate-600"
                  )}>
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                size="lg"
                variant="primary"
                className="px-8 py-3 text-lg font-semibold group"
                onClick={() => router.push(`/${currentLanguage}/contact`)}
              >
                <Phone className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} h-5 w-5 group-hover:scale-110 transition-transform`} />
                {currentLanguage === 'ar' ? 'استشارة مجانية' : 'Free Consultation'}
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="px-8 py-3 text-lg font-semibold group"
                onClick={() => document.getElementById('services-grid')?.scrollIntoView({ behavior: 'smooth' })}
              >
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 group-hover:translate-x-1 transition-transform`} />
                {currentLanguage === 'ar' ? 'استكشف الخدمات' : 'Explore Services'}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Advanced Search and Filter Section */}
      <section className={cn("py-12", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            {/* Search Bar */}
            <div className="relative mb-6">
                <Search className={cn(
                  "absolute top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400",
                  currentLanguage === 'ar' ? "right-4" : "left-4"
                )} />
                <Input
                  type="text"
                  placeholder={currentLanguage === 'ar' ? 'ابحث عن الخدمات...' : 'Search services...'}
                  value={filters.searchQuery}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchQuery: e.target.value }))}
                  className={cn(
                    "w-full py-4 text-lg rounded-xl border-2 transition-all duration-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
                    currentLanguage === 'ar' ? "pr-12 pl-4" : "pl-12 pr-4",
                    isDarkMode
                      ? "bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                      : "bg-white border-slate-200 text-slate-900 placeholder-slate-500"
                  )}
                />
                {filters.searchQuery && (
                  <button
                    onClick={() => setFilters(prev => ({ ...prev, searchQuery: '' }))}
                    className={cn(
                      "absolute top-1/2 transform -translate-y-1/2 p-2 rounded-full hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors",
                      currentLanguage === 'ar' ? "left-2" : "right-2"
                    )}
                  >
                    <X className="h-4 w-4 text-slate-400" />
                  </button>
                )}
              </div>

              {/* Filter Controls */}
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                {/* Category Filter */}
                <div className="flex-1">
                  <label className={cn(
                    "block text-sm font-medium mb-2",
                    isDarkMode ? "text-slate-300" : "text-slate-700"
                  )}>
                    {currentLanguage === 'ar' ? 'فئة الخدمة' : 'Service Category'}
                  </label>
                  <select
                    value={filters.category}
                    onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                    className={cn(
                      "w-full p-3 rounded-lg border transition-colors duration-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
                      isDarkMode
                        ? "bg-slate-700 border-slate-600 text-white"
                        : "bg-white border-slate-200 text-slate-900"
                    )}
                  >
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Sort Filter */}
                <div className="flex-1">
                  <label className={cn(
                    "block text-sm font-medium mb-2",
                    isDarkMode ? "text-slate-300" : "text-slate-700"
                  )}>
                    {currentLanguage === 'ar' ? 'ترتيب حسب' : 'Sort By'}
                  </label>
                  <select
                    value={filters.sortBy}
                    onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as 'name' | 'popularity' | 'recent' }))}
                    className={cn(
                      "w-full p-3 rounded-lg border transition-colors duration-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
                      isDarkMode
                        ? "bg-slate-700 border-slate-600 text-white"
                        : "bg-white border-slate-200 text-slate-900"
                    )}
                  >
                    <option value="name">{currentLanguage === 'ar' ? 'الاسم' : 'Name'}</option>
                    <option value="popularity">{currentLanguage === 'ar' ? 'الشعبية' : 'Popularity'}</option>
                    <option value="recent">{currentLanguage === 'ar' ? 'الأحدث' : 'Most Recent'}</option>
                  </select>
                </div>

                {/* Clear Filters */}
                {(filters.searchQuery || filters.category !== 'all' || filters.sortBy !== 'name') && (
                  <div className="flex items-end">
                    <Button
                      variant="outline"
                      onClick={clearFilters}
                      className="px-6 py-3 h-fit"
                    >
                      <X className="h-4 w-4 mr-2" />
                      {currentLanguage === 'ar' ? 'مسح الفلاتر' : 'Clear Filters'}
                    </Button>
                  </div>
                )}
              </div>

              {/* Results Summary */}
              <div className={cn(
                "text-sm mb-6 p-3 rounded-lg",
                isDarkMode ? "bg-slate-700 text-slate-300" : "bg-white text-slate-600"
              )}>
                {currentLanguage === 'ar'
                  ? `عرض ${filteredServices.length} من ${services.length} خدمة`
                  : `Showing ${filteredServices.length} of ${services.length} services`}
                {filters.searchQuery && (
                  <span className="ml-2">
                    {currentLanguage === 'ar'
                      ? `للبحث "${filters.searchQuery}"`
                      : `for "${filters.searchQuery}"`}
                  </span>
                )}
              </div>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Services Grid */}
      <section id="services-grid" className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'عروض خدماتنا' : 'Our Service Offerings'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'اكتشف مجموعة كاملة من خدمات الأعمال المصممة لدعم عملياتك في كل مرحلة.'
                  : 'Discover the full range of business services designed to support your operations at every stage.'}
              </p>
            </div>
          </ScrollAnimation>

          {filteredServices.length > 0 ? (
            <ScrollStagger
              animation="slide"
              direction="up"
              staggerDelay={0.1}
              delay={0.4}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {filteredServices.map((service) => {
                const Icon = icons[service.icon as keyof typeof icons];
                return (
                  <HoverAnimation key={service.id} animation="lift">
                    <Card className="group hover:shadow-xl transition-all duration-300 h-full">
                      <CardHeader className="text-center pb-4">
                        <div className="flex justify-center mb-4">
                          <div className={cn(
                            "p-4 rounded-full transition-all duration-300 group-hover:scale-110",
                            isDarkMode ? "bg-primary-500/20" : "bg-primary-50"
                          )}>
                            <Icon size={32} className="text-primary-500 dark:text-primary-400" />
                          </div>
                        </div>
                        <CardTitle className="text-xl mb-2 text-slate-900 dark:text-white">
                          {currentLanguage === 'ar' ? service.name_ar || service.name : service.name}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="flex flex-col h-full">
                        <p className="text-slate-600 dark:text-slate-300 mb-6 flex-grow">
                          {currentLanguage === 'ar' ? service.description_ar || service.description : service.description}
                        </p>

                        {/* Service Features Preview */}
                        <div className="mb-6">
                          <div className="flex flex-wrap gap-2">
                            {(currentLanguage === 'ar' ? service.features_ar || service.features : service.features)
                              .slice(0, 3)
                              .map((feature, index) => (
                                <span
                                  key={index}
                                  className={cn(
                                    "text-xs px-2 py-1 rounded-full",
                                    isDarkMode
                                      ? "bg-slate-700 text-slate-300"
                                      : "bg-slate-100 text-slate-600"
                                  )}
                                >
                                  {feature}
                                </span>
                              ))}
                            {service.features.length > 3 && (
                              <span className={cn(
                                "text-xs px-2 py-1 rounded-full",
                                isDarkMode
                                  ? "bg-primary-500/20 text-primary-400"
                                  : "bg-primary-50 text-primary-600"
                              )}>
                                +{service.features.length - 3} {currentLanguage === 'ar' ? 'المزيد' : 'more'}
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex gap-2 mt-auto">
                          <HoverAnimation animation="scale">
                            <Button
                              onClick={() => handleBookService(currentLanguage === 'ar' ? service.name_ar || service.name : service.name)}
                              className="flex-1 group"
                              variant="primary"
                            >
                              <Phone className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                              {currentLanguage === 'ar' ? 'احجز الخدمة' : 'Book Service'}
                            </Button>
                          </HoverAnimation>
                          <HoverAnimation animation="scale">
                            <Button
                              onClick={() => router.push(`/${currentLanguage}/services/${service.slug}`)}
                              variant="outline"
                              className="flex-1 group"
                            >
                              {currentLanguage === 'ar' ? 'معرفة المزيد' : 'Learn More'}
                              <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-4 w-4 transition-transform group-hover:translate-x-1`} />
                            </Button>
                          </HoverAnimation>
                        </div>
                      </CardContent>
                    </Card>
                  </HoverAnimation>
                );
              })}
            </ScrollStagger>
          ) : (
            <ScrollAnimation animation="fade" delay={0.4}>
              <div className="text-center py-16">
                <div className={cn(
                  "w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-6",
                  isDarkMode ? "bg-slate-800" : "bg-slate-100"
                )}>
                  <Search className="h-12 w-12 text-slate-400" />
                </div>
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                  {currentLanguage === 'ar' ? 'لم يتم العثور على خدمات' : 'No Services Found'}
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-6">
                  {currentLanguage === 'ar'
                    ? 'جرب تعديل معايير البحث أو الفلاتر للعثور على ما تبحث عنه.'
                    : 'Try adjusting your search criteria or filters to find what you\'re looking for.'}
                </p>
                <Button onClick={clearFilters} variant="outline">
                  {currentLanguage === 'ar' ? 'مسح جميع الفلاتر' : 'Clear All Filters'}
                </Button>
              </div>
            </ScrollAnimation>
          )}
        </div>
      </section>

      {/* How We Work */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'كيف نعمل' : 'How We Work'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'نهجنا الاستشاري يضمن حلولاً مخصصة لاحتياجات عملك الفريدة.'
                  : 'Our consultative approach ensures tailored solutions for your unique business needs.'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="right"
            staggerDelay={0.15}
            delay={0.4}
            className="grid grid-cols-1 md:grid-cols-4 gap-8"
          >
            {[
              {
                number: "01",
                title: currentLanguage === 'ar' ? "الاستشارة" : "Consultation",
                description: currentLanguage === 'ar'
                  ? "نبدأ باستشارة شاملة لفهم احتياجات وتحديات عملك."
                  : "We begin with a thorough consultation to understand your business needs and challenges.",
              },
              {
                number: "02",
                title: currentLanguage === 'ar' ? "التحليل" : "Analysis",
                description: currentLanguage === 'ar'
                  ? "يقوم خبراؤنا بتحليل متطلباتك وتطوير توصيات خدمة مخصصة."
                  : "Our experts analyze your requirements and develop customized service recommendations.",
              },
              {
                number: "03",
                title: currentLanguage === 'ar' ? "التنفيذ" : "Implementation",
                description: currentLanguage === 'ar'
                  ? "نقوم بتنفيذ الخدمات المتفق عليها مع الاهتمام بالتفاصيل وضمان الجودة."
                  : "We implement the agreed services with attention to detail and quality assurance.",
              },
              {
                number: "04",
                title: currentLanguage === 'ar' ? "الدعم المستمر" : "Ongoing Support",
                description: currentLanguage === 'ar'
                  ? "المراقبة والدعم المستمر يضمنان النتائج المثلى والقدرة على التكيف."
                  : "Continuous monitoring and support ensure optimal results and adaptability.",
              },
            ].map((step, index) => (
              <HoverAnimation key={index} animation="lift" className="relative text-center">
                <div className="bg-primary-500 text-white rounded-full h-12 w-12 flex items-center justify-center font-bold text-lg mb-4 mx-auto">
                  {step.number}
                </div>
                <div className={cn("p-6 rounded-lg shadow-sm h-full", isDarkMode ? "bg-slate-900" : "bg-white")}>
                  <h3 className="text-xl font-semibold mb-2 text-slate-900 dark:text-white">{step.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{step.description}</p>
                </div>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-primary-500 text-white">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'هل أنت مستعد لتعزيز عمليات عملك؟' : 'Ready to Enhance Your Business Operations?'}
              </h2>
              <p className="text-xl mb-8 text-primary-50">
                {currentLanguage === 'ar'
                  ? 'اتصل بفريقنا لمناقشة كيف يمكن لخدماتنا تلبية احتياجات عملك المحددة.'
                  : 'Contact our team to discuss how our services can address your specific business needs.'}
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <HoverAnimation animation="scale">
                  <Button
                    onClick={() => router.push(`/${currentLanguage}/services/request`)}
                    size="lg"
                    variant="outline"
                    className="bg-transparent border-white text-white hover:bg-primary-600 px-8"
                  >
                    {currentLanguage === 'ar' ? 'طلب عرض سعر للخدمة' : 'Request Service Quote'}
                  </Button>
                </HoverAnimation>
              </div>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* FAQ Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'الأسئلة الشائعة' : 'Frequently Asked Questions'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'ابحث عن إجابات للأسئلة الشائعة حول خدمات أعمالنا.'
                  : 'Find answers to common questions about our business services.'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="fade"
            staggerDelay={0.1}
            delay={0.4}
            className="max-w-4xl mx-auto space-y-6"
          >
            {[
              {
                question: currentLanguage === 'ar' ? "ما هي أنواع الشركات التي تخدمونها؟" : "What types of businesses do you serve?",
                answer: currentLanguage === 'ar'
                  ? "نخدم مجموعة واسعة من الشركات عبر مختلف الصناعات، بما في ذلك التصنيع والتجزئة والتوزيع والتجارة الإلكترونية. خدماتنا قابلة للتطوير ويمكن تخصيصها لتلبية احتياجات كل من الشركات الصغيرة والمؤسسات الكبيرة."
                  : "We serve a wide range of businesses across various industries, including manufacturing, retail, distribution, and e-commerce. Our services are scalable and can be customized to meet the needs of both small businesses and large enterprises.",
              },
              {
                question: currentLanguage === 'ar' ? "ما هي سرعة ترتيب خدمات الفحص؟" : "How quickly can you arrange inspection services?",
                answer: currentLanguage === 'ar'
                  ? "يمكن ترتيب خدمات الفحص القياسية في غضون 48-72 ساعة من الطلب. للاحتياجات العاجلة، نقدم خدمات معجلة يمكن جدولتها في غضون 24 ساعة في معظم المواقع، حسب التوفر. تضمن شبكتنا العالمية من المفتشين أوقات استجابة سريعة."
                  : "Standard inspection services can be arranged within 48-72 hours of request. For urgent needs, we offer expedited services that can be scheduled within 24 hours in most locations, subject to availability. Our global network of inspectors ensures quick response times.",
              },
              {
                question: currentLanguage === 'ar' ? "هل تقدمون خدمات دولية؟" : "Do you provide services internationally?",
                answer: currentLanguage === 'ar'
                  ? "نعم، نقدم خدمات أعمالنا عالميًا من خلال شبكتنا الواسعة من المكاتب والشركاء في مراكز التصنيع والخدمات اللوجستية الرئيسية عبر آسيا وأوروبا والأمريكتين. يمكننا تنسيق الخدمات عبر العديد من البلدان والمناطق."
                  : "Yes, we offer our business services globally through our extensive network of offices and partners in major manufacturing and logistics hubs across Asia, Europe, and the Americas. We can coordinate services across multiple countries and regions.",
              },
              {
                question: "What certifications and standards do you comply with?",
                answer: "Our services comply with international standards including ISO 9001, ISO 14001, and industry-specific certifications. For product certification services, we work with all major certification bodies and can assist with CE, FCC, UL, and other market-specific requirements.",
              },
              {
                question: "How do you ensure service quality and consistency?",
                answer: "We maintain strict quality control processes, including regular training for our staff, standardized operating procedures, and continuous monitoring of service delivery. Our quality management system ensures consistent service delivery across all locations.",
              },
              {
                question: "What are your payment terms and methods?",
                answer: "We accept various payment methods including bank transfers, credit cards, and digital payments. For regular clients, we offer flexible payment terms including net-30 accounts. Volume-based discounts and service packages are available for long-term contracts.",
              },
              {
                question: "Can you handle rush orders or emergency situations?",
                answer: "Yes, we have dedicated teams to handle urgent requests and emergency situations. We offer expedited services across all our service lines with priority handling and 24/7 support for critical situations.",
              },
              {
                question: "Do you provide customized service packages?",
                answer: "Yes, we can create customized service packages that combine multiple services to meet your specific business needs. Our solutions architects will work with you to design the most efficient and cost-effective service package.",
              },
              {
                question: "What kind of reporting and documentation do you provide?",
                answer: "We provide detailed digital reports for all services, including inspection findings, certification status, shipping documentation, and performance analytics. Our clients have access to a secure portal for real-time tracking and report downloads.",
              },
              {
                question: "How do you handle confidential information?",
                answer: "We maintain strict confidentiality protocols and are compliant with GDPR and other data protection regulations. All client information is stored securely, and our staff signs comprehensive NDAs.",
              }
            ].map((faq, index) => (
              <HoverAnimation key={index} animation="lift">
                <details
                  className={cn("group rounded-lg overflow-hidden", isDarkMode ? "bg-slate-800" : "bg-slate-50")}
                >
                <summary className="flex items-center justify-between p-6 cursor-pointer">
                  <h3 className="text-xl font-medium text-slate-900 dark:text-white pr-8">{faq.question}</h3>
                  <span className={cn("flex-shrink-0 ml-1.5 p-1.5 rounded-full", isDarkMode ? "text-slate-300 bg-slate-700" : "text-slate-700 bg-white")}>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 transform group-open:rotate-180 transition-transform duration-200"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </span>
                </summary>
                <div className="px-6 pb-6">
                  <p className="text-slate-600 dark:text-slate-300">{faq.answer}</p>
                </div>
              </details>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Booking Form Modal */}
      {showBookingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <ScrollAnimation animation="scale" duration={0.3}>
            <div className="max-w-2xl w-full">
              <ServiceBookingForm
                serviceName={selectedService}
                onClose={() => {
                  setShowBookingForm(false);
                  setSelectedService(undefined);
                }}
              />
            </div>
          </ScrollAnimation>
        </div>
      )}
    </div>
  );
}