'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowRight, Search, Package, Truck, FileCheck, Users, ClipboardList } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { ServiceBookingForm } from '../../components/forms/ServiceBookingForm';
import { services } from '../../data/services';
import { useThemeStore } from '../../stores/themeStore';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { cn } from '../../lib/utils';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { EnhancedImage } from '../../components/ui/EnhancedImage';

const icons = {
  Search,
  Package,
  Truck,
  FileCheck,
  Users,
  ClipboardList
};

export default function ServicesPage() {
  const router = useRouter();
  const [showBookingForm, setShowBookingForm] = useState(false);
  const [selectedService, setSelectedService] = useState<string | undefined>();
  const { isDarkMode } = useThemeStore();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  const handleBookService = (serviceName: string) => {
    setSelectedService(serviceName);
    setShowBookingForm(true);
  };

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-800 to-slate-900 text-white py-20">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="max-w-3xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'خدمات الأعمال' : 'Business Services'}
              </h1>
              <p className="text-xl mb-8 text-slate-300">
                {currentLanguage === 'ar'
                  ? 'خدمات دعم شاملة لتبسيط عملياتك وتعزيز كفاءة أعمالك.'
                  : 'Comprehensive support services to streamline your operations and enhance your business efficiency.'}
              </p>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Services Overview */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'عروض خدماتنا' : 'Our Service Offerings'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'اكتشف مجموعة كاملة من خدمات الأعمال المصممة لدعم عملياتك في كل مرحلة.'
                  : 'Discover the full range of business services designed to support your operations at every stage.'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            delay={0.4}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {services.map((service) => {
              const Icon = icons[service.icon as keyof typeof icons];
              return (
                <HoverAnimation key={service.id} animation="lift">
                  <Card className="group hover:shadow-md transition-shadow duration-300">
                    <CardHeader className="text-center">
                      <div className="flex justify-center mb-4">
                        <Icon size={40} className="text-primary-500 dark:text-primary-400" />
                      </div>
                      <CardTitle className="text-xl mb-2 text-slate-900 dark:text-white">
                        {currentLanguage === 'ar' ? service.name_ar || service.name : service.name}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-slate-600 dark:text-slate-300 mb-6">
                        {currentLanguage === 'ar' ? service.description_ar || service.description : service.description}
                      </p>
                      <div className="flex gap-2">
                        <HoverAnimation animation="scale">
                          <Button
                            onClick={() => handleBookService(currentLanguage === 'ar' ? service.name_ar || service.name : service.name)}
                            className="flex-1"
                          >
                            {currentLanguage === 'ar' ? 'احجز الخدمة' : 'Book Service'}
                          </Button>
                        </HoverAnimation>
                        <HoverAnimation animation="scale">
                          <Button
                            onClick={() => router.push(`/${currentLanguage}/services/${service.slug}`)}
                            variant="outline"
                            className="flex-1 group-hover:bg-primary-50 group-hover:border-primary-300 transition-colors"
                          >
                            {currentLanguage === 'ar' ? 'معرفة المزيد' : 'Learn More'}
                            <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-4 w-4 transition-transform group-hover:translate-x-1`} />
                          </Button>
                        </HoverAnimation>
                      </div>
                    </CardContent>
                  </Card>
                </HoverAnimation>
              );
            })}
          </ScrollStagger>
        </div>
      </section>

      {/* How We Work */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'كيف نعمل' : 'How We Work'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'نهجنا الاستشاري يضمن حلولاً مخصصة لاحتياجات عملك الفريدة.'
                  : 'Our consultative approach ensures tailored solutions for your unique business needs.'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="right"
            staggerDelay={0.15}
            delay={0.4}
            className="grid grid-cols-1 md:grid-cols-4 gap-8"
          >
            {[
              {
                number: "01",
                title: currentLanguage === 'ar' ? "الاستشارة" : "Consultation",
                description: currentLanguage === 'ar'
                  ? "نبدأ باستشارة شاملة لفهم احتياجات وتحديات عملك."
                  : "We begin with a thorough consultation to understand your business needs and challenges.",
              },
              {
                number: "02",
                title: currentLanguage === 'ar' ? "التحليل" : "Analysis",
                description: currentLanguage === 'ar'
                  ? "يقوم خبراؤنا بتحليل متطلباتك وتطوير توصيات خدمة مخصصة."
                  : "Our experts analyze your requirements and develop customized service recommendations.",
              },
              {
                number: "03",
                title: currentLanguage === 'ar' ? "التنفيذ" : "Implementation",
                description: currentLanguage === 'ar'
                  ? "نقوم بتنفيذ الخدمات المتفق عليها مع الاهتمام بالتفاصيل وضمان الجودة."
                  : "We implement the agreed services with attention to detail and quality assurance.",
              },
              {
                number: "04",
                title: currentLanguage === 'ar' ? "الدعم المستمر" : "Ongoing Support",
                description: currentLanguage === 'ar'
                  ? "المراقبة والدعم المستمر يضمنان النتائج المثلى والقدرة على التكيف."
                  : "Continuous monitoring and support ensure optimal results and adaptability.",
              },
            ].map((step, index) => (
              <HoverAnimation key={index} animation="lift" className="relative text-center">
                <div className="bg-primary-500 text-white rounded-full h-12 w-12 flex items-center justify-center font-bold text-lg mb-4 mx-auto">
                  {step.number}
                </div>
                <div className={cn("p-6 rounded-lg shadow-sm h-full", isDarkMode ? "bg-slate-900" : "bg-white")}>
                  <h3 className="text-xl font-semibold mb-2 text-slate-900 dark:text-white">{step.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{step.description}</p>
                </div>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-primary-500 text-white">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'هل أنت مستعد لتعزيز عمليات عملك؟' : 'Ready to Enhance Your Business Operations?'}
              </h2>
              <p className="text-xl mb-8 text-primary-50">
                {currentLanguage === 'ar'
                  ? 'اتصل بفريقنا لمناقشة كيف يمكن لخدماتنا تلبية احتياجات عملك المحددة.'
                  : 'Contact our team to discuss how our services can address your specific business needs.'}
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <HoverAnimation animation="scale">
                  <Button
                    onClick={() => router.push(`/${currentLanguage}/services/request`)}
                    size="lg"
                    variant="outline"
                    className="bg-transparent border-white text-white hover:bg-primary-600 px-8"
                  >
                    {currentLanguage === 'ar' ? 'طلب عرض سعر للخدمة' : 'Request Service Quote'}
                  </Button>
                </HoverAnimation>
              </div>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* FAQ Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.3}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'الأسئلة الشائعة' : 'Frequently Asked Questions'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'ابحث عن إجابات للأسئلة الشائعة حول خدمات أعمالنا.'
                  : 'Find answers to common questions about our business services.'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="fade"
            staggerDelay={0.1}
            delay={0.4}
            className="max-w-4xl mx-auto space-y-6"
          >
            {[
              {
                question: currentLanguage === 'ar' ? "ما هي أنواع الشركات التي تخدمونها؟" : "What types of businesses do you serve?",
                answer: currentLanguage === 'ar'
                  ? "نخدم مجموعة واسعة من الشركات عبر مختلف الصناعات، بما في ذلك التصنيع والتجزئة والتوزيع والتجارة الإلكترونية. خدماتنا قابلة للتطوير ويمكن تخصيصها لتلبية احتياجات كل من الشركات الصغيرة والمؤسسات الكبيرة."
                  : "We serve a wide range of businesses across various industries, including manufacturing, retail, distribution, and e-commerce. Our services are scalable and can be customized to meet the needs of both small businesses and large enterprises.",
              },
              {
                question: currentLanguage === 'ar' ? "ما هي سرعة ترتيب خدمات الفحص؟" : "How quickly can you arrange inspection services?",
                answer: currentLanguage === 'ar'
                  ? "يمكن ترتيب خدمات الفحص القياسية في غضون 48-72 ساعة من الطلب. للاحتياجات العاجلة، نقدم خدمات معجلة يمكن جدولتها في غضون 24 ساعة في معظم المواقع، حسب التوفر. تضمن شبكتنا العالمية من المفتشين أوقات استجابة سريعة."
                  : "Standard inspection services can be arranged within 48-72 hours of request. For urgent needs, we offer expedited services that can be scheduled within 24 hours in most locations, subject to availability. Our global network of inspectors ensures quick response times.",
              },
              {
                question: currentLanguage === 'ar' ? "هل تقدمون خدمات دولية؟" : "Do you provide services internationally?",
                answer: currentLanguage === 'ar'
                  ? "نعم، نقدم خدمات أعمالنا عالميًا من خلال شبكتنا الواسعة من المكاتب والشركاء في مراكز التصنيع والخدمات اللوجستية الرئيسية عبر آسيا وأوروبا والأمريكتين. يمكننا تنسيق الخدمات عبر العديد من البلدان والمناطق."
                  : "Yes, we offer our business services globally through our extensive network of offices and partners in major manufacturing and logistics hubs across Asia, Europe, and the Americas. We can coordinate services across multiple countries and regions.",
              },
              {
                question: "What certifications and standards do you comply with?",
                answer: "Our services comply with international standards including ISO 9001, ISO 14001, and industry-specific certifications. For product certification services, we work with all major certification bodies and can assist with CE, FCC, UL, and other market-specific requirements.",
              },
              {
                question: "How do you ensure service quality and consistency?",
                answer: "We maintain strict quality control processes, including regular training for our staff, standardized operating procedures, and continuous monitoring of service delivery. Our quality management system ensures consistent service delivery across all locations.",
              },
              {
                question: "What are your payment terms and methods?",
                answer: "We accept various payment methods including bank transfers, credit cards, and digital payments. For regular clients, we offer flexible payment terms including net-30 accounts. Volume-based discounts and service packages are available for long-term contracts.",
              },
              {
                question: "Can you handle rush orders or emergency situations?",
                answer: "Yes, we have dedicated teams to handle urgent requests and emergency situations. We offer expedited services across all our service lines with priority handling and 24/7 support for critical situations.",
              },
              {
                question: "Do you provide customized service packages?",
                answer: "Yes, we can create customized service packages that combine multiple services to meet your specific business needs. Our solutions architects will work with you to design the most efficient and cost-effective service package.",
              },
              {
                question: "What kind of reporting and documentation do you provide?",
                answer: "We provide detailed digital reports for all services, including inspection findings, certification status, shipping documentation, and performance analytics. Our clients have access to a secure portal for real-time tracking and report downloads.",
              },
              {
                question: "How do you handle confidential information?",
                answer: "We maintain strict confidentiality protocols and are compliant with GDPR and other data protection regulations. All client information is stored securely, and our staff signs comprehensive NDAs.",
              }
            ].map((faq, index) => (
              <HoverAnimation key={index} animation="lift">
                <details
                  className={cn("group rounded-lg overflow-hidden", isDarkMode ? "bg-slate-800" : "bg-slate-50")}
                >
                <summary className="flex items-center justify-between p-6 cursor-pointer">
                  <h3 className="text-xl font-medium text-slate-900 dark:text-white pr-8">{faq.question}</h3>
                  <span className={cn("flex-shrink-0 ml-1.5 p-1.5 rounded-full", isDarkMode ? "text-slate-300 bg-slate-700" : "text-slate-700 bg-white")}>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 transform group-open:rotate-180 transition-transform duration-200"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </span>
                </summary>
                <div className="px-6 pb-6">
                  <p className="text-slate-600 dark:text-slate-300">{faq.answer}</p>
                </div>
              </details>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Booking Form Modal */}
      {showBookingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <ScrollAnimation animation="scale" duration={0.3}>
            <div className="max-w-2xl w-full">
              <ServiceBookingForm
                serviceName={selectedService}
                onClose={() => {
                  setShowBookingForm(false);
                  setSelectedService(undefined);
                }}
              />
            </div>
          </ScrollAnimation>
        </div>
      )}
    </div>
  );
}