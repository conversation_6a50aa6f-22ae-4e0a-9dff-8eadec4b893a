import { ButtonHTMLAttributes, forwardRef, ElementType, ReactNode } from 'react';
import Link from 'next/link';
import { cn } from '../../lib/utils';
import { motion } from 'framer-motion';
import { VisuallyHidden } from './VisuallyHidden';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost' | 'link' | 'destructive';
  size?: 'sm' | 'md' | 'lg' | 'icon';
  isLoading?: boolean;
  as?: ElementType;
  to?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  hoverEffect?: 'none' | 'scale' | 'glow' | 'lift';
  /** نص إضافي للقارئات الشاشة فقط */
  accessibilityLabel?: string;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    isLoading,
    children,
    disabled,
    as,
    to,
    leftIcon,
    rightIcon,
    hoverEffect = 'none',
    accessibilityLabel,
    ...props
  }, ref) => {
    const baseStyles = 'group inline-flex items-center justify-center font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none rounded-md relative overflow-hidden';

    const variants = {
      primary: 'bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 focus-visible:ring-primary-500 shadow-md hover:shadow-lg',
      secondary: 'bg-gradient-to-r from-secondary-500 to-secondary-600 text-white hover:from-secondary-600 hover:to-secondary-700 focus-visible:ring-secondary-500 shadow-md hover:shadow-lg',
      accent: 'bg-gradient-to-r from-accent-500 to-accent-600 text-white hover:from-accent-600 hover:to-accent-700 focus-visible:ring-accent-500 shadow-md hover:shadow-lg',
      outline: 'border-2 border-slate-300 bg-transparent hover:border-primary-500 hover:text-primary-600 focus-visible:ring-primary-500 dark:border-slate-600 dark:hover:border-primary-400 dark:hover:text-primary-400',
      ghost: 'bg-transparent hover:bg-slate-100 focus-visible:ring-slate-500 dark:hover:bg-slate-800',
      link: 'bg-transparent text-primary-500 hover:underline hover:text-primary-600 focus-visible:ring-primary-500 p-0 dark:text-primary-400 dark:hover:text-primary-300',
      destructive: 'bg-red-500 text-white hover:bg-red-600 focus-visible:ring-red-500 shadow-md hover:shadow-lg dark:bg-red-600 dark:hover:bg-red-700',
    };

    const sizes = {
      sm: 'h-9 px-3 text-xs',
      md: 'h-10 px-4 text-sm',
      lg: 'h-11 px-6 text-base',
      icon: 'h-10 w-10 p-0 flex items-center justify-center',
    };

    // تحديد تأثير التحويم
    const getHoverEffectClass = () => {
      switch (hoverEffect) {
        case 'scale':
          return 'transform transition-transform hover:scale-105 active:scale-95';
        case 'glow':
          return 'hover:shadow-glow';
        case 'lift':
          return 'transform transition-all hover:-translate-y-1 hover:shadow-md active:translate-y-0 active:shadow-none';
        case 'none':
        default:
          return '';
      }
    };

    const classNames = cn(
      baseStyles,
      variants[variant],
      sizes[size],
      isLoading && 'opacity-70',
      getHoverEffectClass(),
      className
    );

    // تحديد محتوى الزر
    const buttonContent = (
      <>
        {isLoading ? (
          <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
        ) : leftIcon ? (
          <span className="mr-2 relative z-10">{leftIcon}</span>
        ) : null}

        <span className="relative z-10">{children}</span>

        {accessibilityLabel && (
          <VisuallyHidden>{accessibilityLabel}</VisuallyHidden>
        )}

        {!isLoading && rightIcon && (
          <span className="ml-2 relative z-10">{rightIcon}</span>
        )}

        {/* تأثير التحويم للأزرار الملونة - تم إصلاح مشكلة اختفاء النص */}
        {(variant === 'primary' || variant === 'secondary' || variant === 'accent' || variant === 'destructive') && (
          <span className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
        )}
      </>
    );

    // If 'as' prop is provided, render the component as that element type
    if (as === Link && to) {
      return (
        <motion.div whileTap={{ scale: 0.98 }}>
          <Link
            href={to}
            className={classNames}
          >
            {buttonContent}
          </Link>
        </motion.div>
      );
    }

    // Default to button
    return (
      <motion.div whileTap={{ scale: 0.98 }}>
        <button
          ref={ref}
          className={classNames}
          disabled={disabled || isLoading}
          {...props}
        >
          {buttonContent}
        </button>
      </motion.div>
    );
  }
);

Button.displayName = 'Button';

export { Button };