"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/services/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check-circle.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckCircle)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.344.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst CheckCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CheckCircle\", [\n    [\n        \"path\",\n        {\n            d: \"M22 11.08V12a10 10 0 1 1-5.93-9.14\",\n            key: \"g774vq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n]);\n //# sourceMappingURL=check-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Clock)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.344.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Clock\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"12 6 12 12 16 14\",\n            key: \"68esgv\"\n        }\n    ]\n]);\n //# sourceMappingURL=clock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2xvY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFhTSxjQUFRLGdFQUFnQixDQUFDLE9BQVM7SUFDdEM7UUFBQyxRQUFVO1FBQUE7WUFBRSxFQUFJO1lBQU0sQ0FBSSxRQUFNO1lBQUEsQ0FBRztZQUFNLEdBQUs7UUFBQSxDQUFVO0tBQUE7SUFDekQ7UUFBQyxVQUFZO1FBQUE7WUFBRSxRQUFRLENBQW9CO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtDQUMzRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzcmNcXGljb25zXFxjbG9jay50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENsb2NrXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThZMmx5WTJ4bElHTjRQU0l4TWlJZ1kzazlJakV5SWlCeVBTSXhNQ0lnTHo0S0lDQThjRzlzZVd4cGJtVWdjRzlwYm5SelBTSXhNaUEySURFeUlERXlJREUySURFMElpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2Nsb2NrXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2xvY2sgPSBjcmVhdGVMdWNpZGVJY29uKCdDbG9jaycsIFtcbiAgWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzEyJywgcjogJzEwJywga2V5OiAnMW1nbGF5JyB9XSxcbiAgWydwb2x5bGluZScsIHsgcG9pbnRzOiAnMTIgNiAxMiAxMiAxNiAxNCcsIGtleTogJzY4ZXNndicgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2xvY2s7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/star.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Star)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.344.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Star = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Star\", [\n    [\n        \"polygon\",\n        {\n            points: \"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\",\n            key: \"8f66p6\"\n        }\n    ]\n]);\n //# sourceMappingURL=star.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/pages/services/ServicesPage.tsx":
/*!*********************************************!*\
  !*** ./src/pages/services/ServicesPage.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServicesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,MapPin,Package,Phone,Search,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_forms_ServiceBookingForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/forms/ServiceBookingForm */ \"(app-pages-browser)/./src/components/forms/ServiceBookingForm.tsx\");\n/* harmony import */ var _data_services__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../data/services */ \"(app-pages-browser)/./src/data/services.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../stores/themeStore */ \"(app-pages-browser)/./src/stores/themeStore.ts\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _translations__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../translations */ \"(app-pages-browser)/./src/translations/index.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_animations__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../components/ui/animations */ \"(app-pages-browser)/./src/components/ui/animations/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst icons = {\n    Search: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    Package: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    Truck: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    FileCheck: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    Users: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    ClipboardList: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n};\nfunction ServicesPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [showBookingForm, setShowBookingForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedService, setSelectedService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        searchQuery: '',\n        category: 'all',\n        sortBy: 'name'\n    });\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_7__.useThemeStore)();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_8__.useLanguageStore)();\n    const { t, locale } = (0,_translations__WEBPACK_IMPORTED_MODULE_9__.useTranslation)();\n    // استخدام اللغة من المسار أو من المتجر\n    const currentLanguage = locale || language;\n    // Service categories for filtering\n    const categories = [\n        {\n            id: 'all',\n            name: currentLanguage === 'ar' ? 'جميع الخدمات' : 'All Services'\n        },\n        {\n            id: 'inspection',\n            name: currentLanguage === 'ar' ? 'خدمات الفحص' : 'Inspection Services'\n        },\n        {\n            id: 'logistics',\n            name: currentLanguage === 'ar' ? 'الخدمات اللوجستية' : 'Logistics Services'\n        },\n        {\n            id: 'consulting',\n            name: currentLanguage === 'ar' ? 'الاستشارات' : 'Consulting Services'\n        },\n        {\n            id: 'certification',\n            name: currentLanguage === 'ar' ? 'الشهادات' : 'Certification Services'\n        }\n    ];\n    // Filter and sort services\n    const filteredServices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ServicesPage.useMemo[filteredServices]\": ()=>{\n            let filtered = _data_services__WEBPACK_IMPORTED_MODULE_6__.services.filter({\n                \"ServicesPage.useMemo[filteredServices].filtered\": (service)=>{\n                    const searchTerm = filters.searchQuery.toLowerCase();\n                    const serviceName = currentLanguage === 'ar' ? service.name_ar || service.name : service.name;\n                    const serviceDesc = currentLanguage === 'ar' ? service.description_ar || service.description : service.description;\n                    const matchesSearch = !searchTerm || serviceName.toLowerCase().includes(searchTerm) || serviceDesc.toLowerCase().includes(searchTerm);\n                    const matchesCategory = filters.category === 'all' || service.id.includes(filters.category) || filters.category === 'logistics' && [\n                        'shipping',\n                        'storage'\n                    ].includes(service.id) || filters.category === 'inspection' && service.id === 'inspection' || filters.category === 'consulting' && [\n                        'consulting',\n                        'order-management'\n                    ].includes(service.id) || filters.category === 'certification' && service.id === 'certification';\n                    return matchesSearch && matchesCategory;\n                }\n            }[\"ServicesPage.useMemo[filteredServices].filtered\"]);\n            // Sort services\n            filtered.sort({\n                \"ServicesPage.useMemo[filteredServices]\": (a, b)=>{\n                    const aName = currentLanguage === 'ar' ? a.name_ar || a.name : a.name;\n                    const bName = currentLanguage === 'ar' ? b.name_ar || b.name : b.name;\n                    switch(filters.sortBy){\n                        case 'name':\n                            return aName.localeCompare(bName);\n                        case 'popularity':\n                            // Mock popularity sorting - in real app, this would be based on actual data\n                            return Math.random() - 0.5;\n                        case 'recent':\n                            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                        default:\n                            return 0;\n                    }\n                }\n            }[\"ServicesPage.useMemo[filteredServices]\"]);\n            return filtered;\n        }\n    }[\"ServicesPage.useMemo[filteredServices]\"], [\n        _data_services__WEBPACK_IMPORTED_MODULE_6__.services,\n        filters,\n        currentLanguage\n    ]);\n    const handleBookService = (serviceName)=>{\n        setSelectedService(serviceName);\n        setShowBookingForm(true);\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            searchQuery: '',\n            category: 'all',\n            sortBy: 'name'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"relative py-16 overflow-hidden transition-colors duration-500\", isDarkMode ? \"bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900\" : \"bg-gradient-to-br from-slate-50 via-white to-slate-100\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"absolute inset-0 opacity-5 transition-opacity duration-500\", isDarkMode ? \"opacity-10\" : \"opacity-5\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:50px_50px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"text-4xl md:text-5xl font-bold leading-tight tracking-tight mb-6\", isDarkMode ? \"text-white\" : \"text-slate-900\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block\",\n                                                children: currentLanguage === 'ar' ? 'خدمات' : 'Business'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"block bg-gradient-to-r bg-clip-text text-transparent\", isDarkMode ? \"from-primary-400 to-blue-400\" : \"from-primary-600 to-blue-600\"),\n                                                children: currentLanguage === 'ar' ? 'الأعمال' : 'Services'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"text-lg md:text-xl leading-relaxed max-w-3xl mx-auto mb-8\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\"),\n                                        children: currentLanguage === 'ar' ? 'خدمات دعم شاملة لتبسيط عملياتك وتعزيز كفاءة أعمالك مع حلول متطورة ومخصصة.' : 'Comprehensive support services to streamline your operations and enhance business efficiency with advanced, tailored solutions.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.ScrollStagger, {\n                                            animation: \"slide\",\n                                            direction: \"up\",\n                                            staggerDelay: 0.1,\n                                            children: [\n                                                {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    value: '6+',\n                                                    label: currentLanguage === 'ar' ? 'خدمة متخصصة' : 'Expert Services'\n                                                },\n                                                {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    value: '500+',\n                                                    label: currentLanguage === 'ar' ? 'عميل راضي' : 'Satisfied Clients'\n                                                },\n                                                {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    value: '24/48h',\n                                                    label: currentLanguage === 'ar' ? 'وقت الاستجابة' : 'Response Time'\n                                                },\n                                                {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    value: '50+',\n                                                    label: currentLanguage === 'ar' ? 'دولة' : 'Countries'\n                                                }\n                                            ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"p-4 rounded-lg transition-colors duration-300\", isDarkMode ? \"bg-slate-800/50 hover:bg-slate-800/70\" : \"bg-white/50 hover:bg-white/70\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"flex items-center justify-center w-8 h-8 rounded-full mx-auto mb-2\", isDarkMode ? \"bg-primary-500/20 text-primary-400\" : \"bg-primary-100 text-primary-600\"),\n                                                            children: stat.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"text-lg font-bold mb-1\", isDarkMode ? \"text-white\" : \"text-slate-900\"),\n                                                            children: stat.value\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"text-xs font-medium\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\"),\n                                                            children: stat.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"primary\",\n                                                    className: \"px-8 py-3 text-lg font-semibold group\",\n                                                    onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/contact\")),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5 group-hover:scale-110 transition-transform\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'استشارة مجانية' : 'Free Consultation'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"outline\",\n                                                    className: \"px-8 py-3 text-lg font-semibold group\",\n                                                    onClick: ()=>{\n                                                        var _document_getElementById;\n                                                        return (_document_getElementById = document.getElementById('services-grid')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                            behavior: 'smooth'\n                                                        });\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2', \" h-5 w-5 group-hover:translate-x-1 transition-transform\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'استكشف الخدمات' : 'Explore Services'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"py-16 md:py-24\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-3xl mx-auto text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\",\n                                        children: currentLanguage === 'ar' ? 'عروض خدماتنا' : 'Our Service Offerings'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-slate-600 dark:text-slate-300\",\n                                        children: currentLanguage === 'ar' ? 'اكتشف مجموعة كاملة من خدمات الأعمال المصممة لدعم عملياتك في كل مرحلة.' : 'Discover the full range of business services designed to support your operations at every stage.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.ScrollStagger, {\n                            animation: \"slide\",\n                            direction: \"up\",\n                            staggerDelay: 0.1,\n                            delay: 0.4,\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: _data_services__WEBPACK_IMPORTED_MODULE_6__.services.map((service)=>{\n                                const Icon = icons[service.icon];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.HoverAnimation, {\n                                    animation: \"lift\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"group hover:shadow-md transition-shadow duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            size: 40,\n                                                            className: \"text-primary-500 dark:text-primary-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-xl mb-2 text-slate-900 dark:text-white\",\n                                                        children: currentLanguage === 'ar' ? service.name_ar || service.name : service.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600 dark:text-slate-300 mb-6\",\n                                                        children: currentLanguage === 'ar' ? service.description_ar || service.description : service.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: ()=>handleBookService(currentLanguage === 'ar' ? service.name_ar || service.name : service.name),\n                                                                    className: \"flex-1\",\n                                                                    children: currentLanguage === 'ar' ? 'احجز الخدمة' : 'Book Service'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/services/\").concat(service.slug)),\n                                                                    variant: \"outline\",\n                                                                    className: \"flex-1 group-hover:bg-primary-50 group-hover:border-primary-300 transition-colors\",\n                                                                    children: [\n                                                                        currentLanguage === 'ar' ? 'معرفة المزيد' : 'Learn More',\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_MapPin_Package_Phone_Search_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"\".concat(currentLanguage === 'ar' ? 'mr-2' : 'ml-2', \" h-4 w-4 transition-transform group-hover:translate-x-1\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                            lineNumber: 320,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 19\n                                    }, this)\n                                }, service.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"py-16 md:py-24\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-50\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-3xl mx-auto text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\",\n                                        children: currentLanguage === 'ar' ? 'كيف نعمل' : 'How We Work'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-slate-600 dark:text-slate-300\",\n                                        children: currentLanguage === 'ar' ? 'نهجنا الاستشاري يضمن حلولاً مخصصة لاحتياجات عملك الفريدة.' : 'Our consultative approach ensures tailored solutions for your unique business needs.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.ScrollStagger, {\n                            animation: \"slide\",\n                            direction: \"right\",\n                            staggerDelay: 0.15,\n                            delay: 0.4,\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                {\n                                    number: \"01\",\n                                    title: currentLanguage === 'ar' ? \"الاستشارة\" : \"Consultation\",\n                                    description: currentLanguage === 'ar' ? \"نبدأ باستشارة شاملة لفهم احتياجات وتحديات عملك.\" : \"We begin with a thorough consultation to understand your business needs and challenges.\"\n                                },\n                                {\n                                    number: \"02\",\n                                    title: currentLanguage === 'ar' ? \"التحليل\" : \"Analysis\",\n                                    description: currentLanguage === 'ar' ? \"يقوم خبراؤنا بتحليل متطلباتك وتطوير توصيات خدمة مخصصة.\" : \"Our experts analyze your requirements and develop customized service recommendations.\"\n                                },\n                                {\n                                    number: \"03\",\n                                    title: currentLanguage === 'ar' ? \"التنفيذ\" : \"Implementation\",\n                                    description: currentLanguage === 'ar' ? \"نقوم بتنفيذ الخدمات المتفق عليها مع الاهتمام بالتفاصيل وضمان الجودة.\" : \"We implement the agreed services with attention to detail and quality assurance.\"\n                                },\n                                {\n                                    number: \"04\",\n                                    title: currentLanguage === 'ar' ? \"الدعم المستمر\" : \"Ongoing Support\",\n                                    description: currentLanguage === 'ar' ? \"المراقبة والدعم المستمر يضمنان النتائج المثلى والقدرة على التكيف.\" : \"Continuous monitoring and support ensure optimal results and adaptability.\"\n                                }\n                            ].map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.HoverAnimation, {\n                                    animation: \"lift\",\n                                    className: \"relative text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-500 text-white rounded-full h-12 w-12 flex items-center justify-center font-bold text-lg mb-4 mx-auto\",\n                                            children: step.number\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"p-6 rounded-lg shadow-sm h-full\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-2 text-slate-900 dark:text-white\",\n                                                    children: step.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 dark:text-slate-300\",\n                                                    children: step.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 md:py-24 bg-primary-500 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.ScrollAnimation, {\n                        animation: \"fade\",\n                        delay: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-3xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-6\",\n                                    children: currentLanguage === 'ar' ? 'هل أنت مستعد لتعزيز عمليات عملك؟' : 'Ready to Enhance Your Business Operations?'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl mb-8 text-primary-50\",\n                                    children: currentLanguage === 'ar' ? 'اتصل بفريقنا لمناقشة كيف يمكن لخدماتنا تلبية احتياجات عملك المحددة.' : 'Contact our team to discuss how our services can address your specific business needs.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.HoverAnimation, {\n                                        animation: \"scale\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/services/request\")),\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"bg-transparent border-white text-white hover:bg-primary-600 px-8\",\n                                            children: currentLanguage === 'ar' ? 'طلب عرض سعر للخدمة' : 'Request Service Quote'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"py-16 md:py-24\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-3xl mx-auto text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\",\n                                        children: currentLanguage === 'ar' ? 'الأسئلة الشائعة' : 'Frequently Asked Questions'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-slate-600 dark:text-slate-300\",\n                                        children: currentLanguage === 'ar' ? 'ابحث عن إجابات للأسئلة الشائعة حول خدمات أعمالنا.' : 'Find answers to common questions about our business services.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.ScrollStagger, {\n                            animation: \"fade\",\n                            staggerDelay: 0.1,\n                            delay: 0.4,\n                            className: \"max-w-4xl mx-auto space-y-6\",\n                            children: [\n                                {\n                                    question: currentLanguage === 'ar' ? \"ما هي أنواع الشركات التي تخدمونها؟\" : \"What types of businesses do you serve?\",\n                                    answer: currentLanguage === 'ar' ? \"نخدم مجموعة واسعة من الشركات عبر مختلف الصناعات، بما في ذلك التصنيع والتجزئة والتوزيع والتجارة الإلكترونية. خدماتنا قابلة للتطوير ويمكن تخصيصها لتلبية احتياجات كل من الشركات الصغيرة والمؤسسات الكبيرة.\" : \"We serve a wide range of businesses across various industries, including manufacturing, retail, distribution, and e-commerce. Our services are scalable and can be customized to meet the needs of both small businesses and large enterprises.\"\n                                },\n                                {\n                                    question: currentLanguage === 'ar' ? \"ما هي سرعة ترتيب خدمات الفحص؟\" : \"How quickly can you arrange inspection services?\",\n                                    answer: currentLanguage === 'ar' ? \"يمكن ترتيب خدمات الفحص القياسية في غضون 48-72 ساعة من الطلب. للاحتياجات العاجلة، نقدم خدمات معجلة يمكن جدولتها في غضون 24 ساعة في معظم المواقع، حسب التوفر. تضمن شبكتنا العالمية من المفتشين أوقات استجابة سريعة.\" : \"Standard inspection services can be arranged within 48-72 hours of request. For urgent needs, we offer expedited services that can be scheduled within 24 hours in most locations, subject to availability. Our global network of inspectors ensures quick response times.\"\n                                },\n                                {\n                                    question: currentLanguage === 'ar' ? \"هل تقدمون خدمات دولية؟\" : \"Do you provide services internationally?\",\n                                    answer: currentLanguage === 'ar' ? \"نعم، نقدم خدمات أعمالنا عالميًا من خلال شبكتنا الواسعة من المكاتب والشركاء في مراكز التصنيع والخدمات اللوجستية الرئيسية عبر آسيا وأوروبا والأمريكتين. يمكننا تنسيق الخدمات عبر العديد من البلدان والمناطق.\" : \"Yes, we offer our business services globally through our extensive network of offices and partners in major manufacturing and logistics hubs across Asia, Europe, and the Americas. We can coordinate services across multiple countries and regions.\"\n                                },\n                                {\n                                    question: \"What certifications and standards do you comply with?\",\n                                    answer: \"Our services comply with international standards including ISO 9001, ISO 14001, and industry-specific certifications. For product certification services, we work with all major certification bodies and can assist with CE, FCC, UL, and other market-specific requirements.\"\n                                },\n                                {\n                                    question: \"How do you ensure service quality and consistency?\",\n                                    answer: \"We maintain strict quality control processes, including regular training for our staff, standardized operating procedures, and continuous monitoring of service delivery. Our quality management system ensures consistent service delivery across all locations.\"\n                                },\n                                {\n                                    question: \"What are your payment terms and methods?\",\n                                    answer: \"We accept various payment methods including bank transfers, credit cards, and digital payments. For regular clients, we offer flexible payment terms including net-30 accounts. Volume-based discounts and service packages are available for long-term contracts.\"\n                                },\n                                {\n                                    question: \"Can you handle rush orders or emergency situations?\",\n                                    answer: \"Yes, we have dedicated teams to handle urgent requests and emergency situations. We offer expedited services across all our service lines with priority handling and 24/7 support for critical situations.\"\n                                },\n                                {\n                                    question: \"Do you provide customized service packages?\",\n                                    answer: \"Yes, we can create customized service packages that combine multiple services to meet your specific business needs. Our solutions architects will work with you to design the most efficient and cost-effective service package.\"\n                                },\n                                {\n                                    question: \"What kind of reporting and documentation do you provide?\",\n                                    answer: \"We provide detailed digital reports for all services, including inspection findings, certification status, shipping documentation, and performance analytics. Our clients have access to a secure portal for real-time tracking and report downloads.\"\n                                },\n                                {\n                                    question: \"How do you handle confidential information?\",\n                                    answer: \"We maintain strict confidentiality protocols and are compliant with GDPR and other data protection regulations. All client information is stored securely, and our staff signs comprehensive NDAs.\"\n                                }\n                            ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.HoverAnimation, {\n                                    animation: \"lift\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"group rounded-lg overflow-hidden\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"flex items-center justify-between p-6 cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-medium text-slate-900 dark:text-white pr-8\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"flex-shrink-0 ml-1.5 p-1.5 rounded-full\", isDarkMode ? \"text-slate-300 bg-slate-700\" : \"text-slate-700 bg-white\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5 transform group-open:rotate-180 transition-transform duration-200\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: \"2\",\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 pb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 dark:text-slate-300\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 431,\n                columnNumber: 7\n            }, this),\n            showBookingForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_11__.ScrollAnimation, {\n                    animation: \"scale\",\n                    duration: 0.3,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-2xl w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_ServiceBookingForm__WEBPACK_IMPORTED_MODULE_5__.ServiceBookingForm, {\n                            serviceName: selectedService,\n                            onClose: ()=>{\n                                setShowBookingForm(false);\n                                setSelectedService(undefined);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                    lineNumber: 536,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n                lineNumber: 535,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPage.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(ServicesPage, \"X3wwz2DGKpEpblgQnkfiwH7CVSg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _stores_themeStore__WEBPACK_IMPORTED_MODULE_7__.useThemeStore,\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_8__.useLanguageStore,\n        _translations__WEBPACK_IMPORTED_MODULE_9__.useTranslation\n    ];\n});\n_c = ServicesPage;\nvar _c;\n$RefreshReg$(_c, \"ServicesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9wYWdlcy9zZXJ2aWNlcy9TZXJ2aWNlc1BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMEM7QUFFRTtBQWlCdEI7QUFDOEI7QUFDZ0M7QUFFTDtBQUNoQztBQUNTO0FBQ007QUFDVjtBQUNmO0FBQzJEO0FBRWhHLE1BQU02QixRQUFRO0lBQ1p6QixNQUFNQSxnTEFBQUE7SUFDTkMsT0FBT0EsZ0xBQUFBO0lBQ1BDLEtBQUtBLGdMQUFBQTtJQUNMQyxTQUFTQSxnTEFBQUE7SUFDVEMsS0FBS0EsZ0xBQUFBO0lBQ0xDLGFBQWFBLGdMQUFBQTtBQUNmO0FBUWUsU0FBU3FCOztJQUN0QixNQUFNQyxTQUFTN0IsMERBQVNBO0lBQ3hCLE1BQU0sQ0FBQzhCLGlCQUFpQkMsbUJBQW1CLEdBQUdqQywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNrQyxpQkFBaUJDLG1CQUFtQixHQUFHbkMsK0NBQVFBO0lBQ3RELE1BQU0sQ0FBQ29DLGFBQWFDLGVBQWUsR0FBR3JDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3NDLFNBQVNDLFdBQVcsR0FBR3ZDLCtDQUFRQSxDQUFpQjtRQUNyRHdDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxRQUFRO0lBQ1Y7SUFDQSxNQUFNLEVBQUVDLFVBQVUsRUFBRSxHQUFHckIsaUVBQWFBO0lBQ3BDLE1BQU0sRUFBRXNCLFFBQVEsRUFBRSxHQUFHckIsdUVBQWdCQTtJQUNyQyxNQUFNLEVBQUVzQixDQUFDLEVBQUVDLE1BQU0sRUFBRSxHQUFHdEIsNkRBQWNBO0lBRXBDLHVDQUF1QztJQUN2QyxNQUFNdUIsa0JBQWtCLFVBQTJCSDtJQUVuRCxtQ0FBbUM7SUFDbkMsTUFBTUksYUFBYTtRQUNqQjtZQUFFQyxJQUFJO1lBQU9DLE1BQU1ILG9CQUFvQixPQUFPLGlCQUFpQjtRQUFlO1FBQzlFO1lBQUVFLElBQUk7WUFBY0MsTUFBTUgsb0JBQW9CLE9BQU8sZ0JBQWdCO1FBQXNCO1FBQzNGO1lBQUVFLElBQUk7WUFBYUMsTUFBTUgsb0JBQW9CLE9BQU8sc0JBQXNCO1FBQXFCO1FBQy9GO1lBQUVFLElBQUk7WUFBY0MsTUFBTUgsb0JBQW9CLE9BQU8sZUFBZTtRQUFzQjtRQUMxRjtZQUFFRSxJQUFJO1lBQWlCQyxNQUFNSCxvQkFBb0IsT0FBTyxhQUFhO1FBQXlCO0tBQy9GO0lBRUQsMkJBQTJCO0lBQzNCLE1BQU1JLG1CQUFtQmxELDhDQUFPQTtrREFBQztZQUMvQixJQUFJbUQsV0FBVy9CLG9EQUFRQSxDQUFDZ0MsTUFBTTttRUFBQ0MsQ0FBQUE7b0JBQzdCLE1BQU1DLGFBQWFqQixRQUFRRSxXQUFXLENBQUNnQixXQUFXO29CQUNsRCxNQUFNQyxjQUFjVixvQkFBb0IsT0FBT08sUUFBUUksT0FBTyxJQUFJSixRQUFRSixJQUFJLEdBQUdJLFFBQVFKLElBQUk7b0JBQzdGLE1BQU1TLGNBQWNaLG9CQUFvQixPQUFPTyxRQUFRTSxjQUFjLElBQUlOLFFBQVFPLFdBQVcsR0FBR1AsUUFBUU8sV0FBVztvQkFFbEgsTUFBTUMsZ0JBQWdCLENBQUNQLGNBQ3JCRSxZQUFZRCxXQUFXLEdBQUdPLFFBQVEsQ0FBQ1IsZUFDbkNJLFlBQVlILFdBQVcsR0FBR08sUUFBUSxDQUFDUjtvQkFFckMsTUFBTVMsa0JBQWtCMUIsUUFBUUcsUUFBUSxLQUFLLFNBQzNDYSxRQUFRTCxFQUFFLENBQUNjLFFBQVEsQ0FBQ3pCLFFBQVFHLFFBQVEsS0FDbkNILFFBQVFHLFFBQVEsS0FBSyxlQUFlO3dCQUFDO3dCQUFZO3FCQUFVLENBQUNzQixRQUFRLENBQUNULFFBQVFMLEVBQUUsS0FDL0VYLFFBQVFHLFFBQVEsS0FBSyxnQkFBZ0JhLFFBQVFMLEVBQUUsS0FBSyxnQkFDcERYLFFBQVFHLFFBQVEsS0FBSyxnQkFBZ0I7d0JBQUM7d0JBQWM7cUJBQW1CLENBQUNzQixRQUFRLENBQUNULFFBQVFMLEVBQUUsS0FDM0ZYLFFBQVFHLFFBQVEsS0FBSyxtQkFBbUJhLFFBQVFMLEVBQUUsS0FBSztvQkFFMUQsT0FBT2EsaUJBQWlCRTtnQkFDMUI7O1lBRUEsZ0JBQWdCO1lBQ2hCWixTQUFTYSxJQUFJOzBEQUFDLENBQUNDLEdBQUdDO29CQUNoQixNQUFNQyxRQUFRckIsb0JBQW9CLE9BQU9tQixFQUFFUixPQUFPLElBQUlRLEVBQUVoQixJQUFJLEdBQUdnQixFQUFFaEIsSUFBSTtvQkFDckUsTUFBTW1CLFFBQVF0QixvQkFBb0IsT0FBT29CLEVBQUVULE9BQU8sSUFBSVMsRUFBRWpCLElBQUksR0FBR2lCLEVBQUVqQixJQUFJO29CQUVyRSxPQUFRWixRQUFRSSxNQUFNO3dCQUNwQixLQUFLOzRCQUNILE9BQU8wQixNQUFNRSxhQUFhLENBQUNEO3dCQUM3QixLQUFLOzRCQUNILDRFQUE0RTs0QkFDNUUsT0FBT0UsS0FBS0MsTUFBTSxLQUFLO3dCQUN6QixLQUFLOzRCQUNILE9BQU8sSUFBSUMsS0FBS04sRUFBRU8sU0FBUyxFQUFFQyxPQUFPLEtBQUssSUFBSUYsS0FBS1AsRUFBRVEsU0FBUyxFQUFFQyxPQUFPO3dCQUN4RTs0QkFDRSxPQUFPO29CQUNYO2dCQUNGOztZQUVBLE9BQU92QjtRQUNUO2lEQUFHO1FBQUMvQixvREFBUUE7UUFBRWlCO1FBQVNTO0tBQWdCO0lBRXZDLE1BQU02QixvQkFBb0IsQ0FBQ25CO1FBQ3pCdEIsbUJBQW1Cc0I7UUFDbkJ4QixtQkFBbUI7SUFDckI7SUFFQSxNQUFNNEMsZUFBZTtRQUNuQnRDLFdBQVc7WUFDVEMsYUFBYTtZQUNiQyxVQUFVO1lBQ1ZDLFFBQVE7UUFDVjtJQUNGO0lBRUEscUJBQ0UsOERBQUNvQzs7MEJBRUMsOERBQUNDO2dCQUFRQyxXQUFXdkQsK0NBQUVBLENBQ3BCLGlFQUNBa0IsYUFDSSxnRUFDQTs7a0NBR0osOERBQUNtQzt3QkFBSUUsV0FBVTtrQ0FDYiw0RUFBQ0Y7NEJBQUlFLFdBQVd2RCwrQ0FBRUEsQ0FDaEIsOERBQ0FrQixhQUFhLGVBQWU7c0NBRTVCLDRFQUFDbUM7Z0NBQUlFLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSW5CLDhEQUFDRjt3QkFBSUUsV0FBVTtrQ0FDYiw0RUFBQ3RELHVFQUFlQTs0QkFBQ3VELFdBQVU7NEJBQU9DLE9BQU87c0NBQ3ZDLDRFQUFDSjtnQ0FBSUUsV0FBVTs7a0RBRWIsOERBQUNHO3dDQUFHSCxXQUFXdkQsK0NBQUVBLENBQ2Ysb0VBQ0FrQixhQUFhLGVBQWU7OzBEQUU1Qiw4REFBQ3lDO2dEQUFLSixXQUFVOzBEQUNiakMsb0JBQW9CLE9BQU8sVUFBVTs7Ozs7OzBEQUV4Qyw4REFBQ3FDO2dEQUFLSixXQUFXdkQsK0NBQUVBLENBQ2pCLHdEQUNBa0IsYUFDSSxpQ0FDQTswREFFSEksb0JBQW9CLE9BQU8sWUFBWTs7Ozs7Ozs7Ozs7O2tEQUs1Qyw4REFBQ3NDO3dDQUFFTCxXQUFXdkQsK0NBQUVBLENBQ2QsNkRBQ0FrQixhQUFhLG1CQUFtQjtrREFFL0JJLG9CQUFvQixPQUNqQiw4RUFDQTs7Ozs7O2tEQUlOLDhEQUFDK0I7d0NBQUlFLFdBQVU7a0RBQ2IsNEVBQUNyRCxxRUFBYUE7NENBQUNzRCxXQUFVOzRDQUFRSyxXQUFVOzRDQUFLQyxjQUFjO3NEQUMzRDtnREFDQztvREFDRUMsb0JBQU0sOERBQUM1RSw4S0FBV0E7d0RBQUNvRSxXQUFVOzs7Ozs7b0RBQzdCUyxPQUFPO29EQUNQQyxPQUFPM0Msb0JBQW9CLE9BQU8sZ0JBQWdCO2dEQUNwRDtnREFDQTtvREFDRXlDLG9CQUFNLDhEQUFDOUUsOEtBQUlBO3dEQUFDc0UsV0FBVTs7Ozs7O29EQUN0QlMsT0FBTztvREFDUEMsT0FBTzNDLG9CQUFvQixPQUFPLGNBQWM7Z0RBQ2xEO2dEQUNBO29EQUNFeUMsb0JBQU0sOERBQUM3RSw4S0FBS0E7d0RBQUNxRSxXQUFVOzs7Ozs7b0RBQ3ZCUyxPQUFPO29EQUNQQyxPQUFPM0Msb0JBQW9CLE9BQU8sa0JBQWtCO2dEQUN0RDtnREFDQTtvREFDRXlDLG9CQUFNLDhEQUFDMUUsOEtBQU1BO3dEQUFDa0UsV0FBVTs7Ozs7O29EQUN4QlMsT0FBTztvREFDUEMsT0FBTzNDLG9CQUFvQixPQUFPLFNBQVM7Z0RBQzdDOzZDQUNELENBQUM0QyxHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQ1gsOERBQUNmO29EQUFnQkUsV0FBV3ZELCtDQUFFQSxDQUM1QixpREFDQWtCLGFBQ0ksMENBQ0E7O3NFQUVKLDhEQUFDbUM7NERBQUlFLFdBQVd2RCwrQ0FBRUEsQ0FDaEIsc0VBQ0FrQixhQUFhLHVDQUF1QztzRUFFbkRpRCxLQUFLSixJQUFJOzs7Ozs7c0VBRVosOERBQUNWOzREQUFJRSxXQUFXdkQsK0NBQUVBLENBQ2hCLDBCQUNBa0IsYUFBYSxlQUFlO3NFQUUzQmlELEtBQUtILEtBQUs7Ozs7OztzRUFFYiw4REFBQ1g7NERBQUlFLFdBQVd2RCwrQ0FBRUEsQ0FDaEIsdUJBQ0FrQixhQUFhLG1CQUFtQjtzRUFFL0JpRCxLQUFLRixLQUFLOzs7Ozs7O21EQXRCTEc7Ozs7Ozs7Ozs7Ozs7OztrREE4QmhCLDhEQUFDZjt3Q0FBSUUsV0FBVTs7MERBQ2IsOERBQUNwRCxzRUFBY0E7Z0RBQUNxRCxXQUFVOzBEQUN4Qiw0RUFBQ2xFLHlEQUFNQTtvREFDTCtFLE1BQUs7b0RBQ0xDLFNBQVE7b0RBQ1JmLFdBQVU7b0RBQ1ZnQixTQUFTLElBQU1qRSxPQUFPa0UsSUFBSSxDQUFDLElBQW9CLE9BQWhCbEQsaUJBQWdCOztzRUFFL0MsOERBQUNsQyw4S0FBS0E7NERBQUNtRSxXQUFXLEdBQThDLE9BQTNDakMsb0JBQW9CLE9BQU8sU0FBUyxRQUFPOzs7Ozs7d0RBQy9EQSxvQkFBb0IsT0FBTyxtQkFBbUI7Ozs7Ozs7Ozs7OzswREFHbkQsOERBQUNuQixzRUFBY0E7Z0RBQUNxRCxXQUFVOzBEQUN4Qiw0RUFBQ2xFLHlEQUFNQTtvREFDTCtFLE1BQUs7b0RBQ0xDLFNBQVE7b0RBQ1JmLFdBQVU7b0RBQ1ZnQixTQUFTOzREQUFNRTtnRUFBQUEsMkJBQUFBLFNBQVNDLGNBQWMsQ0FBQyw4QkFBeEJELCtDQUFBQSx5QkFBMENFLGNBQWMsQ0FBQzs0REFBRUMsVUFBVTt3REFBUzs7O3NFQUU3Riw4REFBQ2xHLDhLQUFVQTs0REFBQzZFLFdBQVcsR0FBeUQsT0FBdERqQyxvQkFBb0IsT0FBTyxvQkFBb0IsUUFBTzs7Ozs7O3dEQUMvRUEsb0JBQW9CLE9BQU8sbUJBQW1COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVU3RCw4REFBQ2dDO2dCQUFRQyxXQUFXdkQsK0NBQUVBLENBQUMsa0JBQWtCa0IsYUFBYSxpQkFBaUI7MEJBQ3JFLDRFQUFDbUM7b0JBQUlFLFdBQVU7O3NDQUNiLDhEQUFDdEQsdUVBQWVBOzRCQUFDdUQsV0FBVTs0QkFBT0MsT0FBTztzQ0FDdkMsNEVBQUNKO2dDQUFJRSxXQUFVOztrREFDYiw4REFBQ3NCO3dDQUFHdEIsV0FBVTtrREFDWGpDLG9CQUFvQixPQUFPLGlCQUFpQjs7Ozs7O2tEQUUvQyw4REFBQ3NDO3dDQUFFTCxXQUFVO2tEQUNWakMsb0JBQW9CLE9BQ2pCLDBFQUNBOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLViw4REFBQ3BCLHFFQUFhQTs0QkFDWnNELFdBQVU7NEJBQ1ZLLFdBQVU7NEJBQ1ZDLGNBQWM7NEJBQ2RMLE9BQU87NEJBQ1BGLFdBQVU7c0NBRVQzRCxvREFBUUEsQ0FBQ3NFLEdBQUcsQ0FBQyxDQUFDckM7Z0NBQ2IsTUFBTWlELE9BQU8xRSxLQUFLLENBQUN5QixRQUFRa0MsSUFBSSxDQUF1QjtnQ0FDdEQscUJBQ0UsOERBQUM1RCxzRUFBY0E7b0NBQWtCcUQsV0FBVTs4Q0FDekMsNEVBQUNqRSxxREFBSUE7d0NBQUNnRSxXQUFVOzswREFDZCw4REFBQzlELDJEQUFVQTtnREFBQzhELFdBQVU7O2tFQUNwQiw4REFBQ0Y7d0RBQUlFLFdBQVU7a0VBQ2IsNEVBQUN1Qjs0REFBS1QsTUFBTTs0REFBSWQsV0FBVTs7Ozs7Ozs7Ozs7a0VBRTVCLDhEQUFDN0QsMERBQVNBO3dEQUFDNkQsV0FBVTtrRUFDbEJqQyxvQkFBb0IsT0FBT08sUUFBUUksT0FBTyxJQUFJSixRQUFRSixJQUFJLEdBQUdJLFFBQVFKLElBQUk7Ozs7Ozs7Ozs7OzswREFHOUUsOERBQUNqQyw0REFBV0E7O2tFQUNWLDhEQUFDb0U7d0RBQUVMLFdBQVU7a0VBQ1ZqQyxvQkFBb0IsT0FBT08sUUFBUU0sY0FBYyxJQUFJTixRQUFRTyxXQUFXLEdBQUdQLFFBQVFPLFdBQVc7Ozs7OztrRUFFakcsOERBQUNpQjt3REFBSUUsV0FBVTs7MEVBQ2IsOERBQUNwRCxzRUFBY0E7Z0VBQUNxRCxXQUFVOzBFQUN4Qiw0RUFBQ2xFLHlEQUFNQTtvRUFDTGlGLFNBQVMsSUFBTXBCLGtCQUFrQjdCLG9CQUFvQixPQUFPTyxRQUFRSSxPQUFPLElBQUlKLFFBQVFKLElBQUksR0FBR0ksUUFBUUosSUFBSTtvRUFDMUc4QixXQUFVOzhFQUVUakMsb0JBQW9CLE9BQU8sZ0JBQWdCOzs7Ozs7Ozs7OzswRUFHaEQsOERBQUNuQixzRUFBY0E7Z0VBQUNxRCxXQUFVOzBFQUN4Qiw0RUFBQ2xFLHlEQUFNQTtvRUFDTGlGLFNBQVMsSUFBTWpFLE9BQU9rRSxJQUFJLENBQUMsSUFBZ0MzQyxPQUE1QlAsaUJBQWdCLGNBQXlCLE9BQWJPLFFBQVFrRCxJQUFJO29FQUN2RVQsU0FBUTtvRUFDUmYsV0FBVTs7d0VBRVRqQyxvQkFBb0IsT0FBTyxpQkFBaUI7c0ZBQzdDLDhEQUFDNUMsOEtBQVVBOzRFQUFDNkUsV0FBVyxHQUE4QyxPQUEzQ2pDLG9CQUFvQixPQUFPLFNBQVMsUUFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBOUI1RE8sUUFBUUwsRUFBRTs7Ozs7NEJBc0NuQzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTU4sOERBQUM4QjtnQkFBUUMsV0FBV3ZELCtDQUFFQSxDQUFDLGtCQUFrQmtCLGFBQWEsaUJBQWlCOzBCQUNyRSw0RUFBQ21DO29CQUFJRSxXQUFVOztzQ0FDYiw4REFBQ3RELHVFQUFlQTs0QkFBQ3VELFdBQVU7NEJBQU9DLE9BQU87c0NBQ3ZDLDRFQUFDSjtnQ0FBSUUsV0FBVTs7a0RBQ2IsOERBQUNzQjt3Q0FBR3RCLFdBQVU7a0RBQ1hqQyxvQkFBb0IsT0FBTyxhQUFhOzs7Ozs7a0RBRTNDLDhEQUFDc0M7d0NBQUVMLFdBQVU7a0RBQ1ZqQyxvQkFBb0IsT0FDakIsOERBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtWLDhEQUFDcEIscUVBQWFBOzRCQUNac0QsV0FBVTs0QkFDVkssV0FBVTs0QkFDVkMsY0FBYzs0QkFDZEwsT0FBTzs0QkFDUEYsV0FBVTtzQ0FFVDtnQ0FDQztvQ0FDRXlCLFFBQVE7b0NBQ1JDLE9BQU8zRCxvQkFBb0IsT0FBTyxjQUFjO29DQUNoRGMsYUFBYWQsb0JBQW9CLE9BQzdCLG9EQUNBO2dDQUNOO2dDQUNBO29DQUNFMEQsUUFBUTtvQ0FDUkMsT0FBTzNELG9CQUFvQixPQUFPLFlBQVk7b0NBQzlDYyxhQUFhZCxvQkFBb0IsT0FDN0IsMkRBQ0E7Z0NBQ047Z0NBQ0E7b0NBQ0UwRCxRQUFRO29DQUNSQyxPQUFPM0Qsb0JBQW9CLE9BQU8sWUFBWTtvQ0FDOUNjLGFBQWFkLG9CQUFvQixPQUM3Qix5RUFDQTtnQ0FDTjtnQ0FDQTtvQ0FDRTBELFFBQVE7b0NBQ1JDLE9BQU8zRCxvQkFBb0IsT0FBTyxrQkFBa0I7b0NBQ3BEYyxhQUFhZCxvQkFBb0IsT0FDN0Isc0VBQ0E7Z0NBQ047NkJBQ0QsQ0FBQzRDLEdBQUcsQ0FBQyxDQUFDZ0IsTUFBTWQsc0JBQ1gsOERBQUNqRSxzRUFBY0E7b0NBQWFxRCxXQUFVO29DQUFPRCxXQUFVOztzREFDckQsOERBQUNGOzRDQUFJRSxXQUFVO3NEQUNaMkIsS0FBS0YsTUFBTTs7Ozs7O3NEQUVkLDhEQUFDM0I7NENBQUlFLFdBQVd2RCwrQ0FBRUEsQ0FBQyxtQ0FBbUNrQixhQUFhLGlCQUFpQjs7OERBQ2xGLDhEQUFDaUU7b0RBQUc1QixXQUFVOzhEQUE2RDJCLEtBQUtELEtBQUs7Ozs7Ozs4REFDckYsOERBQUNyQjtvREFBRUwsV0FBVTs4REFBc0MyQixLQUFLOUMsV0FBVzs7Ozs7Ozs7Ozs7OzttQ0FObERnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQWU3Qiw4REFBQ2Q7Z0JBQVFDLFdBQVU7MEJBQ2pCLDRFQUFDRjtvQkFBSUUsV0FBVTs4QkFDYiw0RUFBQ3RELHVFQUFlQTt3QkFBQ3VELFdBQVU7d0JBQU9DLE9BQU87a0NBQ3ZDLDRFQUFDSjs0QkFBSUUsV0FBVTs7OENBQ2IsOERBQUNzQjtvQ0FBR3RCLFdBQVU7OENBQ1hqQyxvQkFBb0IsT0FBTyxxQ0FBcUM7Ozs7Ozs4Q0FFbkUsOERBQUNzQztvQ0FBRUwsV0FBVTs4Q0FDVmpDLG9CQUFvQixPQUNqQix3RUFDQTs7Ozs7OzhDQUVOLDhEQUFDK0I7b0NBQUlFLFdBQVU7OENBQ2IsNEVBQUNwRCxzRUFBY0E7d0NBQUNxRCxXQUFVO2tEQUN4Qiw0RUFBQ2xFLHlEQUFNQTs0Q0FDTGlGLFNBQVMsSUFBTWpFLE9BQU9rRSxJQUFJLENBQUMsSUFBb0IsT0FBaEJsRCxpQkFBZ0I7NENBQy9DK0MsTUFBSzs0Q0FDTEMsU0FBUTs0Q0FDUmYsV0FBVTtzREFFVGpDLG9CQUFvQixPQUFPLHVCQUF1Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFVakUsOERBQUNnQztnQkFBUUMsV0FBV3ZELCtDQUFFQSxDQUFDLGtCQUFrQmtCLGFBQWEsaUJBQWlCOzBCQUNyRSw0RUFBQ21DO29CQUFJRSxXQUFVOztzQ0FDYiw4REFBQ3RELHVFQUFlQTs0QkFBQ3VELFdBQVU7NEJBQU9DLE9BQU87c0NBQ3ZDLDRFQUFDSjtnQ0FBSUUsV0FBVTs7a0RBQ2IsOERBQUNzQjt3Q0FBR3RCLFdBQVU7a0RBQ1hqQyxvQkFBb0IsT0FBTyxvQkFBb0I7Ozs7OztrREFFbEQsOERBQUNzQzt3Q0FBRUwsV0FBVTtrREFDVmpDLG9CQUFvQixPQUNqQixzREFDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS1YsOERBQUNwQixxRUFBYUE7NEJBQ1pzRCxXQUFVOzRCQUNWTSxjQUFjOzRCQUNkTCxPQUFPOzRCQUNQRixXQUFVO3NDQUVUO2dDQUNDO29DQUNFNkIsVUFBVTlELG9CQUFvQixPQUFPLHVDQUF1QztvQ0FDNUUrRCxRQUFRL0Qsb0JBQW9CLE9BQ3hCLDZNQUNBO2dDQUNOO2dDQUNBO29DQUNFOEQsVUFBVTlELG9CQUFvQixPQUFPLGtDQUFrQztvQ0FDdkUrRCxRQUFRL0Qsb0JBQW9CLE9BQ3hCLHNOQUNBO2dDQUNOO2dDQUNBO29DQUNFOEQsVUFBVTlELG9CQUFvQixPQUFPLDJCQUEyQjtvQ0FDaEUrRCxRQUFRL0Qsb0JBQW9CLE9BQ3hCLCtNQUNBO2dDQUNOO2dDQUNBO29DQUNFOEQsVUFBVTtvQ0FDVkMsUUFBUTtnQ0FDVjtnQ0FDQTtvQ0FDRUQsVUFBVTtvQ0FDVkMsUUFBUTtnQ0FDVjtnQ0FDQTtvQ0FDRUQsVUFBVTtvQ0FDVkMsUUFBUTtnQ0FDVjtnQ0FDQTtvQ0FDRUQsVUFBVTtvQ0FDVkMsUUFBUTtnQ0FDVjtnQ0FDQTtvQ0FDRUQsVUFBVTtvQ0FDVkMsUUFBUTtnQ0FDVjtnQ0FDQTtvQ0FDRUQsVUFBVTtvQ0FDVkMsUUFBUTtnQ0FDVjtnQ0FDQTtvQ0FDRUQsVUFBVTtvQ0FDVkMsUUFBUTtnQ0FDVjs2QkFDRCxDQUFDbkIsR0FBRyxDQUFDLENBQUNvQixLQUFLbEIsc0JBQ1YsOERBQUNqRSxzRUFBY0E7b0NBQWFxRCxXQUFVOzhDQUNwQyw0RUFBQytCO3dDQUNDaEMsV0FBV3ZELCtDQUFFQSxDQUFDLG9DQUFvQ2tCLGFBQWEsaUJBQWlCOzswREFFbEYsOERBQUNzRTtnREFBUWpDLFdBQVU7O2tFQUNqQiw4REFBQzRCO3dEQUFHNUIsV0FBVTtrRUFBMkQrQixJQUFJRixRQUFROzs7Ozs7a0VBQ3JGLDhEQUFDekI7d0RBQUtKLFdBQVd2RCwrQ0FBRUEsQ0FBQywyQ0FBMkNrQixhQUFhLGdDQUFnQztrRUFDMUcsNEVBQUN1RTs0REFDQ0MsT0FBTTs0REFDTm5DLFdBQVU7NERBQ1ZvQyxNQUFLOzREQUNMQyxTQUFROzREQUNSQyxRQUFPO3NFQUVQLDRFQUFDQztnRUFDQ0MsZUFBYztnRUFDZEMsZ0JBQWU7Z0VBQ2ZDLGFBQVk7Z0VBQ1pDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBS1YsOERBQUM3QztnREFBSUUsV0FBVTswREFDYiw0RUFBQ0s7b0RBQUVMLFdBQVU7OERBQXNDK0IsSUFBSUQsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBeEI1Q2pCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFrQzVCN0QsaUNBQ0MsOERBQUM4QztnQkFBSUUsV0FBVTswQkFDYiw0RUFBQ3RELHVFQUFlQTtvQkFBQ3VELFdBQVU7b0JBQVEyQyxVQUFVOzhCQUMzQyw0RUFBQzlDO3dCQUFJRSxXQUFVO2tDQUNiLDRFQUFDNUQsb0ZBQWtCQTs0QkFDakJxQyxhQUFhdkI7NEJBQ2IyRixTQUFTO2dDQUNQNUYsbUJBQW1CO2dDQUNuQkUsbUJBQW1CMkY7NEJBQ3JCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRaEI7R0F0ZndCaEc7O1FBQ1A1QixzREFBU0E7UUFTRG9CLDZEQUFhQTtRQUNmQyxtRUFBZ0JBO1FBQ2ZDLHlEQUFjQTs7O0tBWmRNIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFrcmFtWWFoeWFcXERlc2t0b3BcXGVjb21tZXJjZXByb1xcc3JjXFxwYWdlc1xcc2VydmljZXNcXFNlcnZpY2VzUGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHtcbiAgQXJyb3dSaWdodCxcbiAgU2VhcmNoLFxuICBQYWNrYWdlLFxuICBUcnVjayxcbiAgRmlsZUNoZWNrLFxuICBVc2VycyxcbiAgQ2xpcGJvYXJkTGlzdCxcbiAgRmlsdGVyLFxuICBYLFxuICBTdGFyLFxuICBDbG9jayxcbiAgQ2hlY2tDaXJjbGUsXG4gIFBob25lLFxuICBNYWlsLFxuICBNYXBQaW5cbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJy4uLy4uL2NvbXBvbmVudHMvdWkvQnV0dG9uJztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICcuLi8uLi9jb21wb25lbnRzL3VpL0NhcmQnO1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICcuLi8uLi9jb21wb25lbnRzL3VpL0lucHV0JztcbmltcG9ydCB7IFNlcnZpY2VCb29raW5nRm9ybSB9IGZyb20gJy4uLy4uL2NvbXBvbmVudHMvZm9ybXMvU2VydmljZUJvb2tpbmdGb3JtJztcbmltcG9ydCB7IHNlcnZpY2VzIH0gZnJvbSAnLi4vLi4vZGF0YS9zZXJ2aWNlcyc7XG5pbXBvcnQgeyB1c2VUaGVtZVN0b3JlIH0gZnJvbSAnLi4vLi4vc3RvcmVzL3RoZW1lU3RvcmUnO1xuaW1wb3J0IHsgdXNlTGFuZ3VhZ2VTdG9yZSB9IGZyb20gJy4uLy4uL3N0b3Jlcy9sYW5ndWFnZVN0b3JlJztcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnLi4vLi4vdHJhbnNsYXRpb25zJztcbmltcG9ydCB7IGNuIH0gZnJvbSAnLi4vLi4vbGliL3V0aWxzJztcbmltcG9ydCB7IFNjcm9sbEFuaW1hdGlvbiwgU2Nyb2xsU3RhZ2dlciwgSG92ZXJBbmltYXRpb24gfSBmcm9tICcuLi8uLi9jb21wb25lbnRzL3VpL2FuaW1hdGlvbnMnO1xuXG5jb25zdCBpY29ucyA9IHtcbiAgU2VhcmNoLFxuICBQYWNrYWdlLFxuICBUcnVjayxcbiAgRmlsZUNoZWNrLFxuICBVc2VycyxcbiAgQ2xpcGJvYXJkTGlzdFxufTtcblxuaW50ZXJmYWNlIFNlcnZpY2VGaWx0ZXJzIHtcbiAgc2VhcmNoUXVlcnk6IHN0cmluZztcbiAgY2F0ZWdvcnk6IHN0cmluZztcbiAgc29ydEJ5OiAnbmFtZScgfCAncG9wdWxhcml0eScgfCAncmVjZW50Jztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2VydmljZXNQYWdlKCkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgW3Nob3dCb29raW5nRm9ybSwgc2V0U2hvd0Jvb2tpbmdGb3JtXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NlbGVjdGVkU2VydmljZSwgc2V0U2VsZWN0ZWRTZXJ2aWNlXSA9IHVzZVN0YXRlPHN0cmluZyB8IHVuZGVmaW5lZD4oKTtcbiAgY29uc3QgW3Nob3dGaWx0ZXJzLCBzZXRTaG93RmlsdGVyc10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtmaWx0ZXJzLCBzZXRGaWx0ZXJzXSA9IHVzZVN0YXRlPFNlcnZpY2VGaWx0ZXJzPih7XG4gICAgc2VhcmNoUXVlcnk6ICcnLFxuICAgIGNhdGVnb3J5OiAnYWxsJyxcbiAgICBzb3J0Qnk6ICduYW1lJ1xuICB9KTtcbiAgY29uc3QgeyBpc0RhcmtNb2RlIH0gPSB1c2VUaGVtZVN0b3JlKCk7XG4gIGNvbnN0IHsgbGFuZ3VhZ2UgfSA9IHVzZUxhbmd1YWdlU3RvcmUoKTtcbiAgY29uc3QgeyB0LCBsb2NhbGUgfSA9IHVzZVRyYW5zbGF0aW9uKCk7XG5cbiAgLy8g2KfYs9iq2K7Yr9in2YUg2KfZhNmE2LrYqSDZhdmGINin2YTZhdiz2KfYsSDYo9mIINmF2YYg2KfZhNmF2KrYrNixXG4gIGNvbnN0IGN1cnJlbnRMYW5ndWFnZSA9IChsb2NhbGUgYXMgJ2FyJyB8ICdlbicpIHx8IGxhbmd1YWdlO1xuXG4gIC8vIFNlcnZpY2UgY2F0ZWdvcmllcyBmb3IgZmlsdGVyaW5nXG4gIGNvbnN0IGNhdGVnb3JpZXMgPSBbXG4gICAgeyBpZDogJ2FsbCcsIG5hbWU6IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICfYrNmF2YrYuSDYp9mE2K7Yr9mF2KfYqicgOiAnQWxsIFNlcnZpY2VzJyB9LFxuICAgIHsgaWQ6ICdpbnNwZWN0aW9uJywgbmFtZTogY3VycmVudExhbmd1YWdlID09PSAnYXInID8gJ9iu2K/Zhdin2Kog2KfZhNmB2K3YtScgOiAnSW5zcGVjdGlvbiBTZXJ2aWNlcycgfSxcbiAgICB7IGlkOiAnbG9naXN0aWNzJywgbmFtZTogY3VycmVudExhbmd1YWdlID09PSAnYXInID8gJ9in2YTYrtiv2YXYp9iqINin2YTZhNmI2KzYs9iq2YrYqScgOiAnTG9naXN0aWNzIFNlcnZpY2VzJyB9LFxuICAgIHsgaWQ6ICdjb25zdWx0aW5nJywgbmFtZTogY3VycmVudExhbmd1YWdlID09PSAnYXInID8gJ9in2YTYp9iz2KrYtNin2LHYp9iqJyA6ICdDb25zdWx0aW5nIFNlcnZpY2VzJyB9LFxuICAgIHsgaWQ6ICdjZXJ0aWZpY2F0aW9uJywgbmFtZTogY3VycmVudExhbmd1YWdlID09PSAnYXInID8gJ9in2YTYtNmH2KfYr9in2KonIDogJ0NlcnRpZmljYXRpb24gU2VydmljZXMnIH1cbiAgXTtcblxuICAvLyBGaWx0ZXIgYW5kIHNvcnQgc2VydmljZXNcbiAgY29uc3QgZmlsdGVyZWRTZXJ2aWNlcyA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIGxldCBmaWx0ZXJlZCA9IHNlcnZpY2VzLmZpbHRlcihzZXJ2aWNlID0+IHtcbiAgICAgIGNvbnN0IHNlYXJjaFRlcm0gPSBmaWx0ZXJzLnNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCk7XG4gICAgICBjb25zdCBzZXJ2aWNlTmFtZSA9IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/IHNlcnZpY2UubmFtZV9hciB8fCBzZXJ2aWNlLm5hbWUgOiBzZXJ2aWNlLm5hbWU7XG4gICAgICBjb25zdCBzZXJ2aWNlRGVzYyA9IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/IHNlcnZpY2UuZGVzY3JpcHRpb25fYXIgfHwgc2VydmljZS5kZXNjcmlwdGlvbiA6IHNlcnZpY2UuZGVzY3JpcHRpb247XG5cbiAgICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPSAhc2VhcmNoVGVybSB8fFxuICAgICAgICBzZXJ2aWNlTmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pIHx8XG4gICAgICAgIHNlcnZpY2VEZXNjLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybSk7XG5cbiAgICAgIGNvbnN0IG1hdGNoZXNDYXRlZ29yeSA9IGZpbHRlcnMuY2F0ZWdvcnkgPT09ICdhbGwnIHx8XG4gICAgICAgIHNlcnZpY2UuaWQuaW5jbHVkZXMoZmlsdGVycy5jYXRlZ29yeSkgfHxcbiAgICAgICAgKGZpbHRlcnMuY2F0ZWdvcnkgPT09ICdsb2dpc3RpY3MnICYmIFsnc2hpcHBpbmcnLCAnc3RvcmFnZSddLmluY2x1ZGVzKHNlcnZpY2UuaWQpKSB8fFxuICAgICAgICAoZmlsdGVycy5jYXRlZ29yeSA9PT0gJ2luc3BlY3Rpb24nICYmIHNlcnZpY2UuaWQgPT09ICdpbnNwZWN0aW9uJykgfHxcbiAgICAgICAgKGZpbHRlcnMuY2F0ZWdvcnkgPT09ICdjb25zdWx0aW5nJyAmJiBbJ2NvbnN1bHRpbmcnLCAnb3JkZXItbWFuYWdlbWVudCddLmluY2x1ZGVzKHNlcnZpY2UuaWQpKSB8fFxuICAgICAgICAoZmlsdGVycy5jYXRlZ29yeSA9PT0gJ2NlcnRpZmljYXRpb24nICYmIHNlcnZpY2UuaWQgPT09ICdjZXJ0aWZpY2F0aW9uJyk7XG5cbiAgICAgIHJldHVybiBtYXRjaGVzU2VhcmNoICYmIG1hdGNoZXNDYXRlZ29yeTtcbiAgICB9KTtcblxuICAgIC8vIFNvcnQgc2VydmljZXNcbiAgICBmaWx0ZXJlZC5zb3J0KChhLCBiKSA9PiB7XG4gICAgICBjb25zdCBhTmFtZSA9IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/IGEubmFtZV9hciB8fCBhLm5hbWUgOiBhLm5hbWU7XG4gICAgICBjb25zdCBiTmFtZSA9IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/IGIubmFtZV9hciB8fCBiLm5hbWUgOiBiLm5hbWU7XG5cbiAgICAgIHN3aXRjaCAoZmlsdGVycy5zb3J0QnkpIHtcbiAgICAgICAgY2FzZSAnbmFtZSc6XG4gICAgICAgICAgcmV0dXJuIGFOYW1lLmxvY2FsZUNvbXBhcmUoYk5hbWUpO1xuICAgICAgICBjYXNlICdwb3B1bGFyaXR5JzpcbiAgICAgICAgICAvLyBNb2NrIHBvcHVsYXJpdHkgc29ydGluZyAtIGluIHJlYWwgYXBwLCB0aGlzIHdvdWxkIGJlIGJhc2VkIG9uIGFjdHVhbCBkYXRhXG4gICAgICAgICAgcmV0dXJuIE1hdGgucmFuZG9tKCkgLSAwLjU7XG4gICAgICAgIGNhc2UgJ3JlY2VudCc6XG4gICAgICAgICAgcmV0dXJuIG5ldyBEYXRlKGIuY3JlYXRlZEF0KS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShhLmNyZWF0ZWRBdCkuZ2V0VGltZSgpO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHJldHVybiAwO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgcmV0dXJuIGZpbHRlcmVkO1xuICB9LCBbc2VydmljZXMsIGZpbHRlcnMsIGN1cnJlbnRMYW5ndWFnZV0pO1xuXG4gIGNvbnN0IGhhbmRsZUJvb2tTZXJ2aWNlID0gKHNlcnZpY2VOYW1lOiBzdHJpbmcpID0+IHtcbiAgICBzZXRTZWxlY3RlZFNlcnZpY2Uoc2VydmljZU5hbWUpO1xuICAgIHNldFNob3dCb29raW5nRm9ybSh0cnVlKTtcbiAgfTtcblxuICBjb25zdCBjbGVhckZpbHRlcnMgPSAoKSA9PiB7XG4gICAgc2V0RmlsdGVycyh7XG4gICAgICBzZWFyY2hRdWVyeTogJycsXG4gICAgICBjYXRlZ29yeTogJ2FsbCcsXG4gICAgICBzb3J0Qnk6ICduYW1lJ1xuICAgIH0pO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdj5cbiAgICAgIHsvKiBDb21wYWN0IFByb2Zlc3Npb25hbCBIZXJvIFNlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcInJlbGF0aXZlIHB5LTE2IG92ZXJmbG93LWhpZGRlbiB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi01MDBcIixcbiAgICAgICAgaXNEYXJrTW9kZVxuICAgICAgICAgID8gXCJiZy1ncmFkaWVudC10by1iciBmcm9tLXNsYXRlLTkwMCB2aWEtc2xhdGUtODAwIHRvLXNsYXRlLTkwMFwiXG4gICAgICAgICAgOiBcImJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtNTAgdmlhLXdoaXRlIHRvLXNsYXRlLTEwMFwiXG4gICAgICApfT5cbiAgICAgICAgey8qIFN1YnRsZSBCYWNrZ3JvdW5kIFBhdHRlcm4gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgIFwiYWJzb2x1dGUgaW5zZXQtMCBvcGFjaXR5LTUgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTUwMFwiLFxuICAgICAgICAgICAgaXNEYXJrTW9kZSA/IFwib3BhY2l0eS0xMFwiIDogXCJvcGFjaXR5LTVcIlxuICAgICAgICAgICl9PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLVtsaW5lYXItZ3JhZGllbnQocmdiYSg1OSwxMzAsMjQ2LDAuMSlfMXB4LHRyYW5zcGFyZW50XzFweCksbGluZWFyLWdyYWRpZW50KDkwZGVnLHJnYmEoNTksMTMwLDI0NiwwLjEpXzFweCx0cmFuc3BhcmVudF8xcHgpXSBiZy1bc2l6ZTo1MHB4XzUwcHhdXCI+PC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyLWN1c3RvbSByZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgICAgPFNjcm9sbEFuaW1hdGlvbiBhbmltYXRpb249XCJmYWRlXCIgZGVsYXk9ezAuMX0+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIHsvKiBNYWluIEhlYWRpbmcgKi99XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgIFwidGV4dC00eGwgbWQ6dGV4dC01eGwgZm9udC1ib2xkIGxlYWRpbmctdGlnaHQgdHJhY2tpbmctdGlnaHQgbWItNlwiLFxuICAgICAgICAgICAgICAgIGlzRGFya01vZGUgPyBcInRleHQtd2hpdGVcIiA6IFwidGV4dC1zbGF0ZS05MDBcIlxuICAgICAgICAgICAgICApfT5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJibG9ja1wiPlxuICAgICAgICAgICAgICAgICAge2N1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICfYrtiv2YXYp9iqJyA6ICdCdXNpbmVzcyd9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICBcImJsb2NrIGJnLWdyYWRpZW50LXRvLXIgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIixcbiAgICAgICAgICAgICAgICAgIGlzRGFya01vZGVcbiAgICAgICAgICAgICAgICAgICAgPyBcImZyb20tcHJpbWFyeS00MDAgdG8tYmx1ZS00MDBcIlxuICAgICAgICAgICAgICAgICAgICA6IFwiZnJvbS1wcmltYXJ5LTYwMCB0by1ibHVlLTYwMFwiXG4gICAgICAgICAgICAgICAgKX0+XG4gICAgICAgICAgICAgICAgICB7Y3VycmVudExhbmd1YWdlID09PSAnYXInID8gJ9in2YTYo9i52YXYp9mEJyA6ICdTZXJ2aWNlcyd9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2gxPlxuXG4gICAgICAgICAgICAgIHsvKiBEZXNjcmlwdGlvbiAqL31cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICBcInRleHQtbGcgbWQ6dGV4dC14bCBsZWFkaW5nLXJlbGF4ZWQgbWF4LXctM3hsIG14LWF1dG8gbWItOFwiLFxuICAgICAgICAgICAgICAgIGlzRGFya01vZGUgPyBcInRleHQtc2xhdGUtMzAwXCIgOiBcInRleHQtc2xhdGUtNjAwXCJcbiAgICAgICAgICAgICAgKX0+XG4gICAgICAgICAgICAgICAge2N1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJ1xuICAgICAgICAgICAgICAgICAgPyAn2K7Yr9mF2KfYqiDYr9i52YUg2LTYp9mF2YTYqSDZhNiq2KjYs9mK2Lcg2LnZhdmE2YrYp9iq2YMg2YjYqti52LLZitiyINmD2YHYp9ih2Kkg2KPYudmF2KfZhNmDINmF2Lkg2K3ZhNmI2YQg2YXYqti32YjYsdipINmI2YXYrti12LXYqS4nXG4gICAgICAgICAgICAgICAgICA6ICdDb21wcmVoZW5zaXZlIHN1cHBvcnQgc2VydmljZXMgdG8gc3RyZWFtbGluZSB5b3VyIG9wZXJhdGlvbnMgYW5kIGVuaGFuY2UgYnVzaW5lc3MgZWZmaWNpZW5jeSB3aXRoIGFkdmFuY2VkLCB0YWlsb3JlZCBzb2x1dGlvbnMuJ31cbiAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgIHsvKiBRdWljayBTdGF0cyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGdhcC00IG1iLThcIj5cbiAgICAgICAgICAgICAgICA8U2Nyb2xsU3RhZ2dlciBhbmltYXRpb249XCJzbGlkZVwiIGRpcmVjdGlvbj1cInVwXCIgc3RhZ2dlckRlbGF5PXswLjF9PlxuICAgICAgICAgICAgICAgICAge1tcbiAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgIGljb246IDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4sXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICc2KycsXG4gICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICfYrtiv2YXYqSDZhdiq2K7Ytdi12KknIDogJ0V4cGVydCBTZXJ2aWNlcydcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgIGljb246IDxTdGFyIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPixcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogJzUwMCsnLFxuICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcicgPyAn2LnZhdmK2YQg2LHYp9i22YonIDogJ1NhdGlzZmllZCBDbGllbnRzJ1xuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgaWNvbjogPENsb2NrIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPixcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogJzI0LzQ4aCcsXG4gICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICfZiNmC2Kog2KfZhNin2LPYqtis2KfYqNipJyA6ICdSZXNwb25zZSBUaW1lJ1xuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgaWNvbjogPE1hcFBpbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4sXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICc1MCsnLFxuICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcicgPyAn2K/ZiNmE2KknIDogJ0NvdW50cmllcydcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgXS5tYXAoKHN0YXQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICBcInAtNCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGlzRGFya01vZGVcbiAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1zbGF0ZS04MDAvNTAgaG92ZXI6Ymctc2xhdGUtODAwLzcwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy13aGl0ZS81MCBob3ZlcjpiZy13aGl0ZS83MFwiXG4gICAgICAgICAgICAgICAgICAgICl9PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICAgIFwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdy04IGgtOCByb3VuZGVkLWZ1bGwgbXgtYXV0byBtYi0yXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0RhcmtNb2RlID8gXCJiZy1wcmltYXJ5LTUwMC8yMCB0ZXh0LXByaW1hcnktNDAwXCIgOiBcImJnLXByaW1hcnktMTAwIHRleHQtcHJpbWFyeS02MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICl9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3N0YXQuaWNvbn1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICAgICBcInRleHQtbGcgZm9udC1ib2xkIG1iLTFcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzRGFya01vZGUgPyBcInRleHQtd2hpdGVcIiA6IFwidGV4dC1zbGF0ZS05MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICl9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3N0YXQudmFsdWV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgICAgXCJ0ZXh0LXhzIGZvbnQtbWVkaXVtXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBpc0RhcmtNb2RlID8gXCJ0ZXh0LXNsYXRlLTMwMFwiIDogXCJ0ZXh0LXNsYXRlLTYwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgKX0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7c3RhdC5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L1Njcm9sbFN0YWdnZXI+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBBY3Rpb24gQnV0dG9ucyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGp1c3RpZnktY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPEhvdmVyQW5pbWF0aW9uIGFuaW1hdGlvbj1cInNjYWxlXCI+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtOCBweS0zIHRleHQtbGcgZm9udC1zZW1pYm9sZCBncm91cFwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKGAvJHtjdXJyZW50TGFuZ3VhZ2V9L2NvbnRhY3RgKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFBob25lIGNsYXNzTmFtZT17YCR7Y3VycmVudExhbmd1YWdlID09PSAnYXInID8gJ21sLTInIDogJ21yLTInfSBoLTUgdy01IGdyb3VwLWhvdmVyOnNjYWxlLTExMCB0cmFuc2l0aW9uLXRyYW5zZm9ybWB9IC8+XG4gICAgICAgICAgICAgICAgICAgIHtjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcicgPyAn2KfYs9iq2LTYp9ix2Kkg2YXYrNin2YbZitipJyA6ICdGcmVlIENvbnN1bHRhdGlvbid9XG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L0hvdmVyQW5pbWF0aW9uPlxuICAgICAgICAgICAgICAgIDxIb3ZlckFuaW1hdGlvbiBhbmltYXRpb249XCJzY2FsZVwiPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTggcHktMyB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgZ3JvdXBcIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnc2VydmljZXMtZ3JpZCcpPy5zY3JvbGxJbnRvVmlldyh7IGJlaGF2aW9yOiAnc21vb3RoJyB9KX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPXtgJHtjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcicgPyAnbXItMiByb3RhdGUtMTgwJyA6ICdtbC0yJ30gaC01IHctNSBncm91cC1ob3Zlcjp0cmFuc2xhdGUteC0xIHRyYW5zaXRpb24tdHJhbnNmb3JtYH0gLz5cbiAgICAgICAgICAgICAgICAgICAge2N1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICfYp9iz2KrZg9i02YEg2KfZhNiu2K/Zhdin2KonIDogJ0V4cGxvcmUgU2VydmljZXMnfVxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9Ib3ZlckFuaW1hdGlvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L1Njcm9sbEFuaW1hdGlvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBTZXJ2aWNlcyBPdmVydmlldyAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT17Y24oXCJweS0xNiBtZDpweS0yNFwiLCBpc0RhcmtNb2RlID8gXCJiZy1zbGF0ZS05MDBcIiA6IFwiYmctd2hpdGVcIil9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lci1jdXN0b21cIj5cbiAgICAgICAgICA8U2Nyb2xsQW5pbWF0aW9uIGFuaW1hdGlvbj1cImZhZGVcIiBkZWxheT17MC4zfT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctM3hsIG14LWF1dG8gdGV4dC1jZW50ZXIgbWItMTZcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTkwMCBkYXJrOnRleHQtd2hpdGUgbWItNFwiPlxuICAgICAgICAgICAgICAgIHtjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcicgPyAn2LnYsdmI2LYg2K7Yr9mF2KfYqtmG2KcnIDogJ091ciBTZXJ2aWNlIE9mZmVyaW5ncyd9XG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1zbGF0ZS02MDAgZGFyazp0ZXh0LXNsYXRlLTMwMFwiPlxuICAgICAgICAgICAgICAgIHtjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcidcbiAgICAgICAgICAgICAgICAgID8gJ9in2YPYqti02YEg2YXYrNmF2YjYudipINmD2KfZhdmE2Kkg2YXZhiDYrtiv2YXYp9iqINin2YTYo9i52YXYp9mEINin2YTZhdi12YXZhdipINmE2K/YudmFINi52YXZhNmK2KfYqtmDINmB2Yog2YPZhCDZhdix2K3ZhNipLidcbiAgICAgICAgICAgICAgICAgIDogJ0Rpc2NvdmVyIHRoZSBmdWxsIHJhbmdlIG9mIGJ1c2luZXNzIHNlcnZpY2VzIGRlc2lnbmVkIHRvIHN1cHBvcnQgeW91ciBvcGVyYXRpb25zIGF0IGV2ZXJ5IHN0YWdlLid9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvU2Nyb2xsQW5pbWF0aW9uPlxuXG4gICAgICAgICAgPFNjcm9sbFN0YWdnZXJcbiAgICAgICAgICAgIGFuaW1hdGlvbj1cInNsaWRlXCJcbiAgICAgICAgICAgIGRpcmVjdGlvbj1cInVwXCJcbiAgICAgICAgICAgIHN0YWdnZXJEZWxheT17MC4xfVxuICAgICAgICAgICAgZGVsYXk9ezAuNH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtzZXJ2aWNlcy5tYXAoKHNlcnZpY2UpID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgSWNvbiA9IGljb25zW3NlcnZpY2UuaWNvbiBhcyBrZXlvZiB0eXBlb2YgaWNvbnNdO1xuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxIb3ZlckFuaW1hdGlvbiBrZXk9e3NlcnZpY2UuaWR9IGFuaW1hdGlvbj1cImxpZnRcIj5cbiAgICAgICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImdyb3VwIGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLXNoYWRvdyBkdXJhdGlvbi0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEljb24gc2l6ZT17NDB9IGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS01MDAgZGFyazp0ZXh0LXByaW1hcnktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQteGwgbWItMiB0ZXh0LXNsYXRlLTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcicgPyBzZXJ2aWNlLm5hbWVfYXIgfHwgc2VydmljZS5uYW1lIDogc2VydmljZS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTYwMCBkYXJrOnRleHQtc2xhdGUtMzAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcicgPyBzZXJ2aWNlLmRlc2NyaXB0aW9uX2FyIHx8IHNlcnZpY2UuZGVzY3JpcHRpb24gOiBzZXJ2aWNlLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxIb3ZlckFuaW1hdGlvbiBhbmltYXRpb249XCJzY2FsZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQm9va1NlcnZpY2UoY3VycmVudExhbmd1YWdlID09PSAnYXInID8gc2VydmljZS5uYW1lX2FyIHx8IHNlcnZpY2UubmFtZSA6IHNlcnZpY2UubmFtZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcicgPyAn2KfYrdis2LIg2KfZhNiu2K/ZhdipJyA6ICdCb29rIFNlcnZpY2UnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvSG92ZXJBbmltYXRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SG92ZXJBbmltYXRpb24gYW5pbWF0aW9uPVwic2NhbGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKGAvJHtjdXJyZW50TGFuZ3VhZ2V9L3NlcnZpY2VzLyR7c2VydmljZS5zbHVnfWApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgZ3JvdXAtaG92ZXI6YmctcHJpbWFyeS01MCBncm91cC1ob3Zlcjpib3JkZXItcHJpbWFyeS0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2N1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICfZhdi52LHZgdipINin2YTZhdiy2YrYrycgOiAnTGVhcm4gTW9yZSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPXtgJHtjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcicgPyAnbXItMicgOiAnbWwtMid9IGgtNCB3LTQgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZ3JvdXAtaG92ZXI6dHJhbnNsYXRlLXgtMWB9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Ib3ZlckFuaW1hdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICA8L0hvdmVyQW5pbWF0aW9uPlxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgfSl9XG4gICAgICAgICAgPC9TY3JvbGxTdGFnZ2VyPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIEhvdyBXZSBXb3JrICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPXtjbihcInB5LTE2IG1kOnB5LTI0XCIsIGlzRGFya01vZGUgPyBcImJnLXNsYXRlLTgwMFwiIDogXCJiZy1zbGF0ZS01MFwiKX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyLWN1c3RvbVwiPlxuICAgICAgICAgIDxTY3JvbGxBbmltYXRpb24gYW5pbWF0aW9uPVwiZmFkZVwiIGRlbGF5PXswLjN9PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy0zeGwgbXgtYXV0byB0ZXh0LWNlbnRlciBtYi0xNlwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWQ6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtc2xhdGUtOTAwIGRhcms6dGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICAgICAge2N1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICfZg9mK2YEg2YbYudmF2YQnIDogJ0hvdyBXZSBXb3JrJ31cbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LXNsYXRlLTYwMCBkYXJrOnRleHQtc2xhdGUtMzAwXCI+XG4gICAgICAgICAgICAgICAge2N1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJ1xuICAgICAgICAgICAgICAgICAgPyAn2YbZh9is2YbYpyDYp9mE2KfYs9iq2LTYp9ix2Yog2YrYttmF2YYg2K3ZhNmI2YTYp9mLINmF2K7Ytdi12Kkg2YTYp9it2KrZitin2KzYp9iqINi52YXZhNmDINin2YTZgdix2YrYr9ipLidcbiAgICAgICAgICAgICAgICAgIDogJ091ciBjb25zdWx0YXRpdmUgYXBwcm9hY2ggZW5zdXJlcyB0YWlsb3JlZCBzb2x1dGlvbnMgZm9yIHlvdXIgdW5pcXVlIGJ1c2luZXNzIG5lZWRzLid9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvU2Nyb2xsQW5pbWF0aW9uPlxuXG4gICAgICAgICAgPFNjcm9sbFN0YWdnZXJcbiAgICAgICAgICAgIGFuaW1hdGlvbj1cInNsaWRlXCJcbiAgICAgICAgICAgIGRpcmVjdGlvbj1cInJpZ2h0XCJcbiAgICAgICAgICAgIHN0YWdnZXJEZWxheT17MC4xNX1cbiAgICAgICAgICAgIGRlbGF5PXswLjR9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC04XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7W1xuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgbnVtYmVyOiBcIjAxXCIsXG4gICAgICAgICAgICAgICAgdGl0bGU6IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/IFwi2KfZhNin2LPYqti02KfYsdipXCIgOiBcIkNvbnN1bHRhdGlvblwiLFxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcidcbiAgICAgICAgICAgICAgICAgID8gXCLZhtio2K/YoyDYqNin2LPYqti02KfYsdipINi02KfZhdmE2Kkg2YTZgdmH2YUg2KfYrdiq2YrYp9is2KfYqiDZiNiq2K3Yr9mK2KfYqiDYudmF2YTZgy5cIlxuICAgICAgICAgICAgICAgICAgOiBcIldlIGJlZ2luIHdpdGggYSB0aG9yb3VnaCBjb25zdWx0YXRpb24gdG8gdW5kZXJzdGFuZCB5b3VyIGJ1c2luZXNzIG5lZWRzIGFuZCBjaGFsbGVuZ2VzLlwiLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgbnVtYmVyOiBcIjAyXCIsXG4gICAgICAgICAgICAgICAgdGl0bGU6IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/IFwi2KfZhNiq2K3ZhNmK2YRcIiA6IFwiQW5hbHlzaXNcIixcbiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogY3VycmVudExhbmd1YWdlID09PSAnYXInXG4gICAgICAgICAgICAgICAgICA/IFwi2YrZgtmI2YUg2K7YqNix2KfYpNmG2Kcg2KjYqtit2YTZitmEINmF2KrYt9mE2KjYp9iq2YMg2YjYqti32YjZitixINiq2YjYtdmK2KfYqiDYrtiv2YXYqSDZhdiu2LXYtdipLlwiXG4gICAgICAgICAgICAgICAgICA6IFwiT3VyIGV4cGVydHMgYW5hbHl6ZSB5b3VyIHJlcXVpcmVtZW50cyBhbmQgZGV2ZWxvcCBjdXN0b21pemVkIHNlcnZpY2UgcmVjb21tZW5kYXRpb25zLlwiLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgbnVtYmVyOiBcIjAzXCIsXG4gICAgICAgICAgICAgICAgdGl0bGU6IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/IFwi2KfZhNiq2YbZgdmK2LBcIiA6IFwiSW1wbGVtZW50YXRpb25cIixcbiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogY3VycmVudExhbmd1YWdlID09PSAnYXInXG4gICAgICAgICAgICAgICAgICA/IFwi2YbZgtmI2YUg2KjYqtmG2YHZitiwINin2YTYrtiv2YXYp9iqINin2YTZhdiq2YHZgiDYudmE2YrZh9inINmF2Lkg2KfZhNin2YfYqtmF2KfZhSDYqNin2YTYqtmB2KfYtdmK2YQg2YjYttmF2KfZhiDYp9mE2KzZiNiv2KkuXCJcbiAgICAgICAgICAgICAgICAgIDogXCJXZSBpbXBsZW1lbnQgdGhlIGFncmVlZCBzZXJ2aWNlcyB3aXRoIGF0dGVudGlvbiB0byBkZXRhaWwgYW5kIHF1YWxpdHkgYXNzdXJhbmNlLlwiLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgbnVtYmVyOiBcIjA0XCIsXG4gICAgICAgICAgICAgICAgdGl0bGU6IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/IFwi2KfZhNiv2LnZhSDYp9mE2YXYs9iq2YXYsVwiIDogXCJPbmdvaW5nIFN1cHBvcnRcIixcbiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogY3VycmVudExhbmd1YWdlID09PSAnYXInXG4gICAgICAgICAgICAgICAgICA/IFwi2KfZhNmF2LHYp9mC2KjYqSDZiNin2YTYr9i52YUg2KfZhNmF2LPYqtmF2LEg2YrYttmF2YbYp9mGINin2YTZhtiq2KfYptisINin2YTZhdir2YTZiSDZiNin2YTZgtiv2LHYqSDYudmE2Ykg2KfZhNiq2YPZitmBLlwiXG4gICAgICAgICAgICAgICAgICA6IFwiQ29udGludW91cyBtb25pdG9yaW5nIGFuZCBzdXBwb3J0IGVuc3VyZSBvcHRpbWFsIHJlc3VsdHMgYW5kIGFkYXB0YWJpbGl0eS5cIixcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIF0ubWFwKChzdGVwLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8SG92ZXJBbmltYXRpb24ga2V5PXtpbmRleH0gYW5pbWF0aW9uPVwibGlmdFwiIGNsYXNzTmFtZT1cInJlbGF0aXZlIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1wcmltYXJ5LTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZm9udC1ib2xkIHRleHQtbGcgbWItNCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICB7c3RlcC5udW1iZXJ9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFwicC02IHJvdW5kZWQtbGcgc2hhZG93LXNtIGgtZnVsbFwiLCBpc0RhcmtNb2RlID8gXCJiZy1zbGF0ZS05MDBcIiA6IFwiYmctd2hpdGVcIil9PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi0yIHRleHQtc2xhdGUtOTAwIGRhcms6dGV4dC13aGl0ZVwiPntzdGVwLnRpdGxlfTwvaDM+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTYwMCBkYXJrOnRleHQtc2xhdGUtMzAwXCI+e3N0ZXAuZGVzY3JpcHRpb259PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0hvdmVyQW5pbWF0aW9uPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9TY3JvbGxTdGFnZ2VyPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIENUQSBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMTYgbWQ6cHktMjQgYmctcHJpbWFyeS01MDAgdGV4dC13aGl0ZVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lci1jdXN0b21cIj5cbiAgICAgICAgICA8U2Nyb2xsQW5pbWF0aW9uIGFuaW1hdGlvbj1cImZhZGVcIiBkZWxheT17MC4zfT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctM3hsIG14LWF1dG8gdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGZvbnQtYm9sZCBtYi02XCI+XG4gICAgICAgICAgICAgICAge2N1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICfZh9mEINij2YbYqiDZhdiz2KrYudivINmE2KrYudiy2YrYsiDYudmF2YTZitin2Kog2LnZhdmE2YPYnycgOiAnUmVhZHkgdG8gRW5oYW5jZSBZb3VyIEJ1c2luZXNzIE9wZXJhdGlvbnM/J31cbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBtYi04IHRleHQtcHJpbWFyeS01MFwiPlxuICAgICAgICAgICAgICAgIHtjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcidcbiAgICAgICAgICAgICAgICAgID8gJ9in2KrYtdmEINio2YHYsdmK2YLZhtinINmE2YXZhtin2YLYtNipINmD2YrZgSDZitmF2YPZhiDZhNiu2K/Zhdin2KrZhtinINiq2YTYqNmK2Kkg2KfYrdiq2YrYp9is2KfYqiDYudmF2YTZgyDYp9mE2YXYrdiv2K/YqS4nXG4gICAgICAgICAgICAgICAgICA6ICdDb250YWN0IG91ciB0ZWFtIHRvIGRpc2N1c3MgaG93IG91ciBzZXJ2aWNlcyBjYW4gYWRkcmVzcyB5b3VyIHNwZWNpZmljIGJ1c2luZXNzIG5lZWRzLid9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGp1c3RpZnktY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPEhvdmVyQW5pbWF0aW9uIGFuaW1hdGlvbj1cInNjYWxlXCI+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKGAvJHtjdXJyZW50TGFuZ3VhZ2V9L3NlcnZpY2VzL3JlcXVlc3RgKX1cbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy10cmFuc3BhcmVudCBib3JkZXItd2hpdGUgdGV4dC13aGl0ZSBob3ZlcjpiZy1wcmltYXJ5LTYwMCBweC04XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2N1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICfYt9mE2Kgg2LnYsdi2INiz2LnYsSDZhNmE2K7Yr9mF2KknIDogJ1JlcXVlc3QgU2VydmljZSBRdW90ZSd9XG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L0hvdmVyQW5pbWF0aW9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvU2Nyb2xsQW5pbWF0aW9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIEZBUSBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPXtjbihcInB5LTE2IG1kOnB5LTI0XCIsIGlzRGFya01vZGUgPyBcImJnLXNsYXRlLTkwMFwiIDogXCJiZy13aGl0ZVwiKX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyLWN1c3RvbVwiPlxuICAgICAgICAgIDxTY3JvbGxBbmltYXRpb24gYW5pbWF0aW9uPVwiZmFkZVwiIGRlbGF5PXswLjN9PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy0zeGwgbXgtYXV0byB0ZXh0LWNlbnRlciBtYi0xNlwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWQ6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtc2xhdGUtOTAwIGRhcms6dGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICAgICAge2N1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/ICfYp9mE2KPYs9im2YTYqSDYp9mE2LTYp9im2LnYqScgOiAnRnJlcXVlbnRseSBBc2tlZCBRdWVzdGlvbnMnfVxuICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtc2xhdGUtNjAwIGRhcms6dGV4dC1zbGF0ZS0zMDBcIj5cbiAgICAgICAgICAgICAgICB7Y3VycmVudExhbmd1YWdlID09PSAnYXInXG4gICAgICAgICAgICAgICAgICA/ICfYp9io2K3YqyDYudmGINil2KzYp9io2KfYqiDZhNmE2KPYs9im2YTYqSDYp9mE2LTYp9im2LnYqSDYrdmI2YQg2K7Yr9mF2KfYqiDYo9i52YXYp9mE2YbYpy4nXG4gICAgICAgICAgICAgICAgICA6ICdGaW5kIGFuc3dlcnMgdG8gY29tbW9uIHF1ZXN0aW9ucyBhYm91dCBvdXIgYnVzaW5lc3Mgc2VydmljZXMuJ31cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9TY3JvbGxBbmltYXRpb24+XG5cbiAgICAgICAgICA8U2Nyb2xsU3RhZ2dlclxuICAgICAgICAgICAgYW5pbWF0aW9uPVwiZmFkZVwiXG4gICAgICAgICAgICBzdGFnZ2VyRGVsYXk9ezAuMX1cbiAgICAgICAgICAgIGRlbGF5PXswLjR9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0byBzcGFjZS15LTZcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICBxdWVzdGlvbjogY3VycmVudExhbmd1YWdlID09PSAnYXInID8gXCLZhdinINmH2Yog2KPZhtmI2KfYuSDYp9mE2LTYsdmD2KfYqiDYp9mE2KrZiiDYqtiu2K/ZhdmI2YbZh9in2J9cIiA6IFwiV2hhdCB0eXBlcyBvZiBidXNpbmVzc2VzIGRvIHlvdSBzZXJ2ZT9cIixcbiAgICAgICAgICAgICAgICBhbnN3ZXI6IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJ1xuICAgICAgICAgICAgICAgICAgPyBcItmG2K7Yr9mFINmF2KzZhdmI2LnYqSDZiNin2LPYudipINmF2YYg2KfZhNi02LHZg9in2Kog2LnYqNixINmF2K7YqtmE2YEg2KfZhNi12YbYp9i52KfYqtiMINio2YXYpyDZgdmKINiw2YTZgyDYp9mE2KrYtdmG2YrYuSDZiNin2YTYqtis2LLYptipINmI2KfZhNiq2YjYstmK2Lkg2YjYp9mE2KrYrNin2LHYqSDYp9mE2KXZhNmD2KrYsdmI2YbZitipLiDYrtiv2YXYp9iq2YbYpyDZgtin2KjZhNipINmE2YTYqti32YjZitixINmI2YrZhdmD2YYg2KrYrti12YrYtdmH2Kcg2YTYqtmE2KjZitipINin2K3YqtmK2KfYrNin2Kog2YPZhCDZhdmGINin2YTYtNix2YPYp9iqINin2YTYtdi62YrYsdipINmI2KfZhNmF2KTYs9iz2KfYqiDYp9mE2YPYqNmK2LHYqS5cIlxuICAgICAgICAgICAgICAgICAgOiBcIldlIHNlcnZlIGEgd2lkZSByYW5nZSBvZiBidXNpbmVzc2VzIGFjcm9zcyB2YXJpb3VzIGluZHVzdHJpZXMsIGluY2x1ZGluZyBtYW51ZmFjdHVyaW5nLCByZXRhaWwsIGRpc3RyaWJ1dGlvbiwgYW5kIGUtY29tbWVyY2UuIE91ciBzZXJ2aWNlcyBhcmUgc2NhbGFibGUgYW5kIGNhbiBiZSBjdXN0b21pemVkIHRvIG1lZXQgdGhlIG5lZWRzIG9mIGJvdGggc21hbGwgYnVzaW5lc3NlcyBhbmQgbGFyZ2UgZW50ZXJwcmlzZXMuXCIsXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICBxdWVzdGlvbjogY3VycmVudExhbmd1YWdlID09PSAnYXInID8gXCLZhdinINmH2Yog2LPYsdi52Kkg2KrYsdiq2YrYqCDYrtiv2YXYp9iqINin2YTZgdit2LXYn1wiIDogXCJIb3cgcXVpY2tseSBjYW4geW91IGFycmFuZ2UgaW5zcGVjdGlvbiBzZXJ2aWNlcz9cIixcbiAgICAgICAgICAgICAgICBhbnN3ZXI6IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJ1xuICAgICAgICAgICAgICAgICAgPyBcItmK2YXZg9mGINiq2LHYqtmK2Kgg2K7Yr9mF2KfYqiDYp9mE2YHYrdi1INin2YTZgtmK2KfYs9mK2Kkg2YHZiiDYuti22YjZhiA0OC03MiDYs9in2LnYqSDZhdmGINin2YTYt9mE2KguINmE2YTYp9it2KrZitin2KzYp9iqINin2YTYudin2KzZhNip2Iwg2YbZgtiv2YUg2K7Yr9mF2KfYqiDZhdi52KzZhNipINmK2YXZg9mGINis2K/ZiNmE2KrZh9inINmB2Yog2LrYttmI2YYgMjQg2LPYp9i52Kkg2YHZiiDZhdi52LjZhSDYp9mE2YXZiNin2YLYudiMINit2LPYqCDYp9mE2KrZiNmB2LEuINiq2LbZhdmGINi02KjZg9iq2YbYpyDYp9mE2LnYp9mE2YXZitipINmF2YYg2KfZhNmF2YHYqti02YrZhiDYo9mI2YLYp9iqINin2LPYqtis2KfYqNipINiz2LHZiti52KkuXCJcbiAgICAgICAgICAgICAgICAgIDogXCJTdGFuZGFyZCBpbnNwZWN0aW9uIHNlcnZpY2VzIGNhbiBiZSBhcnJhbmdlZCB3aXRoaW4gNDgtNzIgaG91cnMgb2YgcmVxdWVzdC4gRm9yIHVyZ2VudCBuZWVkcywgd2Ugb2ZmZXIgZXhwZWRpdGVkIHNlcnZpY2VzIHRoYXQgY2FuIGJlIHNjaGVkdWxlZCB3aXRoaW4gMjQgaG91cnMgaW4gbW9zdCBsb2NhdGlvbnMsIHN1YmplY3QgdG8gYXZhaWxhYmlsaXR5LiBPdXIgZ2xvYmFsIG5ldHdvcmsgb2YgaW5zcGVjdG9ycyBlbnN1cmVzIHF1aWNrIHJlc3BvbnNlIHRpbWVzLlwiLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgcXVlc3Rpb246IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA/IFwi2YfZhCDYqtmC2K/ZhdmI2YYg2K7Yr9mF2KfYqiDYr9mI2YTZitip2J9cIiA6IFwiRG8geW91IHByb3ZpZGUgc2VydmljZXMgaW50ZXJuYXRpb25hbGx5P1wiLFxuICAgICAgICAgICAgICAgIGFuc3dlcjogY3VycmVudExhbmd1YWdlID09PSAnYXInXG4gICAgICAgICAgICAgICAgICA/IFwi2YbYudmF2Iwg2YbZgtiv2YUg2K7Yr9mF2KfYqiDYo9i52YXYp9mE2YbYpyDYudin2YTZhdmK2YvYpyDZhdmGINiu2YTYp9mEINi02KjZg9iq2YbYpyDYp9mE2YjYp9iz2LnYqSDZhdmGINin2YTZhdmD2KfYqtioINmI2KfZhNi02LHZg9in2KEg2YHZiiDZhdix2KfZg9iyINin2YTYqti12YbZiti5INmI2KfZhNiu2K/Zhdin2Kog2KfZhNmE2YjYrNiz2KrZitipINin2YTYsdim2YrYs9mK2Kkg2LnYqNixINii2LPZitinINmI2KPZiNix2YjYqNinINmI2KfZhNij2YXYsdmK2YPYqtmK2YYuINmK2YXZg9mG2YbYpyDYqtmG2LPZitmCINin2YTYrtiv2YXYp9iqINi52KjYsSDYp9mE2LnYr9mK2K8g2YXZhiDYp9mE2KjZhNiv2KfZhiDZiNin2YTZhdmG2KfYt9mCLlwiXG4gICAgICAgICAgICAgICAgICA6IFwiWWVzLCB3ZSBvZmZlciBvdXIgYnVzaW5lc3Mgc2VydmljZXMgZ2xvYmFsbHkgdGhyb3VnaCBvdXIgZXh0ZW5zaXZlIG5ldHdvcmsgb2Ygb2ZmaWNlcyBhbmQgcGFydG5lcnMgaW4gbWFqb3IgbWFudWZhY3R1cmluZyBhbmQgbG9naXN0aWNzIGh1YnMgYWNyb3NzIEFzaWEsIEV1cm9wZSwgYW5kIHRoZSBBbWVyaWNhcy4gV2UgY2FuIGNvb3JkaW5hdGUgc2VydmljZXMgYWNyb3NzIG11bHRpcGxlIGNvdW50cmllcyBhbmQgcmVnaW9ucy5cIixcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIHF1ZXN0aW9uOiBcIldoYXQgY2VydGlmaWNhdGlvbnMgYW5kIHN0YW5kYXJkcyBkbyB5b3UgY29tcGx5IHdpdGg/XCIsXG4gICAgICAgICAgICAgICAgYW5zd2VyOiBcIk91ciBzZXJ2aWNlcyBjb21wbHkgd2l0aCBpbnRlcm5hdGlvbmFsIHN0YW5kYXJkcyBpbmNsdWRpbmcgSVNPIDkwMDEsIElTTyAxNDAwMSwgYW5kIGluZHVzdHJ5LXNwZWNpZmljIGNlcnRpZmljYXRpb25zLiBGb3IgcHJvZHVjdCBjZXJ0aWZpY2F0aW9uIHNlcnZpY2VzLCB3ZSB3b3JrIHdpdGggYWxsIG1ham9yIGNlcnRpZmljYXRpb24gYm9kaWVzIGFuZCBjYW4gYXNzaXN0IHdpdGggQ0UsIEZDQywgVUwsIGFuZCBvdGhlciBtYXJrZXQtc3BlY2lmaWMgcmVxdWlyZW1lbnRzLlwiLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgcXVlc3Rpb246IFwiSG93IGRvIHlvdSBlbnN1cmUgc2VydmljZSBxdWFsaXR5IGFuZCBjb25zaXN0ZW5jeT9cIixcbiAgICAgICAgICAgICAgICBhbnN3ZXI6IFwiV2UgbWFpbnRhaW4gc3RyaWN0IHF1YWxpdHkgY29udHJvbCBwcm9jZXNzZXMsIGluY2x1ZGluZyByZWd1bGFyIHRyYWluaW5nIGZvciBvdXIgc3RhZmYsIHN0YW5kYXJkaXplZCBvcGVyYXRpbmcgcHJvY2VkdXJlcywgYW5kIGNvbnRpbnVvdXMgbW9uaXRvcmluZyBvZiBzZXJ2aWNlIGRlbGl2ZXJ5LiBPdXIgcXVhbGl0eSBtYW5hZ2VtZW50IHN5c3RlbSBlbnN1cmVzIGNvbnNpc3RlbnQgc2VydmljZSBkZWxpdmVyeSBhY3Jvc3MgYWxsIGxvY2F0aW9ucy5cIixcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIHF1ZXN0aW9uOiBcIldoYXQgYXJlIHlvdXIgcGF5bWVudCB0ZXJtcyBhbmQgbWV0aG9kcz9cIixcbiAgICAgICAgICAgICAgICBhbnN3ZXI6IFwiV2UgYWNjZXB0IHZhcmlvdXMgcGF5bWVudCBtZXRob2RzIGluY2x1ZGluZyBiYW5rIHRyYW5zZmVycywgY3JlZGl0IGNhcmRzLCBhbmQgZGlnaXRhbCBwYXltZW50cy4gRm9yIHJlZ3VsYXIgY2xpZW50cywgd2Ugb2ZmZXIgZmxleGlibGUgcGF5bWVudCB0ZXJtcyBpbmNsdWRpbmcgbmV0LTMwIGFjY291bnRzLiBWb2x1bWUtYmFzZWQgZGlzY291bnRzIGFuZCBzZXJ2aWNlIHBhY2thZ2VzIGFyZSBhdmFpbGFibGUgZm9yIGxvbmctdGVybSBjb250cmFjdHMuXCIsXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICBxdWVzdGlvbjogXCJDYW4geW91IGhhbmRsZSBydXNoIG9yZGVycyBvciBlbWVyZ2VuY3kgc2l0dWF0aW9ucz9cIixcbiAgICAgICAgICAgICAgICBhbnN3ZXI6IFwiWWVzLCB3ZSBoYXZlIGRlZGljYXRlZCB0ZWFtcyB0byBoYW5kbGUgdXJnZW50IHJlcXVlc3RzIGFuZCBlbWVyZ2VuY3kgc2l0dWF0aW9ucy4gV2Ugb2ZmZXIgZXhwZWRpdGVkIHNlcnZpY2VzIGFjcm9zcyBhbGwgb3VyIHNlcnZpY2UgbGluZXMgd2l0aCBwcmlvcml0eSBoYW5kbGluZyBhbmQgMjQvNyBzdXBwb3J0IGZvciBjcml0aWNhbCBzaXR1YXRpb25zLlwiLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgcXVlc3Rpb246IFwiRG8geW91IHByb3ZpZGUgY3VzdG9taXplZCBzZXJ2aWNlIHBhY2thZ2VzP1wiLFxuICAgICAgICAgICAgICAgIGFuc3dlcjogXCJZZXMsIHdlIGNhbiBjcmVhdGUgY3VzdG9taXplZCBzZXJ2aWNlIHBhY2thZ2VzIHRoYXQgY29tYmluZSBtdWx0aXBsZSBzZXJ2aWNlcyB0byBtZWV0IHlvdXIgc3BlY2lmaWMgYnVzaW5lc3MgbmVlZHMuIE91ciBzb2x1dGlvbnMgYXJjaGl0ZWN0cyB3aWxsIHdvcmsgd2l0aCB5b3UgdG8gZGVzaWduIHRoZSBtb3N0IGVmZmljaWVudCBhbmQgY29zdC1lZmZlY3RpdmUgc2VydmljZSBwYWNrYWdlLlwiLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgcXVlc3Rpb246IFwiV2hhdCBraW5kIG9mIHJlcG9ydGluZyBhbmQgZG9jdW1lbnRhdGlvbiBkbyB5b3UgcHJvdmlkZT9cIixcbiAgICAgICAgICAgICAgICBhbnN3ZXI6IFwiV2UgcHJvdmlkZSBkZXRhaWxlZCBkaWdpdGFsIHJlcG9ydHMgZm9yIGFsbCBzZXJ2aWNlcywgaW5jbHVkaW5nIGluc3BlY3Rpb24gZmluZGluZ3MsIGNlcnRpZmljYXRpb24gc3RhdHVzLCBzaGlwcGluZyBkb2N1bWVudGF0aW9uLCBhbmQgcGVyZm9ybWFuY2UgYW5hbHl0aWNzLiBPdXIgY2xpZW50cyBoYXZlIGFjY2VzcyB0byBhIHNlY3VyZSBwb3J0YWwgZm9yIHJlYWwtdGltZSB0cmFja2luZyBhbmQgcmVwb3J0IGRvd25sb2Fkcy5cIixcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIHF1ZXN0aW9uOiBcIkhvdyBkbyB5b3UgaGFuZGxlIGNvbmZpZGVudGlhbCBpbmZvcm1hdGlvbj9cIixcbiAgICAgICAgICAgICAgICBhbnN3ZXI6IFwiV2UgbWFpbnRhaW4gc3RyaWN0IGNvbmZpZGVudGlhbGl0eSBwcm90b2NvbHMgYW5kIGFyZSBjb21wbGlhbnQgd2l0aCBHRFBSIGFuZCBvdGhlciBkYXRhIHByb3RlY3Rpb24gcmVndWxhdGlvbnMuIEFsbCBjbGllbnQgaW5mb3JtYXRpb24gaXMgc3RvcmVkIHNlY3VyZWx5LCBhbmQgb3VyIHN0YWZmIHNpZ25zIGNvbXByZWhlbnNpdmUgTkRBcy5cIixcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgXS5tYXAoKGZhcSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPEhvdmVyQW5pbWF0aW9uIGtleT17aW5kZXh9IGFuaW1hdGlvbj1cImxpZnRcIj5cbiAgICAgICAgICAgICAgICA8ZGV0YWlsc1xuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcImdyb3VwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuXCIsIGlzRGFya01vZGUgPyBcImJnLXNsYXRlLTgwMFwiIDogXCJiZy1zbGF0ZS01MFwiKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHN1bW1hcnkgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNiBjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS05MDAgZGFyazp0ZXh0LXdoaXRlIHByLThcIj57ZmFxLnF1ZXN0aW9ufTwvaDM+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2NuKFwiZmxleC1zaHJpbmstMCBtbC0xLjUgcC0xLjUgcm91bmRlZC1mdWxsXCIsIGlzRGFya01vZGUgPyBcInRleHQtc2xhdGUtMzAwIGJnLXNsYXRlLTcwMFwiIDogXCJ0ZXh0LXNsYXRlLTcwMCBiZy13aGl0ZVwiKX0+XG4gICAgICAgICAgICAgICAgICAgIDxzdmdcbiAgICAgICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTUgdy01IHRyYW5zZm9ybSBncm91cC1vcGVuOnJvdGF0ZS0xODAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk0xOSA5bC03IDctNy03XCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L3N1bW1hcnk+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC02IHBiLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNjAwIGRhcms6dGV4dC1zbGF0ZS0zMDBcIj57ZmFxLmFuc3dlcn08L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGV0YWlscz5cbiAgICAgICAgICAgICAgPC9Ib3ZlckFuaW1hdGlvbj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvU2Nyb2xsU3RhZ2dlcj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBCb29raW5nIEZvcm0gTW9kYWwgKi99XG4gICAgICB7c2hvd0Jvb2tpbmdGb3JtICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrIGJnLW9wYWNpdHktNTAgYmFja2Ryb3AtYmx1ci1zbSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTQgei01MFwiPlxuICAgICAgICAgIDxTY3JvbGxBbmltYXRpb24gYW5pbWF0aW9uPVwic2NhbGVcIiBkdXJhdGlvbj17MC4zfT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctMnhsIHctZnVsbFwiPlxuICAgICAgICAgICAgICA8U2VydmljZUJvb2tpbmdGb3JtXG4gICAgICAgICAgICAgICAgc2VydmljZU5hbWU9e3NlbGVjdGVkU2VydmljZX1cbiAgICAgICAgICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBzZXRTaG93Qm9va2luZ0Zvcm0oZmFsc2UpO1xuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRTZXJ2aWNlKHVuZGVmaW5lZCk7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvU2Nyb2xsQW5pbWF0aW9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlTWVtbyIsInVzZVJvdXRlciIsIkFycm93UmlnaHQiLCJTZWFyY2giLCJQYWNrYWdlIiwiVHJ1Y2siLCJGaWxlQ2hlY2siLCJVc2VycyIsIkNsaXBib2FyZExpc3QiLCJTdGFyIiwiQ2xvY2siLCJDaGVja0NpcmNsZSIsIlBob25lIiwiTWFwUGluIiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIlNlcnZpY2VCb29raW5nRm9ybSIsInNlcnZpY2VzIiwidXNlVGhlbWVTdG9yZSIsInVzZUxhbmd1YWdlU3RvcmUiLCJ1c2VUcmFuc2xhdGlvbiIsImNuIiwiU2Nyb2xsQW5pbWF0aW9uIiwiU2Nyb2xsU3RhZ2dlciIsIkhvdmVyQW5pbWF0aW9uIiwiaWNvbnMiLCJTZXJ2aWNlc1BhZ2UiLCJyb3V0ZXIiLCJzaG93Qm9va2luZ0Zvcm0iLCJzZXRTaG93Qm9va2luZ0Zvcm0iLCJzZWxlY3RlZFNlcnZpY2UiLCJzZXRTZWxlY3RlZFNlcnZpY2UiLCJzaG93RmlsdGVycyIsInNldFNob3dGaWx0ZXJzIiwiZmlsdGVycyIsInNldEZpbHRlcnMiLCJzZWFyY2hRdWVyeSIsImNhdGVnb3J5Iiwic29ydEJ5IiwiaXNEYXJrTW9kZSIsImxhbmd1YWdlIiwidCIsImxvY2FsZSIsImN1cnJlbnRMYW5ndWFnZSIsImNhdGVnb3JpZXMiLCJpZCIsIm5hbWUiLCJmaWx0ZXJlZFNlcnZpY2VzIiwiZmlsdGVyZWQiLCJmaWx0ZXIiLCJzZXJ2aWNlIiwic2VhcmNoVGVybSIsInRvTG93ZXJDYXNlIiwic2VydmljZU5hbWUiLCJuYW1lX2FyIiwic2VydmljZURlc2MiLCJkZXNjcmlwdGlvbl9hciIsImRlc2NyaXB0aW9uIiwibWF0Y2hlc1NlYXJjaCIsImluY2x1ZGVzIiwibWF0Y2hlc0NhdGVnb3J5Iiwic29ydCIsImEiLCJiIiwiYU5hbWUiLCJiTmFtZSIsImxvY2FsZUNvbXBhcmUiLCJNYXRoIiwicmFuZG9tIiwiRGF0ZSIsImNyZWF0ZWRBdCIsImdldFRpbWUiLCJoYW5kbGVCb29rU2VydmljZSIsImNsZWFyRmlsdGVycyIsImRpdiIsInNlY3Rpb24iLCJjbGFzc05hbWUiLCJhbmltYXRpb24iLCJkZWxheSIsImgxIiwic3BhbiIsInAiLCJkaXJlY3Rpb24iLCJzdGFnZ2VyRGVsYXkiLCJpY29uIiwidmFsdWUiLCJsYWJlbCIsIm1hcCIsInN0YXQiLCJpbmRleCIsInNpemUiLCJ2YXJpYW50Iiwib25DbGljayIsInB1c2giLCJkb2N1bWVudCIsImdldEVsZW1lbnRCeUlkIiwic2Nyb2xsSW50b1ZpZXciLCJiZWhhdmlvciIsImgyIiwiSWNvbiIsInNsdWciLCJudW1iZXIiLCJ0aXRsZSIsInN0ZXAiLCJoMyIsInF1ZXN0aW9uIiwiYW5zd2VyIiwiZmFxIiwiZGV0YWlscyIsInN1bW1hcnkiLCJzdmciLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94Iiwic3Ryb2tlIiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsImR1cmF0aW9uIiwib25DbG9zZSIsInVuZGVmaW5lZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/pages/services/ServicesPage.tsx\n"));

/***/ })

});