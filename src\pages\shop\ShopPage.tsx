import { useState, useMemo, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ShoppingCart, Heart, Star, ArrowRight, X, Search,
  Grid, List, SlidersHorizontal, Tag, Filter, ChevronDown,
  ArrowUpDown, ArrowDownUp, CheckCircle, Eye, Truck, Package,
  Award, TrendingUp, Clock, Zap, AlertCircle, XCircle,
  Compare, Bookmark, Share2, MoreHorizontal, Layers,
  Shield, Timer, Flame, Crown, Sparkles
} from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { LazyImage } from '../../components/ui/LazyImage';
import { EnhancedImage } from '../../components/ui/EnhancedImage';
import { formatCurrency, cn } from '../../lib/utils';
import { useCartStore } from '../../stores/cartStore';
import { useWishlistStore } from '../../stores/wishlistStore';
import { useAuthStore } from '../../stores/authStore';
import { useTheme } from 'next-themes';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { AuthModal } from '../../components/auth/AuthModal';
import { WholesaleQuoteForm } from '../../components/forms/WholesaleQuoteForm';
import { products, productCategories } from '../../data/products';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { Product, ProductFiltersState, SortOption } from '../../types/index';
import { useAuthenticatedAction } from '../../hooks/useAuthenticatedAction';
import { ProductFilters } from '../../components/shop/ProductFilters';

// Professional Product Badge Component
const ProductBadge = ({ product, language }: { product: Product; language: 'ar' | 'en' }) => {
  const badges = [];

  // New Arrival Badge
  const isNew = new Date(product.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  if (isNew) {
    badges.push({
      text: language === 'ar' ? 'جديد' : 'NEW',
      className: 'bg-green-500 text-white',
      icon: <Sparkles className="h-3 w-3" />
    });
  }

  // Best Seller Badge
  if (product.tags.includes('Best Seller')) {
    badges.push({
      text: language === 'ar' ? 'الأكثر مبيعاً' : 'BEST SELLER',
      className: 'bg-orange-500 text-white',
      icon: <TrendingUp className="h-3 w-3" />
    });
  }

  // Premium Badge
  if (product.tags.includes('Premium')) {
    badges.push({
      text: language === 'ar' ? 'مميز' : 'PREMIUM',
      className: 'bg-purple-500 text-white',
      icon: <Crown className="h-3 w-3" />
    });
  }

  // Sale Badge
  if (product.compareAtPrice && product.compareAtPrice > product.price) {
    const discount = Math.round((1 - product.price / product.compareAtPrice) * 100);
    badges.push({
      text: `${discount}% ${language === 'ar' ? 'خصم' : 'OFF'}`,
      className: 'bg-red-500 text-white',
      icon: <Flame className="h-3 w-3" />
    });
  }

  // Limited Stock Badge
  if (product.stock <= 5 && product.stock > 0) {
    badges.push({
      text: language === 'ar' ? 'كمية محدودة' : 'LIMITED',
      className: 'bg-yellow-500 text-white',
      icon: <Timer className="h-3 w-3" />
    });
  }

  return (
    <div className="absolute top-2 left-2 z-10 flex flex-col gap-1">
      {badges.slice(0, 2).map((badge, index) => (
        <div
          key={index}
          className={`${badge.className} px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1 shadow-md`}
        >
          {badge.icon}
          {badge.text}
        </div>
      ))}
    </div>
  );
};

// Stock Status Component
const StockStatus = ({ product, language }: { product: Product; language: 'ar' | 'en' }) => {
  if (product.stock === 0) {
    return (
      <div className="flex items-center text-red-500 text-sm">
        <XCircle className="h-4 w-4 mr-1" />
        {language === 'ar' ? 'نفد المخزون' : 'Out of Stock'}
      </div>
    );
  }

  if (product.stock <= 5) {
    return (
      <div className="flex items-center text-yellow-500 text-sm">
        <AlertCircle className="h-4 w-4 mr-1" />
        {language === 'ar' ? `${product.stock} متبقي` : `${product.stock} left`}
      </div>
    );
  }

  return (
    <div className="flex items-center text-green-500 text-sm">
      <CheckCircle className="h-4 w-4 mr-1" />
      {language === 'ar' ? 'متوفر' : 'In Stock'}
    </div>
  );
};

const ShopPage = () => {
  const router = useRouter();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showWholesaleForm, setShowWholesaleForm] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [quickViewProduct, setQuickViewProduct] = useState<Product | null>(null);
  const [sortOption, setSortOption] = useState<SortOption>('featured');
  const [isLoading, setIsLoading] = useState(true);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [compareList, setCompareList] = useState<string[]>([]);

  const maxPrice = useMemo(() => products.reduce((max, p) => p.price > max ? p.price : max, 0), [products]);

  const [filters, setFilters] = useState<ProductFiltersState>({
    category: 'all',
    priceRange: { min: 0, max: maxPrice || 50000 },
    inStock: false,
    onSale: false,
    featured: false,
    searchQuery: ''
  });

  // تحديث الفلاتر عند تغير السعر الأقصى أو عند تحميل الصفحة مع وجود معلمات URL
  useEffect(() => {
    setFilters(prevFilters => ({
      ...prevFilters,
      priceRange: {
        ...prevFilters.priceRange,
        max: maxPrice || 50000
      }
    }));
  }, [maxPrice]);

  // تحقق من وجود معلمات URL عند تحميل الصفحة
  useEffect(() => {
    // محاكاة قراءة معلمات URL (في التطبيق الحقيقي، استخدم router.query)
    const urlParams = new URLSearchParams(window.location.search);
    const featuredParam = urlParams.get('featured');

    if (featuredParam === 'true') {
      setFilters(prevFilters => ({
        ...prevFilters,
        featured: true
      }));
      setSortOption('featured');
    }
  }, []);

  // محاكاة تحميل البيانات
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);
    return () => clearTimeout(timer);
  }, []);

  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const { user } = useAuthStore();
  const { theme, resolvedTheme } = useTheme();
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();

  const currentLanguage = (locale as 'ar' | 'en') || language;
  const isRTL = currentLanguage === 'ar';

  // تصفية المنتجات حسب الفلاتر
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      if (filters.category !== 'all' && product.category !== filters.category) return false;
      if (filters.inStock && product.stock === 0) return false;
      if (filters.onSale && !product.compareAtPrice) return false;
      if (filters.featured && !product.featured) return false;
      if (product.price < filters.priceRange.min || product.price > filters.priceRange.max) return false;
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        return (
          product.name.toLowerCase().includes(query) ||
          product.description.toLowerCase().includes(query) ||
          product.category.toLowerCase().includes(query) ||
          product.tags.some(tag => tag.toLowerCase().includes(query))
        );
      }
      return true;
    });
  }, [filters]);

  // ترتيب المنتجات حسب الخيار المحدد
  const sortedProducts = useMemo(() => {
    let sorted = [...filteredProducts];

    switch (sortOption) {
      case 'featured':
        return sorted.sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0));
      case 'newest':
        return sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      case 'price-asc':
        return sorted.sort((a, b) => a.price - b.price);
      case 'price-desc':
        return sorted.sort((a, b) => b.price - a.price);
      case 'popular':
        return sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0));
      default:
        return sorted;
    }
  }, [filteredProducts, sortOption]);

  const handleUnauthenticated = () => {
    setShowAuthModal(true);
  };

  const handleAddToCart = useAuthenticatedAction((product: Product) => {
    cartStore.addItem(product, 1);
    // إظهار رسالة نجاح (يمكن تنفيذها باستخدام مكتبة toast)
    console.log(`${product.name} added to cart`);
  }, handleUnauthenticated);

  const handleWholesaleInquiry = useAuthenticatedAction((product: Product) => {
    setSelectedProduct(product);
    setShowWholesaleForm(true);
  }, handleUnauthenticated);

  const toggleWishlist = useAuthenticatedAction((product: Product) => {
    if (wishlistStore.isInWishlist(product.id)) {
      wishlistStore.removeItem(product.id);
    } else {
      wishlistStore.addItem(product);
    }
  }, handleUnauthenticated);

  const handleQuickView = (product: Product) => {
    setQuickViewProduct(product);
  };

  // Comparison functionality
  const toggleCompare = (productId: string) => {
    setCompareList(prev => {
      if (prev.includes(productId)) {
        return prev.filter(id => id !== productId);
      } else if (prev.length < 4) {
        return [...prev, productId];
      } else {
        // Replace the first item if we're at the limit
        return [...prev.slice(1), productId];
      }
    });
  };

  const clearComparison = () => {
    setCompareList([]);
  };

  const resetFilters = () => {
    setFilters({
      category: 'all',
      priceRange: { min: 0, max: maxPrice || 50000 },
      inStock: false,
      onSale: false,
      featured: false,
      searchQuery: ''
    });
    setSortOption('featured');
    setShowMobileFilters(false);
  };

  // تبديل وضع العرض (شبكة/قائمة)
  const toggleViewMode = () => {
    setViewMode(prev => prev === 'grid' ? 'list' : 'grid');
  };

  // التحقق من وجود منتجات مميزة
  const hasFeaturedProducts = useMemo(() => {
    return products.some(product => product.featured);
  }, [products]);

  // الحصول على المنتجات المميزة
  const featuredProducts = useMemo(() => {
    return products.filter(product => product.featured).slice(0, 4);
  }, [products]);

  return (
    <div className="container-custom py-8">
      {/* Hero Section */}
      <ScrollAnimation animation="fade" delay={0.1}>
        <div className="mb-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-slate-900 dark:text-white mb-4">
            {currentLanguage === 'ar' ? 'متجر ارتال' : 'ARTAL Shop'}
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto mb-8">
            {currentLanguage === 'ar'
              ? 'تسوق أحدث المنتجات عالية الجودة بأفضل الأسعار. شحن سريع وخدمة عملاء ممتازة.'
              : 'Shop the latest high-quality products at the best prices. Fast shipping and excellent customer service.'}
          </p>

          {/* Shop Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-4 shadow-md">
              <div className="flex items-center justify-center mb-2">
                <Package className="h-6 w-6 text-primary-500" />
              </div>
              <div className="text-2xl font-bold text-slate-900 dark:text-white">
                {products.length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar' ? 'منتج' : 'Products'}
              </div>
            </div>

            <div className="bg-white dark:bg-slate-800 rounded-xl p-4 shadow-md">
              <div className="flex items-center justify-center mb-2">
                <Award className="h-6 w-6 text-yellow-500" />
              </div>
              <div className="text-2xl font-bold text-slate-900 dark:text-white">
                {products.filter(p => p.featured).length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
              </div>
            </div>

            <div className="bg-white dark:bg-slate-800 rounded-xl p-4 shadow-md">
              <div className="flex items-center justify-center mb-2">
                <Layers className="h-6 w-6 text-blue-500" />
              </div>
              <div className="text-2xl font-bold text-slate-900 dark:text-white">
                {productCategories.length - 1}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar' ? 'فئة' : 'Categories'}
              </div>
            </div>

            <div className="bg-white dark:bg-slate-800 rounded-xl p-4 shadow-md">
              <div className="flex items-center justify-center mb-2">
                <Truck className="h-6 w-6 text-green-500" />
              </div>
              <div className="text-2xl font-bold text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'مجاني' : 'FREE'}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar' ? 'شحن' : 'Shipping'}
              </div>
            </div>
          </div>
        </div>
      </ScrollAnimation>

      {/* Featured Products Section */}
      {hasFeaturedProducts && (
        <ScrollAnimation animation="fade" delay={0.2} className="mb-12">
          <div className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-2xl p-6 md:p-8 relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-t-xl"></div>
            <div className="absolute -top-24 -right-24 w-48 h-48 bg-primary-300/20 dark:bg-primary-500/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-accent-300/20 dark:bg-accent-500/10 rounded-full blur-3xl"></div>

            <div className="flex flex-col md:flex-row justify-between items-center mb-6">
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-2 flex items-center">
                  <Tag className="mr-2 text-primary-500" size={24} />
                  {currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
                </h2>
                <p className="text-slate-600 dark:text-slate-300">
                  {currentLanguage === 'ar'
                    ? 'اكتشف منتجاتنا المميزة المختارة خصيصًا لك'
                    : 'Discover our featured products specially curated for you'}
                </p>
              </div>

              <Button
                variant="outline"
                className="mt-4 md:mt-0 border-primary-200 dark:border-primary-800 hover:bg-primary-50 dark:hover:bg-primary-900/30"
                onClick={() => setFilters(prev => ({ ...prev, featured: !prev.featured }))}
              >
                {filters.featured
                  ? (currentLanguage === 'ar' ? 'عرض جميع المنتجات' : 'Show All Products')
                  : (currentLanguage === 'ar' ? 'تصفية المنتجات المميزة فقط' : 'Filter Featured Only')}
                <Filter className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-4 w-4`} />
              </Button>
            </div>

            {/* Featured Products Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {featuredProducts.map((product, index) => (
                <Link
                  key={product.id}
                  href={`/${currentLanguage}/shop/${product.slug}`}
                  className="group bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden flex flex-col h-full transform hover:-translate-y-1"
                >
                  {/* Product Image */}
                  <div className="relative aspect-square overflow-hidden">
                    <EnhancedImage
                      src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                      alt={product.name}
                      fill={true}
                      objectFit="cover"
                      progressive={true}
                      placeholder="shimmer"
                      className="transition-transform duration-500 group-hover:scale-105"
                      sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 25vw"
                      priority={true}
                    />

                    {/* Sale Badge */}
                    {product.compareAtPrice && product.compareAtPrice > product.price && (
                      <div className="absolute top-2 right-2 z-10">
                        <span className="bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                          {`${Math.round((1 - product.price / product.compareAtPrice) * 100)}% ${currentLanguage === 'ar' ? 'خصم' : 'OFF'}`}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="p-3 flex flex-col flex-grow">
                    <h3 className="font-semibold text-slate-900 dark:text-white line-clamp-1 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                      {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                    </h3>

                    <div className="flex items-center justify-between mt-2">
                      <span className="font-bold text-primary-600 dark:text-primary-400">
                        {formatCurrency(product.price)}
                      </span>

                      <div className="flex items-center text-sm text-yellow-500">
                        <Star className="h-4 w-4 fill-current" />
                        <span className="ml-1 text-slate-600 dark:text-slate-300">
                          {product.rating?.toFixed(1) || '4.5'}
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {/* View All Featured Button */}
            <div className="mt-6 text-center">
              <Button
                variant="primary"
                className="px-6"
                onClick={() => setFilters(prev => ({ ...prev, featured: true }))}
              >
                {currentLanguage === 'ar' ? 'عرض جميع المنتجات المميزة' : 'View All Featured Products'}
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-4 w-4`} />
              </Button>
            </div>
          </div>
        </ScrollAnimation>
      )}

      {/* Comparison Bar */}
      {compareList.length > 0 && (
        <ScrollAnimation animation="slide" delay={0.1} className="mb-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center text-blue-600 dark:text-blue-400">
                  <Compare className="h-5 w-5 mr-2" />
                  <span className="font-medium">
                    {currentLanguage === 'ar'
                      ? `${compareList.length} منتج للمقارنة`
                      : `${compareList.length} products to compare`}
                  </span>
                </div>

                <div className="flex gap-2">
                  {compareList.slice(0, 3).map(productId => {
                    const product = products.find(p => p.id === productId);
                    return product ? (
                      <div key={productId} className="flex items-center bg-white dark:bg-slate-800 rounded-lg px-3 py-1 text-sm">
                        <span className="truncate max-w-24">{product.name}</span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="ml-1 h-4 w-4 p-0 text-slate-400 hover:text-red-500"
                          onClick={() => toggleCompare(productId)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ) : null;
                  })}
                  {compareList.length > 3 && (
                    <span className="text-blue-600 dark:text-blue-400 text-sm">
                      +{compareList.length - 3} {currentLanguage === 'ar' ? 'أكثر' : 'more'}
                    </span>
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => {
                    // Navigate to comparison page (implement this)
                    console.log('Compare products:', compareList);
                  }}
                >
                  {currentLanguage === 'ar' ? 'مقارنة' : 'Compare'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearComparison}
                >
                  {currentLanguage === 'ar' ? 'مسح' : 'Clear'}
                </Button>
              </div>
            </div>
          </div>
        </ScrollAnimation>
      )}

      {/* Shop Controls */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 bg-white dark:bg-slate-800 p-4 rounded-xl shadow-md">
        <div className="flex items-center mb-4 md:mb-0">
          <span className="text-slate-600 dark:text-slate-300 mr-2">
            {currentLanguage === 'ar'
              ? `${sortedProducts.length} منتج`
              : `${sortedProducts.length} Products`}
          </span>

          <div className="flex items-center ml-4 space-x-2">
            <button
              onClick={toggleViewMode}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400'
                  : 'bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300'
              }`}
              aria-label={viewMode === 'grid' ? 'Grid View' : 'List View'}
            >
              <Grid size={18} />
            </button>

            <button
              onClick={toggleViewMode}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400'
                  : 'bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300'
              }`}
              aria-label={viewMode === 'list' ? 'List View' : 'Grid View'}
            >
              <List size={18} />
            </button>
          </div>
        </div>

        <div className="relative">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => setShowSortDropdown(!showSortDropdown)}
          >
            <SlidersHorizontal size={16} />
            {currentLanguage === 'ar' ? 'ترتيب حسب' : 'Sort by'}:
            <span className="font-medium">
              {sortOption === 'featured' && (currentLanguage === 'ar' ? 'المميزة' : 'Featured')}
              {sortOption === 'newest' && (currentLanguage === 'ar' ? 'الأحدث' : 'Newest')}
              {sortOption === 'price-asc' && (currentLanguage === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High')}
              {sortOption === 'price-desc' && (currentLanguage === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low')}
              {sortOption === 'popular' && (currentLanguage === 'ar' ? 'الأكثر شعبية' : 'Most Popular')}
            </span>
            <ChevronDown size={16} className={`transition-transform ${showSortDropdown ? 'rotate-180' : ''}`} />
          </Button>

          {showSortDropdown && (
            <div className="absolute right-0 mt-2 w-56 bg-white dark:bg-slate-800 rounded-md shadow-lg z-20 border border-slate-200 dark:border-slate-700">
              <div className="py-1">
                <button
                  className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'featured' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                  onClick={() => {
                    setSortOption('featured');
                    setShowSortDropdown(false);
                  }}
                >
                  {currentLanguage === 'ar' ? 'المميزة' : 'Featured'}
                </button>
                <button
                  className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'newest' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                  onClick={() => {
                    setSortOption('newest');
                    setShowSortDropdown(false);
                  }}
                >
                  {currentLanguage === 'ar' ? 'الأحدث' : 'Newest'}
                </button>
                <button
                  className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'price-asc' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                  onClick={() => {
                    setSortOption('price-asc');
                    setShowSortDropdown(false);
                  }}
                >
                  {currentLanguage === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High'}
                </button>
                <button
                  className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'price-desc' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                  onClick={() => {
                    setSortOption('price-desc');
                    setShowSortDropdown(false);
                  }}
                >
                  {currentLanguage === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low'}
                </button>
                <button
                  className={`w-full text-left px-4 py-2 text-sm ${sortOption === 'popular' ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'}`}
                  onClick={() => {
                    setSortOption('popular');
                    setShowSortDropdown(false);
                  }}
                >
                  {currentLanguage === 'ar' ? 'الأكثر شعبية' : 'Most Popular'}
                </button>
                <div className="border-t border-slate-200 dark:border-slate-600 my-1"></div>
                <button
                  className={`w-full text-left px-4 py-2 text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 flex items-center`}
                  onClick={() => {
                    setSortOption('featured');
                    setFilters(prev => ({ ...prev, featured: true }));
                    setShowSortDropdown(false);
                  }}
                >
                  <Award className="h-4 w-4 mr-2" />
                  {currentLanguage === 'ar' ? 'المنتجات المميزة فقط' : 'Featured Only'}
                </button>
                <button
                  className={`w-full text-left px-4 py-2 text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 flex items-center`}
                  onClick={() => {
                    setFilters(prev => ({ ...prev, onSale: true }));
                    setShowSortDropdown(false);
                  }}
                >
                  <Tag className="h-4 w-4 mr-2" />
                  {currentLanguage === 'ar' ? 'العروض فقط' : 'On Sale Only'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar Filters */}
        <div className="lg:col-span-1">
          <div className="sticky top-24">
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-slate-900 dark:text-white mb-6">
                {currentLanguage === 'ar' ? 'تصفية المنتجات' : 'Filter Products'}
              </h2>

              {/* Search */}
              <div className="mb-6">
                <label htmlFor="search" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  {currentLanguage === 'ar' ? 'البحث' : 'Search'}
                </label>
                <div className="relative">
                  <Input
                    id="search"
                    type="text"
                    placeholder={currentLanguage === 'ar' ? 'ابحث عن منتج...' : 'Search products...'}
                    value={filters.searchQuery}
                    onChange={(e) => setFilters({ ...filters, searchQuery: e.target.value })}
                    className="pl-10 bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 border-slate-300 dark:border-slate-600"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400 dark:text-slate-500" />
                  {filters.searchQuery && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setFilters({ ...filters, searchQuery: '' })}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300"
                    >
                      <X size={18} />
                    </Button>
                  )}
                </div>
              </div>

              {/* Categories */}
              <div className="mb-6">
                <h3 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-3">
                  {currentLanguage === 'ar' ? 'الفئات' : 'Categories'}
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <input
                      id="category-all"
                      type="radio"
                      name="category"
                      checked={filters.category === 'all'}
                      onChange={() => setFilters({ ...filters, category: 'all' })}
                      className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 focus:ring-primary-500"
                    />
                    <label htmlFor="category-all" className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                      {currentLanguage === 'ar' ? 'جميع الفئات' : 'All Categories'}
                    </label>
                  </div>
                  {productCategories.map(category => (
                    <div key={category.id} className="flex items-center">
                      <input
                        id={`category-${category.id}`}
                        type="radio"
                        name="category"
                        checked={filters.category === category.id}
                        onChange={() => setFilters({ ...filters, category: category.id })}
                        className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 focus:ring-primary-500"
                      />
                      <label htmlFor={`category-${category.id}`} className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                        {category.name[currentLanguage]}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Price Range */}
              <div className="mb-6">
                <h3 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-3">
                  {currentLanguage === 'ar' ? 'نطاق السعر' : 'Price Range'}
                </h3>
                <div className="mb-2 flex justify-between text-sm text-slate-600 dark:text-slate-400">
                  <span>{formatCurrency(filters.priceRange.min)}</span>
                  <span>{formatCurrency(filters.priceRange.max)}</span>
                </div>
                <input
                  type="range"
                  id="priceRangeMin"
                  min="0"
                  max={maxPrice}
                  value={filters.priceRange.min}
                  onChange={(e) => setFilters({ ...filters, priceRange: { ...filters.priceRange, min: parseInt(e.target.value) } })}
                  className="w-full h-2 bg-slate-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer accent-primary-500 dark:accent-primary-400 mb-2"
                />
                <input
                  type="range"
                  id="priceRangeMax"
                  min="0"
                  max={maxPrice}
                  value={filters.priceRange.max}
                  onChange={(e) => setFilters({ ...filters, priceRange: { ...filters.priceRange, max: parseInt(e.target.value) } })}
                  className="w-full h-2 bg-slate-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer accent-primary-500 dark:accent-primary-400"
                />
              </div>

              {/* Availability Filters */}
              <div className="mb-6">
                <h3 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-3">
                  {currentLanguage === 'ar' ? 'التوفر' : 'Availability'}
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <input
                      id="inStock"
                      type="checkbox"
                      checked={filters.inStock}
                      onChange={(e) => setFilters({ ...filters, inStock: e.target.checked })}
                      className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500"
                    />
                    <label htmlFor="inStock" className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                      {currentLanguage === 'ar' ? 'متوفر في المخزون فقط' : 'In Stock Only'}
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="onSale"
                      type="checkbox"
                      checked={filters.onSale}
                      onChange={(e) => setFilters({ ...filters, onSale: e.target.checked })}
                      className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500"
                    />
                    <label htmlFor="onSale" className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                      {currentLanguage === 'ar' ? 'العروض فقط' : 'On Sale Only'}
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="featured"
                      type="checkbox"
                      checked={filters.featured}
                      onChange={(e) => setFilters({ ...filters, featured: e.target.checked })}
                      className="h-4 w-4 text-primary-600 border-slate-300 dark:border-slate-600 rounded focus:ring-primary-500"
                    />
                    <label htmlFor="featured" className="ml-2 text-sm text-slate-700 dark:text-slate-300">
                      {currentLanguage === 'ar' ? 'المنتجات المميزة فقط' : 'Featured Products Only'}
                    </label>
                  </div>
                </div>
              </div>

              {/* Reset Filters */}
              <Button
                variant="outline"
                onClick={resetFilters}
                className="w-full mt-4"
              >
                {currentLanguage === 'ar' ? 'إعادة تعيين الفلاتر' : 'Reset Filters'}
              </Button>
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="lg:col-span-3">
          {isLoading ? (
            <div className="flex items-center justify-center py-20">
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
                <p className="mt-4 text-slate-600 dark:text-slate-300">
                  {currentLanguage === 'ar' ? 'جاري تحميل المنتجات...' : 'Loading products...'}
                </p>
              </div>
            </div>
          ) : viewMode === 'grid' ? (
            <ScrollStagger className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {sortedProducts.map((product, index) => (
              <div key={product.id} className="group relative overflow-hidden rounded-xl bg-white dark:bg-slate-800 shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full transform hover:-translate-y-1">
                {/* Professional Product Badges */}
                <ProductBadge product={product} language={currentLanguage} />

                {/* صورة المنتج مع رابط للتفاصيل */}
                <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block relative overflow-hidden">
                  <div className="relative w-full aspect-square overflow-hidden">
                    <EnhancedImage
                      src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                      alt={product.name}
                      fill={true}
                      objectFit="cover"
                      progressive={true}
                      placeholder="shimmer"
                      className="transition-transform duration-500 group-hover:scale-105"
                      sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                      priority={index < 4}
                    />
                  </div>

                  {/* أزرار الإجراءات السريعة */}
                  <div className="absolute top-12 right-3 flex flex-col gap-2 z-10 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
                    {/* زر المفضلة */}
                    <Button
                      variant="icon"
                      size="sm"
                      className={`p-2 rounded-full shadow-lg transform transition-all duration-300 hover:scale-110 backdrop-blur-sm ${
                        user && useWishlistStore.getState().isInWishlist(product.id)
                          ? "bg-red-500 text-white"
                          : "bg-white/90 text-slate-700 dark:bg-slate-800/90 dark:text-white hover:bg-red-50 dark:hover:bg-red-900/20"
                      }`}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleWishlist(product);
                      }}
                      aria-label={currentLanguage === 'ar' ? 'إضافة إلى المفضلة' : 'Add to wishlist'}
                    >
                      <Heart
                        className={`h-4 w-4 ${user && useWishlistStore.getState().isInWishlist(product.id) && "fill-current"}`}
                      />
                    </Button>

                    {/* زر المقارنة */}
                    <Button
                      variant="icon"
                      size="sm"
                      className={`p-2 rounded-full shadow-lg transform transition-all duration-300 hover:scale-110 backdrop-blur-sm ${
                        compareList.includes(product.id)
                          ? "bg-blue-500 text-white"
                          : "bg-white/90 text-slate-700 dark:bg-slate-800/90 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20"
                      }`}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleCompare(product.id);
                      }}
                      aria-label={currentLanguage === 'ar' ? 'إضافة للمقارنة' : 'Add to compare'}
                    >
                      <Compare className="h-4 w-4" />
                    </Button>

                    {/* زر النظرة السريعة */}
                    <Button
                      variant="icon"
                      size="sm"
                      className="p-2 rounded-full shadow-lg transform transition-all duration-300 hover:scale-110 backdrop-blur-sm bg-white/90 text-slate-700 dark:bg-slate-800/90 dark:text-white hover:bg-primary-50 dark:hover:bg-primary-900/20"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleQuickView(product);
                      }}
                      aria-label={currentLanguage === 'ar' ? 'نظرة سريعة' : 'Quick view'}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </Link>

                {/* معلومات المنتج */}
                <div className="p-4 flex flex-col flex-grow">
                  {/* التصنيف والتقييم */}
                  <div className="flex items-center justify-between mb-2">
                    {product.category && (
                      <span className="text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full">
                        {product.category}
                      </span>
                    )}

                    <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
                      <Star className={`h-4 w-4 text-yellow-400 ${currentLanguage === 'ar' ? 'ml-1' : 'mr-1'}`} />
                      <span>
                        {product.rating?.toFixed(1) ?? 'N/A'}
                        {product.reviewCount ? `(${product.reviewCount})` : ''}
                      </span>
                    </div>
                  </div>

                  {/* اسم المنتج */}
                  <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-1 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 line-clamp-1">
                      {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                    </h3>
                  </Link>

                  {/* وصف المنتج */}
                  <p className="text-slate-600 dark:text-slate-300 mb-4 line-clamp-2 text-sm">
                    {currentLanguage === 'ar'
                      ? (product.description_ar || product.description)
                      : product.description}
                  </p>

                  {/* السعر والمخزون */}
                  <div className="flex flex-col gap-2 mb-3 mt-auto">
                    <div className="flex items-baseline gap-2">
                      <span className="text-xl font-bold text-primary-600 dark:text-primary-400">
                        {formatCurrency(product.price)}
                      </span>
                      {product.compareAtPrice && product.compareAtPrice > product.price && (
                        <>
                          <span className="text-sm text-slate-500 line-through">
                            {formatCurrency(product.compareAtPrice)}
                          </span>
                          <span className="text-xs bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 px-2 py-0.5 rounded-full font-medium">
                            {Math.round((1 - product.price / product.compareAtPrice) * 100)}% {currentLanguage === 'ar' ? 'خصم' : 'OFF'}
                          </span>
                        </>
                      )}
                    </div>

                    {/* Stock Status */}
                    <StockStatus product={product} language={currentLanguage} />
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="flex gap-2">
                    {/* زر إضافة إلى السلة */}
                    <Button
                      variant="primary"
                      size="sm"
                      className="flex-1 rounded-md"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleAddToCart(product);
                      }}
                      disabled={product.stock <= 0}
                      aria-label={currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to cart'}
                    >
                      <ShoppingCart className={`h-4 w-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
                      <span>{currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}</span>
                    </Button>

                    {/* زر طلب عرض سعر للجملة */}
                    <Button
                      variant="outline"
                      size="sm"
                      className="rounded-md flex items-center gap-1"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleWholesaleInquiry(product);
                      }}
                      aria-label={currentLanguage === 'ar' ? 'طلب عرض سعر جملة' : 'Request wholesale quote'}
                    >
                      <span>{currentLanguage === 'ar' ? 'طلب سعر جملة' : 'Wholesale'}</span>
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </ScrollStagger>

          )) : (
            // List View
            <div className="space-y-4">
              {sortedProducts.map((product, index) => (
                <div key={product.id} className="group relative overflow-hidden rounded-xl bg-white dark:bg-slate-800 shadow-lg hover:shadow-xl transition-all duration-300 flex flex-row h-full">
                  {/* Product Image */}
                  <div className="relative w-1/3 overflow-hidden">
                    <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
                      <div className="relative aspect-square overflow-hidden">
                        <EnhancedImage
                          src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                          alt={product.name}
                          fill={true}
                          objectFit="cover"
                          progressive={true}
                          placeholder="shimmer"
                          className="transition-transform duration-500 group-hover:scale-105"
                          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
                          priority={index < 2}
                        />
                      </div>
                    </Link>

                    {/* Featured Badge */}
                    {product.featured && (
                      <div className="absolute top-3 left-3 z-10">
                        <span className="bg-amber-500 text-white text-xs font-bold px-2.5 py-1 rounded-full">
                          {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                        </span>
                      </div>
                    )}

                    {/* Sale Badge */}
                    {product.compareAtPrice && product.compareAtPrice > product.price && (
                      <div className="absolute top-3 right-3 z-10">
                        <span className="bg-red-500 text-white text-xs font-bold px-2.5 py-1 rounded-full">
                          {`${Math.round((1 - product.price / product.compareAtPrice) * 100)}% ${currentLanguage === 'ar' ? 'خصم' : 'OFF'}`}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="w-2/3 p-6 flex flex-col">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
                          <h3 className="text-xl font-semibold text-slate-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
                            {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                          </h3>
                        </Link>

                        <div className="flex items-center mt-1 mb-3">
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            <Star className="h-4 w-4 text-slate-300 dark:text-slate-600" />
                            <span className="ml-2 text-sm text-slate-600 dark:text-slate-400">
                              {product.rating?.toFixed(1) || '4.5'}
                              {product.reviewCount ? ` (${product.reviewCount})` : ''}
                            </span>
                          </div>

                          {product.category && (
                            <span className="ml-4 text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full">
                              {product.category}
                            </span>
                          )}
                        </div>
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        className={`rounded-full p-2 ${
                          user && useWishlistStore.getState().isInWishlist(product.id)
                            ? "text-primary-500 bg-primary-50 dark:bg-primary-900/30"
                            : "text-slate-400 hover:text-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900/30"
                        }`}
                        onClick={() => toggleWishlist(product)}
                        aria-label={currentLanguage === 'ar' ? 'إضافة إلى المفضلة' : 'Add to wishlist'}
                      >
                        <Heart
                          className={`h-5 w-5 ${user && useWishlistStore.getState().isInWishlist(product.id) && "fill-current"}`}
                        />
                      </Button>
                    </div>

                    <p className="text-slate-600 dark:text-slate-300 mb-4 line-clamp-2">
                      {currentLanguage === 'ar'
                        ? (product.description_ar || product.description)
                        : product.description}
                    </p>

                    <div className="mt-auto flex flex-wrap items-center justify-between gap-4">
                      <div className="flex items-baseline gap-2">
                        <span className="text-xl font-bold text-primary-600 dark:text-primary-400">
                          {formatCurrency(product.price)}
                        </span>
                        {product.compareAtPrice && product.compareAtPrice > product.price && (
                          <span className="text-sm text-slate-500 line-through">
                            {formatCurrency(product.compareAtPrice)}
                          </span>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="primary"
                          size="sm"
                          className="rounded-md"
                          onClick={() => handleAddToCart(product)}
                          disabled={product.stock <= 0}
                        >
                          <ShoppingCart className="h-4 w-4 mr-1" />
                          {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          className="rounded-md"
                          onClick={() => handleQuickView(product)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          {currentLanguage === 'ar' ? 'نظرة سريعة' : 'Quick View'}
                        </Button>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-slate-100 dark:border-slate-700 flex items-center gap-4 text-xs text-slate-500 dark:text-slate-400">
                      <div className="flex items-center">
                        <Truck className="h-3.5 w-3.5 mr-1" />
                        {currentLanguage === 'ar' ? 'شحن مجاني' : 'Free Shipping'}
                      </div>
                      <div className="flex items-center">
                        <Package className="h-3.5 w-3.5 mr-1" />
                        {product.stock > 10
                          ? (currentLanguage === 'ar' ? 'متوفر بكثرة' : 'In Stock')
                          : product.stock > 0
                            ? (currentLanguage === 'ar' ? `${product.stock} متبقية` : `${product.stock} left`)
                            : (currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock')}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* No Products Found */}
          {sortedProducts.length === 0 && (
            <ScrollAnimation animation="fade" delay={0.2}>
              <div className="text-center py-12">
                <div className="inline-flex justify-center items-center w-16 h-16 rounded-full bg-slate-100 dark:bg-slate-800 text-slate-400 dark:text-slate-500 mb-4">
                  <X size={24} />
                </div>
                <h2 className="text-xl font-medium text-slate-900 dark:text-white mb-2">
                  {currentLanguage === 'ar' ? 'لم يتم العثور على منتجات' : 'No products found'}
                </h2>
                <p className="text-slate-600 dark:text-slate-300 mb-6">
                  {currentLanguage === 'ar'
                    ? 'حاول تعديل البحث أو الفلاتر للعثور على ما تبحث عنه.'
                    : 'Try adjusting your search or filter to find what you\'re looking for.'}
                </p>
                <HoverAnimation animation="scale">
                  <Button variant="outline" onClick={resetFilters}>
                    {currentLanguage === 'ar' ? 'إعادة تعيين الفلاتر' : 'Reset Filters'}
                  </Button>
                </HoverAnimation>
              </div>
            </ScrollAnimation>
          )}

        </div>

        {showAuthModal && (
          <AuthModal onClose={() => setShowAuthModal(false)} />
        )}

        {showWholesaleForm && selectedProduct && (
          <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <ScrollAnimation animation="scale" duration={0.3}>
              <div className="max-w-2xl w-full">
                <WholesaleQuoteForm
                  product={selectedProduct}
                  onClose={() => setShowWholesaleForm(false)}
                />
              </div>
            </ScrollAnimation>
          </div>
        )}

        {/* Quick View Modal */}
        {quickViewProduct && (
          <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
                    {currentLanguage === 'ar' ? (quickViewProduct.name_ar || quickViewProduct.name) : quickViewProduct.name}
                  </h2>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setQuickViewProduct(null)}
                    className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
                  >
                    <X size={24} />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Product Image */}
                  <div className="relative aspect-square overflow-hidden rounded-lg">
                    <EnhancedImage
                      src={quickViewProduct.images && quickViewProduct.images.length > 0 ? quickViewProduct.images[0] : '/images/product-placeholder-light.svg'}
                      alt={quickViewProduct.name}
                      fill={true}
                      objectFit="cover"
                      progressive={true}
                      placeholder="shimmer"
                      className="rounded-lg"
                      sizes="(max-width: 768px) 100vw, 50vw"
                    />

                    {/* Featured Badge */}
                    {quickViewProduct.featured && (
                      <div className="absolute top-3 left-3 z-10">
                        <span className="bg-amber-500 text-white text-xs font-bold px-2.5 py-1 rounded-full">
                          {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                        </span>
                      </div>
                    )}

                    {/* Sale Badge */}
                    {quickViewProduct.compareAtPrice && quickViewProduct.compareAtPrice > quickViewProduct.price && (
                      <div className="absolute top-3 right-3 z-10">
                        <span className="bg-red-500 text-white text-xs font-bold px-2.5 py-1 rounded-full">
                          {`${Math.round((1 - quickViewProduct.price / quickViewProduct.compareAtPrice) * 100)}% ${currentLanguage === 'ar' ? 'خصم' : 'OFF'}`}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="flex flex-col">
                    <div className="mb-4">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="flex items-center">
                          <Star className="h-5 w-5 text-yellow-400 fill-current" />
                          <Star className="h-5 w-5 text-yellow-400 fill-current" />
                          <Star className="h-5 w-5 text-yellow-400 fill-current" />
                          <Star className="h-5 w-5 text-yellow-400 fill-current" />
                          <Star className="h-5 w-5 text-slate-300 dark:text-slate-600" />
                          <span className="ml-2 text-sm text-slate-600 dark:text-slate-400">
                            {quickViewProduct.rating?.toFixed(1) || '4.5'}
                            {quickViewProduct.reviewCount ? ` (${quickViewProduct.reviewCount})` : ''}
                          </span>
                        </div>

                        {quickViewProduct.category && (
                          <span className="text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full">
                            {quickViewProduct.category}
                          </span>
                        )}
                      </div>

                      <div className="flex items-baseline gap-2 mb-4">
                        <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                          {formatCurrency(quickViewProduct.price)}
                        </span>
                        {quickViewProduct.compareAtPrice && quickViewProduct.compareAtPrice > quickViewProduct.price && (
                          <span className="text-lg text-slate-500 line-through">
                            {formatCurrency(quickViewProduct.compareAtPrice)}
                          </span>
                        )}
                      </div>

                      <p className="text-slate-600 dark:text-slate-300 mb-6">
                        {currentLanguage === 'ar'
                          ? (quickViewProduct.description_ar || quickViewProduct.description)
                          : quickViewProduct.description}
                      </p>

                      <div className="flex items-center gap-4 text-sm text-slate-500 dark:text-slate-400 mb-6">
                        <div className="flex items-center">
                          <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                          {currentLanguage === 'ar' ? 'ضمان الجودة' : 'Quality Guaranteed'}
                        </div>
                        <div className="flex items-center">
                          <Truck className="h-4 w-4 mr-1" />
                          {currentLanguage === 'ar' ? 'شحن مجاني' : 'Free Shipping'}
                        </div>
                      </div>
                    </div>

                    <div className="mt-auto space-y-4">
                      <div className="flex items-center justify-between pb-4 border-b border-slate-200 dark:border-slate-700">
                        <span className="font-medium text-slate-700 dark:text-slate-300">
                          {currentLanguage === 'ar' ? 'الحالة:' : 'Status:'}
                        </span>
                        <span className={`font-medium ${quickViewProduct.stock > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                          {quickViewProduct.stock > 0
                            ? (currentLanguage === 'ar' ? 'متوفر في المخزون' : 'In Stock')
                            : (currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock')}
                        </span>
                      </div>

                      <div className="flex gap-4">
                        <Button
                          variant="primary"
                          className="flex-1"
                          onClick={() => {
                            handleAddToCart(quickViewProduct);
                            setQuickViewProduct(null);
                          }}
                          disabled={quickViewProduct.stock <= 0}
                        >
                          <ShoppingCart className="h-5 w-5 mr-2" />
                          {currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}
                        </Button>

                        <Button
                          variant={user && useWishlistStore.getState().isInWishlist(quickViewProduct.id) ? 'primary' : 'outline'}
                          onClick={() => toggleWishlist(quickViewProduct)}
                        >
                          <Heart className={`h-5 w-5 ${user && useWishlistStore.getState().isInWishlist(quickViewProduct.id) && 'fill-current'}`} />
                        </Button>
                      </div>

                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => {
                          router.push(`/${currentLanguage}/shop/${quickViewProduct.slug}`);
                          setQuickViewProduct(null);
                        }}
                      >
                        {currentLanguage === 'ar' ? 'عرض تفاصيل المنتج' : 'View Product Details'}
                        <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-4 w-4`} />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ShopPage;