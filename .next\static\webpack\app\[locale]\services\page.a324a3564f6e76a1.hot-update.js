"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/services/page",{

/***/ "(app-pages-browser)/./src/pages/services/ServicesPageSimple.tsx":
/*!***************************************************!*\
  !*** ./src/pages/services/ServicesPageSimple.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServicesPageEnhanced)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,ClipboardList,Clock,FileCheck,Mail,MapPin,Package,Phone,Search,Star,Truck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_forms_ServiceBookingForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/forms/ServiceBookingForm */ \"(app-pages-browser)/./src/components/forms/ServiceBookingForm.tsx\");\n/* harmony import */ var _data_services__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../data/services */ \"(app-pages-browser)/./src/data/services.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../stores/themeStore */ \"(app-pages-browser)/./src/stores/themeStore.ts\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Icon mapping for services\nconst icons = {\n    Search: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    Package: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    Truck: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    FileCheck: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n    Users: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n    ClipboardList: _barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n};\nfunction ServicesPageEnhanced() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_8__.useThemeStore)();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_9__.useLanguageStore)();\n    // State management\n    const [showBookingForm, setShowBookingForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedService, setSelectedService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        searchQuery: '',\n        category: 'all',\n        sortBy: 'name'\n    });\n    // Service categories for filtering\n    const categories = [\n        {\n            id: 'all',\n            name: language === 'ar' ? 'جميع الخدمات' : 'All Services'\n        },\n        {\n            id: 'inspection',\n            name: language === 'ar' ? 'خدمات الفحص' : 'Inspection Services'\n        },\n        {\n            id: 'storage',\n            name: language === 'ar' ? 'حلول التخزين' : 'Storage Solutions'\n        },\n        {\n            id: 'shipping',\n            name: language === 'ar' ? 'الشحن والخدمات اللوجستية' : 'Shipping & Logistics'\n        },\n        {\n            id: 'order-management',\n            name: language === 'ar' ? 'إدارة الطلبات' : 'Order Management'\n        },\n        {\n            id: 'certification',\n            name: language === 'ar' ? 'شهادات المنتجات' : 'Product Certification'\n        },\n        {\n            id: 'consulting',\n            name: language === 'ar' ? 'الاستشارات التجارية' : 'Business Consulting'\n        }\n    ];\n    // Filter and sort services\n    const filteredServices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ServicesPageEnhanced.useMemo[filteredServices]\": ()=>{\n            let filtered = _data_services__WEBPACK_IMPORTED_MODULE_7__.services.filter({\n                \"ServicesPageEnhanced.useMemo[filteredServices].filtered\": (service)=>{\n                    const matchesSearch = filters.searchQuery === '' || service.name.toLowerCase().includes(filters.searchQuery.toLowerCase()) || service.name_ar && service.name_ar.includes(filters.searchQuery) || service.description.toLowerCase().includes(filters.searchQuery.toLowerCase()) || service.description_ar && service.description_ar.includes(filters.searchQuery);\n                    const matchesCategory = filters.category === 'all' || service.id === filters.category;\n                    return matchesSearch && matchesCategory;\n                }\n            }[\"ServicesPageEnhanced.useMemo[filteredServices].filtered\"]);\n            // Sort services\n            filtered.sort({\n                \"ServicesPageEnhanced.useMemo[filteredServices]\": (a, b)=>{\n                    switch(filters.sortBy){\n                        case 'name':\n                            const nameA = language === 'ar' ? a.name_ar || a.name : a.name;\n                            const nameB = language === 'ar' ? b.name_ar || b.name : b.name;\n                            return nameA.localeCompare(nameB);\n                        case 'popularity':\n                            return b.id.localeCompare(a.id); // Mock popularity sort\n                        case 'recent':\n                            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                        default:\n                            return 0;\n                    }\n                }\n            }[\"ServicesPageEnhanced.useMemo[filteredServices]\"]);\n            return filtered;\n        }\n    }[\"ServicesPageEnhanced.useMemo[filteredServices]\"], [\n        _data_services__WEBPACK_IMPORTED_MODULE_7__.services,\n        filters,\n        language\n    ]);\n    // Event handlers\n    const handleBookService = (serviceName)=>{\n        setSelectedService(serviceName);\n        setShowBookingForm(true);\n    };\n    const clearFilters = ()=>{\n        setFilters({\n            searchQuery: '',\n            category: 'all',\n            sortBy: 'name'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"relative py-16 overflow-hidden transition-colors duration-500\", isDarkMode ? \"bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900\" : \"bg-gradient-to-br from-slate-50 via-white to-slate-100\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"absolute inset-0 opacity-5 transition-opacity duration-500\", isDarkMode ? \"opacity-10\" : \"opacity-5\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:50px_50px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"text-4xl md:text-5xl font-bold leading-tight tracking-tight mb-6 animate-fade-in\", isDarkMode ? \"text-white\" : \"text-slate-900\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block\",\n                                            children: language === 'ar' ? 'خدمات' : 'Business'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"block bg-gradient-to-r bg-clip-text text-transparent\", isDarkMode ? \"from-primary-400 to-blue-400\" : \"from-primary-600 to-blue-600\"),\n                                            children: language === 'ar' ? 'الأعمال' : 'Services'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"text-lg md:text-xl leading-relaxed max-w-3xl mx-auto mb-8 animate-fade-in-delay-1\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\"),\n                                    children: language === 'ar' ? 'خدمات دعم شاملة لتبسيط عملياتك وتعزيز كفاءة أعمالك مع حلول متطورة ومخصصة.' : 'Comprehensive support services to streamline your operations and enhance business efficiency with advanced, tailored solutions.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 animate-fade-in-delay-2\",\n                                    children: [\n                                        {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 25\n                                            }, this),\n                                            value: '6+',\n                                            label: language === 'ar' ? 'خدمة متخصصة' : 'Expert Services'\n                                        },\n                                        {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 25\n                                            }, this),\n                                            value: '500+',\n                                            label: language === 'ar' ? 'عميل راضي' : 'Satisfied Clients'\n                                        },\n                                        {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 25\n                                            }, this),\n                                            value: '24/48h',\n                                            label: language === 'ar' ? 'وقت الاستجابة' : 'Response Time'\n                                        },\n                                        {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 25\n                                            }, this),\n                                            value: '50+',\n                                            label: language === 'ar' ? 'دولة' : 'Countries'\n                                        }\n                                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"p-4 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg\", isDarkMode ? \"bg-slate-800/50 hover:bg-slate-800/70\" : \"bg-white/50 hover:bg-white/70\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"flex items-center justify-center w-8 h-8 rounded-full mx-auto mb-2\", isDarkMode ? \"bg-primary-500/20 text-primary-400\" : \"bg-primary-100 text-primary-600\"),\n                                                    children: stat.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"text-lg font-bold mb-1\", isDarkMode ? \"text-white\" : \"text-slate-900\"),\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"text-xs font-medium\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\"),\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row justify-center gap-4 animate-fade-in-delay-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"lg\",\n                                            variant: \"primary\",\n                                            className: \"px-8 py-3 text-lg font-semibold group transition-all duration-300 hover:scale-105\",\n                                            onClick: ()=>router.push(\"/\".concat(language, \"/contact\")),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"\".concat(language === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5 group-hover:scale-110 transition-transform\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this),\n                                                language === 'ar' ? 'استشارة مجانية' : 'Free Consultation'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            className: \"px-8 py-3 text-lg font-semibold group transition-all duration-300 hover:scale-105\",\n                                            onClick: ()=>{\n                                                var _document_getElementById;\n                                                return (_document_getElementById = document.getElementById('services-grid')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                    behavior: 'smooth'\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"\".concat(language === 'ar' ? 'mr-2 rotate-180' : 'ml-2', \" h-5 w-5 group-hover:translate-x-1 transition-transform\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                language === 'ar' ? 'استكشف الخدمات' : 'Explore Services'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"py-12\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-50\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"absolute top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400\", language === 'ar' ? \"right-4\" : \"left-4\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        type: \"text\",\n                                        placeholder: language === 'ar' ? 'ابحث عن الخدمات...' : 'Search services...',\n                                        value: filters.searchQuery,\n                                        onChange: (e)=>setFilters((prev)=>({\n                                                    ...prev,\n                                                    searchQuery: e.target.value\n                                                })),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"w-full h-12 text-lg transition-all duration-300 focus:ring-2 focus:ring-primary-500\", language === 'ar' ? \"pr-12 pl-4\" : \"pl-12 pr-4\", isDarkMode ? \"bg-slate-700 border-slate-600 text-white\" : \"bg-white border-slate-300\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"block text-sm font-medium mb-2\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\"),\n                                                children: language === 'ar' ? 'الفئة' : 'Category'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.category,\n                                                onChange: (e)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            category: e.target.value\n                                                        })),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"w-full h-10 px-3 rounded-md border transition-all duration-300 focus:ring-2 focus:ring-primary-500\", isDarkMode ? \"bg-slate-700 border-slate-600 text-white\" : \"bg-white border-slate-300\"),\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category.id,\n                                                        children: category.name\n                                                    }, category.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"block text-sm font-medium mb-2\", isDarkMode ? \"text-slate-300\" : \"text-slate-700\"),\n                                                children: language === 'ar' ? 'ترتيب حسب' : 'Sort by'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.sortBy,\n                                                onChange: (e)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            sortBy: e.target.value\n                                                        })),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"w-full h-10 px-3 rounded-md border transition-all duration-300 focus:ring-2 focus:ring-primary-500\", isDarkMode ? \"bg-slate-700 border-slate-600 text-white\" : \"bg-white border-slate-300\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name\",\n                                                        children: language === 'ar' ? 'الاسم' : 'Name'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"popularity\",\n                                                        children: language === 'ar' ? 'الشعبية' : 'Popularity'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"recent\",\n                                                        children: language === 'ar' ? 'الأحدث' : 'Most Recent'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: clearFilters,\n                                            variant: \"outline\",\n                                            className: \"h-10 px-4 transition-all duration-300 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 19\n                                                }, this),\n                                                language === 'ar' ? 'مسح' : 'Clear'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"text-sm mb-6 p-3 rounded-lg\", isDarkMode ? \"bg-slate-700 text-slate-300\" : \"bg-white text-slate-600\"),\n                                children: [\n                                    language === 'ar' ? \"عرض \".concat(filteredServices.length, \" من \").concat(_data_services__WEBPACK_IMPORTED_MODULE_7__.services.length, \" خدمة\") : \"Showing \".concat(filteredServices.length, \" of \").concat(_data_services__WEBPACK_IMPORTED_MODULE_7__.services.length, \" services\"),\n                                    filters.searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: language === 'ar' ? 'للبحث \"'.concat(filters.searchQuery, '\"') : 'for \"'.concat(filters.searchQuery, '\"')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"services-grid\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"py-16 md:py-24\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-3xl mx-auto text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\",\n                                    children: language === 'ar' ? 'عروض خدماتنا' : 'Our Service Offerings'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-slate-600 dark:text-slate-300\",\n                                    children: language === 'ar' ? 'اكتشف مجموعة كاملة من خدمات الأعمال المصممة لدعم عملياتك في كل مرحلة.' : 'Discover the full range of business services designed to support your operations at every stage.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this),\n                        filteredServices.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: filteredServices.map((service, index)=>{\n                                const Icon = icons[service.icon];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group animate-fade-in-stagger\",\n                                    style: {\n                                        animationDelay: \"\".concat(index * 0.1, \"s\")\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"group hover:shadow-xl transition-all duration-300 h-full hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"text-center pb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"p-4 rounded-full transition-all duration-300 group-hover:scale-110\", isDarkMode ? \"bg-primary-500/20\" : \"bg-primary-50\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                size: 32,\n                                                                className: \"text-primary-500 dark:text-primary-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-xl mb-2 text-slate-900 dark:text-white\",\n                                                        children: language === 'ar' ? service.name_ar || service.name : service.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"flex flex-col h-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600 dark:text-slate-300 mb-6 flex-grow\",\n                                                        children: language === 'ar' ? service.description_ar || service.description : service.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: [\n                                                                (language === 'ar' ? service.features_ar || service.features : service.features).slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"text-xs px-2 py-1 rounded-full\", isDarkMode ? \"bg-slate-700 text-slate-300\" : \"bg-slate-100 text-slate-600\"),\n                                                                        children: feature\n                                                                    }, index, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 33\n                                                                    }, this)),\n                                                                service.features.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"text-xs px-2 py-1 rounded-full\", isDarkMode ? \"bg-primary-500/20 text-primary-400\" : \"bg-primary-50 text-primary-600\"),\n                                                                    children: [\n                                                                        \"+\",\n                                                                        service.features.length - 3,\n                                                                        \" \",\n                                                                        language === 'ar' ? 'المزيد' : 'more'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 mt-auto\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: ()=>handleBookService(language === 'ar' ? service.name_ar || service.name : service.name),\n                                                                className: \"flex-1 group transition-all duration-300 hover:scale-105\",\n                                                                variant: \"primary\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2 group-hover:scale-110 transition-transform\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    language === 'ar' ? 'احجز الخدمة' : 'Book Service'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: ()=>router.push(\"/\".concat(language, \"/services/\").concat(service.slug)),\n                                                                variant: \"outline\",\n                                                                className: \"flex-1 group transition-all duration-300 hover:scale-105\",\n                                                                children: [\n                                                                    language === 'ar' ? 'معرفة المزيد' : 'Learn More',\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"\".concat(language === 'ar' ? 'mr-2 rotate-180' : 'ml-2', \" h-4 w-4 transition-transform group-hover:translate-x-1\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                        lineNumber: 440,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 21\n                                    }, this)\n                                }, service.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16 animate-fade-in\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-6\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-100\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-12 w-12 text-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-slate-900 dark:text-white mb-2\",\n                                    children: language === 'ar' ? 'لم يتم العثور على خدمات' : 'No Services Found'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 dark:text-slate-300 mb-6\",\n                                    children: language === 'ar' ? 'جرب تعديل معايير البحث أو الفلاتر للعثور على ما تبحث عنه.' : 'Try adjusting your search criteria or filters to find what you\\'re looking for.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: clearFilters,\n                                    variant: \"outline\",\n                                    className: \"transition-all duration-300 hover:scale-105\",\n                                    children: language === 'ar' ? 'مسح جميع الفلاتر' : 'Clear All Filters'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"py-16 md:py-24\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-50\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-3xl mx-auto text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\",\n                                    children: language === 'ar' ? 'كيف نعمل' : 'How We Work'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-slate-600 dark:text-slate-300\",\n                                    children: language === 'ar' ? 'نهجنا الاستشاري يضمن حلولاً مخصصة لاحتياجات عملك الفريدة.' : 'Our consultative approach ensures tailored solutions for your unique business needs.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                {\n                                    number: \"01\",\n                                    title: language === 'ar' ? \"الاستشارة\" : \"Consultation\",\n                                    description: language === 'ar' ? \"نبدأ باستشارة شاملة لفهم احتياجات وتحديات عملك.\" : \"We begin with a thorough consultation to understand your business needs and challenges.\"\n                                },\n                                {\n                                    number: \"02\",\n                                    title: language === 'ar' ? \"التحليل\" : \"Analysis\",\n                                    description: language === 'ar' ? \"يقوم خبراؤنا بتحليل متطلباتك وتطوير توصيات خدمة مخصصة.\" : \"Our experts analyze your requirements and develop customized service recommendations.\"\n                                },\n                                {\n                                    number: \"03\",\n                                    title: language === 'ar' ? \"التنفيذ\" : \"Implementation\",\n                                    description: language === 'ar' ? \"نقوم بتنفيذ الخدمات المتفق عليها مع الاهتمام بالتفاصيل وضمان الجودة.\" : \"We implement the agreed services with attention to detail and quality assurance.\"\n                                },\n                                {\n                                    number: \"04\",\n                                    title: language === 'ar' ? \"الدعم المستمر\" : \"Ongoing Support\",\n                                    description: language === 'ar' ? \"المراقبة والدعم المستمر يضمنان النتائج المثلى والقدرة على التكيف.\" : \"Continuous monitoring and support ensure optimal results and adaptability.\"\n                                }\n                            ].map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative text-center group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-primary-500 text-white rounded-full h-12 w-12 flex items-center justify-center font-bold text-lg mb-4 mx-auto transition-all duration-300 group-hover:scale-110\",\n                                            children: step.number\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"p-6 rounded-lg shadow-sm h-full transition-all duration-300 group-hover:shadow-lg group-hover:scale-105\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-2 text-slate-900 dark:text-white\",\n                                                    children: step.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 dark:text-slate-300\",\n                                                    children: step.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 474,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"py-16 md:py-24\", isDarkMode ? \"bg-slate-900\" : \"bg-white\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-3xl mx-auto text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4\",\n                                    children: language === 'ar' ? 'الأسئلة الشائعة' : 'Frequently Asked Questions'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-slate-600 dark:text-slate-300\",\n                                    children: language === 'ar' ? 'ابحث عن إجابات للأسئلة الشائعة حول خدمات أعمالنا.' : 'Find answers to common questions about our business services.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto space-y-6\",\n                            children: [\n                                {\n                                    question: language === 'ar' ? \"ما هي أنواع الشركات التي تخدمونها؟\" : \"What types of businesses do you serve?\",\n                                    answer: language === 'ar' ? \"نخدم مجموعة واسعة من الشركات عبر مختلف الصناعات، بما في ذلك التصنيع والتجزئة والتوزيع والتجارة الإلكترونية. خدماتنا قابلة للتطوير ويمكن تخصيصها لتلبية احتياجات كل من الشركات الصغيرة والمؤسسات الكبيرة.\" : \"We serve a wide range of businesses across various industries, including manufacturing, retail, distribution, and e-commerce. Our services are scalable and can be customized to meet the needs of both small businesses and large enterprises.\"\n                                },\n                                {\n                                    question: language === 'ar' ? \"ما هي سرعة ترتيب خدمات الفحص؟\" : \"How quickly can you arrange inspection services?\",\n                                    answer: language === 'ar' ? \"يمكن ترتيب خدمات الفحص القياسية في غضون 48-72 ساعة من الطلب. للاحتياجات العاجلة، نقدم خدمات معجلة يمكن جدولتها في غضون 24 ساعة في معظم المواقع، حسب التوفر.\" : \"Standard inspection services can be arranged within 48-72 hours of request. For urgent needs, we offer expedited services that can be scheduled within 24 hours in most locations, subject to availability.\"\n                                },\n                                {\n                                    question: language === 'ar' ? \"هل تقدمون خدمات دولية؟\" : \"Do you provide services internationally?\",\n                                    answer: language === 'ar' ? \"نعم، نقدم خدمات أعمالنا عالميًا من خلال شبكتنا الواسعة من المكاتب والشركاء في مراكز التصنيع والخدمات اللوجستية الرئيسية عبر آسيا وأوروبا والأمريكتين.\" : \"Yes, we offer our business services globally through our extensive network of offices and partners in major manufacturing and logistics hubs across Asia, Europe, and the Americas.\"\n                                },\n                                {\n                                    question: language === 'ar' ? \"ما هي شروط الدفع والطرق المتاحة؟\" : \"What are your payment terms and methods?\",\n                                    answer: language === 'ar' ? \"نقبل طرق دفع متنوعة بما في ذلك التحويلات المصرفية وبطاقات الائتمان والمدفوعات الرقمية. للعملاء المنتظمين، نقدم شروط دفع مرنة بما في ذلك حسابات صافي 30 يومًا.\" : \"We accept various payment methods including bank transfers, credit cards, and digital payments. For regular clients, we offer flexible payment terms including net-30 accounts.\"\n                                },\n                                {\n                                    question: language === 'ar' ? \"كيف تضمنون جودة الخدمة والاتساق؟\" : \"How do you ensure service quality and consistency?\",\n                                    answer: language === 'ar' ? \"نحافظ على عمليات مراقبة جودة صارمة، بما في ذلك التدريب المنتظم لموظفينا وإجراءات التشغيل المعيارية والمراقبة المستمرة لتقديم الخدمات.\" : \"We maintain strict quality control processes, including regular training for our staff, standardized operating procedures, and continuous monitoring of service delivery.\"\n                                }\n                            ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"rounded-lg overflow-hidden transition-all duration-300 group-hover:shadow-lg\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"flex items-center justify-between p-6 cursor-pointer transition-all duration-300 hover:bg-opacity-80\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-medium text-slate-900 dark:text-white pr-8\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"flex-shrink-0 ml-1.5 p-1.5 rounded-full transition-all duration-300\", isDarkMode ? \"text-slate-300 bg-slate-700\" : \"text-slate-700 bg-white\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5 transform group-open:rotate-180 transition-transform duration-200\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: \"2\",\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-6 pb-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 dark:text-slate-300\",\n                                                    children: faq.answer\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 536,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.cn)(\"py-16 md:py-24\", isDarkMode ? \"bg-slate-800\" : \"bg-slate-50\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-3xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6\",\n                                children: language === 'ar' ? 'هل أنت مستعد لتعزيز عمليات عملك؟' : 'Ready to Enhance Your Business Operations?'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-slate-600 dark:text-slate-300 mb-8\",\n                                children: language === 'ar' ? 'اتصل بفريقنا لمناقشة كيف يمكن لخدماتنا تلبية احتياجات عملك المحددة.' : 'Contact our team to discuss how our services can address your specific business needs.'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row justify-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        variant: \"primary\",\n                                        className: \"px-8 transition-all duration-300 hover:scale-105\",\n                                        onClick: ()=>router.push(\"/\".concat(language, \"/contact\")),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"\".concat(language === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this),\n                                            language === 'ar' ? 'اتصل بنا' : 'Contact Us'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        variant: \"outline\",\n                                        className: \"px-8 transition-all duration-300 hover:scale-105\",\n                                        onClick: ()=>router.push(\"/\".concat(language, \"/contact\")),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_ClipboardList_Clock_FileCheck_Mail_MapPin_Package_Phone_Search_Star_Truck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"\".concat(language === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, this),\n                                            language === 'ar' ? 'طلب عرض سعر' : 'Request Quote'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                        lineNumber: 622,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                    lineNumber: 621,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 620,\n                columnNumber: 7\n            }, this),\n            showBookingForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-fade-in\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl w-full animate-scale-in\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_forms_ServiceBookingForm__WEBPACK_IMPORTED_MODULE_6__.ServiceBookingForm, {\n                        serviceName: selectedService,\n                        onClose: ()=>{\n                            setShowBookingForm(false);\n                            setSelectedService(undefined);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                    lineNumber: 658,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n                lineNumber: 657,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\services\\\\ServicesPageSimple.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(ServicesPageEnhanced, \"E/qd+g4WGyFQLp2CRcC6T6vZhHQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _stores_themeStore__WEBPACK_IMPORTED_MODULE_8__.useThemeStore,\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_9__.useLanguageStore\n    ];\n});\n_c = ServicesPageEnhanced;\nvar _c;\n$RefreshReg$(_c, \"ServicesPageEnhanced\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/pages/services/ServicesPageSimple.tsx\n"));

/***/ })

});