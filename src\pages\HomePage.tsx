'use client';

import { Suspense, lazy } from 'react';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { ArrowRight, ShoppingCart, Users, Package, Truck, FileText, Tag, BarChart as ChartBar, Globe, Clock, Shield, CheckCircle, ArrowUpRight, Heart, Star, Eye, Settings, Gauge, PenTool as Tool, Calendar, FileCheck, Search, Building2, Play, MapPin, X, Send, Box, Factory, Bell } from 'lucide-react';
import { Button } from '../components/ui/Button';
import { HeroButton } from '../components/ui/HeroButton';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import { LazyImage } from '../components/ui/LazyImage';
import { EnhancedImage } from '../components/ui/EnhancedImage';
import { useCartStore } from '../stores/cartStore';
import { useWishlistStore } from '../stores/wishlistStore';
import { useAuthStore } from '../stores/authStore';
import { useLanguageStore } from '../stores/languageStore';
import { useTranslation } from '../translations';
import { useAuthModalStore } from '../stores/authModalStore';
import { WholesaleQuoteForm } from '../components/forms/WholesaleQuoteForm';
import { NewsletterForm } from '../components/forms/NewsletterForm';
import { QuickView } from '../components/shop/QuickView';
import { ExitIntentPopup } from '../components/marketing/ExitIntentPopup';
import { useABTesting } from '../components/marketing/ABTestingProvider';
import { SmoothTransition } from '../components/ui/animations/SmoothTransition';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../components/ui/animations';
import { motion } from 'framer-motion';
import { clearanceItems } from '../data/clearanceItems';
import { productionLines } from '../data/productionLines';
import { services } from '../data/services';
import { blogPosts } from '../data/blogPosts';
import { formatCurrency } from '../lib/utils';
import { getDbInstance } from '../lib/sqlite';
import { Product, ClearanceItem, Service } from '../types/index';
import { WishlistButton } from '../components/shop/WishlistButton';
import { QuickViewButton } from '../components/shop/QuickViewButton';
import { AddToCartButton } from '../components/shop/AddToCartButton';
import { ProductCard } from '../components/product/ProductCard';

// صور القسم الرئيسي حسب اللغة - تم تحديثها لتكون أكثر تعبيراً عن المحتوى النصي
const heroImagesData = {
  en: [
    {
      url: '/images/placeholder-light.svg',
      alt: 'Business solutions showcase',
      title: 'Business Solutions',
      subtitle: 'Comprehensive solutions for your business needs'
    },
    {
      url: '/images/placeholder-light.svg',
      alt: 'Professional services',
      title: 'Professional Services',
      subtitle: 'Expert services to support your business growth'
    },
    {
      url: '/images/placeholder-light.svg',
      alt: 'Quality products',
      title: 'Quality Products',
      subtitle: 'Premium products with quality guarantee'
    }
  ],
  ar: [
    {
      url: '/images/placeholder-light.svg',
      alt: 'عرض حلول الأعمال',
      title: 'حلول الأعمال',
      subtitle: 'حلول شاملة لاحتياجات عملك'
    },
    {
      url: '/images/placeholder-light.svg',
      alt: 'خدمات احترافية',
      title: 'خدمات احترافية',
      subtitle: 'خدمات خبيرة لدعم نمو أعمالك'
    },
    {
      url: '/images/placeholder-light.svg',
      alt: 'منتجات عالية الجودة',
      title: 'منتجات عالية الجودة',
      subtitle: 'منتجات متميزة مع ضمان الجودة'
    }
  ]
};

// حلول الأعمال حسب اللغة
const solutionsData = {
  en: [
    {
      icon: <ShoppingCart size={24} />,
      title: "Retail (B2C)",
      description: "Shop our extensive catalog of products with competitive pricing and fast delivery options.",
      features: [
        "Premium product selection",
        "Fast worldwide shipping",
        "Secure payment processing",
        "Customer satisfaction guarantee"
      ],
      image: "https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg"
    },
    {
      icon: <Users size={24} />,
      title: "Wholesale (B2B)",
      description: "Request quotes for bulk orders with special pricing available for business customers.",
      features: [
        "Volume discounts",
        "Dedicated account manager",
        "Flexible payment terms",
        "Custom packaging options"
      ],
      image: "https://images.pexels.com/photos/4483610/pexels-photo-4483610.jpeg"
    },
    {
      icon: <Package size={24} />,
      title: "Production Lines",
      description: "Browse our selection of turnkey manufacturing solutions with detailed specifications.",
      features: [
        "Automated systems",
        "Quality control integration",
        "Technical support",
        "Installation services"
      ],
      image: "https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg"
    },
    {
      icon: <Truck size={24} />,
      title: "Business Services",
      description: "Inspection, storage, shipping, certification, and consulting services to support your operations.",
      features: [
        "Professional consulting",
        "Quality inspections",
        "Logistics solutions",
        "Certification support"
      ],
      image: "https://images.pexels.com/photos/4483608/pexels-photo-4483608.jpeg"
    },
    {
      icon: <Tag size={24} />,
      title: "Clearance Sales",
      description: "Take advantage of special deals on bulk liquidation items and overstock products.",
      features: [
        "Exclusive discounts",
        "Regular new arrivals",
        "Bulk opportunities",
        "Quick shipping"
      ],
      image: "https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg"
    },
    {
      icon: <FileText size={24} />,
      title: "Industry Insights",
      description: "Stay informed with our blog featuring the latest trends, tips, and best practices.",
      features: [
        "Expert analysis",
        "Market trends",
        "Success stories",
        "Industry updates"
      ],
      image: "https://images.pexels.com/photos/4483602/pexels-photo-4483602.jpeg"
    }
  ],
  ar: [
    {
      icon: <ShoppingCart size={24} />,
      title: "البيع بالتجزئة (B2C)",
      description: "تسوق من كتالوج منتجاتنا الواسع بأسعار تنافسية وخيارات توصيل سريعة.",
      features: [
        "اختيار منتجات متميزة",
        "شحن عالمي سريع",
        "معالجة آمنة للمدفوعات",
        "ضمان رضا العملاء"
      ],
      image: "https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg"
    },
    {
      icon: <Users size={24} />,
      title: "البيع بالجملة (B2B)",
      description: "اطلب عروض أسعار للطلبات الكبيرة مع أسعار خاصة متاحة لعملاء الأعمال.",
      features: [
        "خصومات على الكميات",
        "مدير حساب مخصص",
        "شروط دفع مرنة",
        "خيارات تغليف مخصصة"
      ],
      image: "https://images.pexels.com/photos/4483610/pexels-photo-4483610.jpeg"
    },
    {
      icon: <Package size={24} />,
      title: "خطوط الإنتاج",
      description: "تصفح مجموعتنا من حلول التصنيع الجاهزة مع مواصفات مفصلة.",
      features: [
        "أنظمة آلية",
        "تكامل مراقبة الجودة",
        "دعم فني",
        "خدمات التركيب"
      ],
      image: "https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg"
    },
    {
      icon: <Truck size={24} />,
      title: "خدمات الأعمال",
      description: "خدمات الفحص والتخزين والشحن والشهادات والاستشارات لدعم عملياتك.",
      features: [
        "استشارات احترافية",
        "فحوصات الجودة",
        "حلول لوجستية",
        "دعم الشهادات"
      ],
      image: "https://images.pexels.com/photos/4483608/pexels-photo-4483608.jpeg"
    },
    {
      icon: <Tag size={24} />,
      title: "تصفية المبيعات",
      description: "استفد من عروضنا المحدودة والصفقات الحصرية على الحلول الصناعية المتميزة.",
      features: [
        "خصومات حصرية",
        "وصول منتجات جديدة بانتظام",
        "فرص للشراء بالجملة",
        "شحن سريع"
      ],
      image: "https://images.pexels.com/photos/5668473/pexels-photo-5668473.jpeg"
    },
    {
      icon: <FileText size={24} />,
      title: "رؤى الصناعة",
      description: "ابق على اطلاع من خلال مدونتنا التي تعرض أحدث الاتجاهات والنصائح وأفضل الممارسات.",
      features: [
        "تحليل الخبراء",
        "اتجاهات السوق",
        "قصص النجاح",
        "تحديثات الصناعة"
      ],
      image: "https://images.pexels.com/photos/4483602/pexels-photo-4483602.jpeg"
    }
  ]
};

function HomePage() {
  const { locale } = useTranslation();
  const { language, direction } = useLanguageStore(); // Get direction here
  const { t } = useTranslation();

  // استخدام البيانات المناسبة حسب اللغة المحددة
  const currentLanguage = (locale as 'ar' | 'en') || language;
  const heroImages = heroImagesData[currentLanguage];
  const currentSolutions = solutionsData[currentLanguage];

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [hoveredSolution, setHoveredSolution] = useState<number | null>(null);
  const [showWholesaleForm, setShowWholesaleForm] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showQuickView, setShowQuickView] = useState(false);
  const [selectedClearanceItem, setSelectedClearanceItem] = useState<ClearanceItem | null>(null);
  const [showClearanceQuoteForm, setShowClearanceQuoteForm] = useState(false);
  const [selectedService, setSelectedService] = useState<Service | null>(null);

  // فتح نافذة العرض السريع للمنتج
  const handleQuickView = (product: Product) => {
    setSelectedProduct(product);
    setShowQuickView(true);
  };

  // فتح نموذج طلب عرض سعر للجملة
  const handleWholesaleQuote = (product: Product) => {
    setSelectedProduct(product);
    setShowWholesaleForm(true);
  };

  // التحقق من تسجيل دخول المستخدم قبل إضافة المنتج إلى السلة أو المفضلة
  const { openModal } = useAuthModalStore();
  const { user } = useAuthStore();

  const handleUnauthenticated = () => {
    openModal('sign-in');
  };

  // إضافة المنتج إلى السلة - يمكن للمستخدم إضافة المنتج إلى السلة حتى لو لم يكن مسجل دخول
  const handleAddToCart = (product: Product) => {
    // التحقق من توفر المنتج في المخزون
    if (!product.inStock && product.stock <= 0) {
      // يمكن إضافة إشعار هنا بأن المنتج غير متوفر
      console.warn(`المنتج ${product.name} غير متوفر في المخزون`);
      return;
    }

    // تجهيز بيانات المنتج للإضافة إلى السلة
    const cartItem = {
      id: product.id,
      name: product.name,
      name_ar: product.name_ar || product.name,
      price: product.price,
      image: product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg',
      quantity: 1,
    };

    // إضافة المنتج إلى السلة باستخدام getState للحصول على حالة المتجر
    const cart = useCartStore.getState();
    cart.addItem(cartItem, 1);

    // إظهار إشعار بنجاح الإضافة (يمكن تحسين هذه الوظيفة لاحقاً بإضافة نظام إشعارات)
    console.log(`تمت إضافة ${product.name} إلى السلة`);

    // يمكن إضافة تحليلات هنا لتتبع سلوك المستخدم
    // trackEvent('add_to_cart', { product_id: product.id, product_name: product.name });
  };

  // إضافة المنتج إلى المفضلة - لا يتطلب تسجيل الدخول
  const handleAddToWishlist = (product: Product) => {
    const wishlist = useWishlistStore.getState();

    // التحقق مما إذا كان المنتج موجوداً بالفعل في المفضلة
    if (wishlist.isInWishlist(product.id)) {
      // إزالة المنتج من المفضلة
      wishlist.removeItem(product.id);
      console.log(`تمت إزالة ${product.name} من المفضلة`);
    } else {
      // إضافة المنتج إلى المفضلة
      wishlist.addItem(product);
      console.log(`تمت إضافة ${product.name} إلى المفضلة`);
    }

    // يمكن إضافة تحليلات هنا لتتبع سلوك المستخدم
    // trackEvent('toggle_wishlist', { product_id: product.id, product_name: product.name });
  };

  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [showExitIntentPopup, setShowExitIntentPopup] = useState(false);

  // تحميل المنتجات المميزة
  useEffect(() => {
    const loadProducts = async () => {
      setIsLoadingProducts(true);
      try {
        // استخدام البيانات الثابتة بدلاً من قاعدة البيانات
        // في المستقبل يمكن استبدال هذا بالاتصال بقاعدة البيانات
        import('../data/products').then(module => {
          const productsData = module.products || [];

          // اختيار المنتجات المميزة فقط
          const featured = productsData.filter(p => p.featured).slice(0, 8);

          // إذا لم توجد منتجات مميزة، استخدم أول 8 منتجات
          const productsToShow = featured.length > 0 ? featured : productsData.slice(0, 8);

          // تأكد من أن جميع المنتجات تحتوي على الحقول المطلوبة
          const validatedProducts = productsToShow.map(product => ({
            ...product,
            // تأكد من وجود صور للمنتج
            images: product.images && product.images.length > 0
              ? product.images
              : ['/images/product-placeholder-light.svg'],
            // تأكد من وجود سعر للمنتج
            price: product.price || 0,
            // تأكد من وجود مخزون للمنتج
            stock: typeof product.stock === 'number' ? product.stock : 10,
            // تأكد من وجود تقييم للمنتج
            rating: product.rating || 0,
            reviewCount: product.reviewCount || 0,
            // إضافة حقل inStock لتسهيل التحقق من توفر المنتج
            inStock: typeof product.stock === 'number' ? product.stock > 0 : true,
            // إضافة حقل discount لحساب نسبة الخصم
            discount: product.compareAtPrice && product.compareAtPrice > product.price
              ? Math.round((1 - product.price / product.compareAtPrice) * 100)
              : 0,
            // إضافة حقول الترجمة إذا لم تكن موجودة
            name_ar: product.name_ar || product.name,
            description_ar: product.description_ar || product.description
          }));

          setFeaturedProducts(validatedProducts);
          setIsLoadingProducts(false);
        }).catch(error => {
          console.error("Failed to load products data:", error);
          setIsLoadingProducts(false);
        });
      } catch (error) {
        console.error("Failed to load products:", error);
        setIsLoadingProducts(false);
      }
    };
    loadProducts();
  }, []);

  // تحسين مدة الانتقال بين الصور وإضافة تأثيرات انتقالية أكثر سلاسة
  useEffect(() => {
    // زيادة مدة عرض كل صورة إلى 7 ثوانٍ لإعطاء المستخدم وقتًا كافيًا لقراءة المحتوى
    const interval = setInterval(() => {
      // تغيير الصورة مباشرة بدون تأثير انتقالي على الصفحة بأكملها
      setCurrentImageIndex((prev) => (prev + 1) % heroImages.length);
    }, 7000);

    return () => clearInterval(interval);
  }, [heroImages.length]);

  // استخدام A/B Testing للصور البارزة
  const { getVariant, trackConversion } = useABTesting();
  const heroVariant = getVariant('heroImage');

  // تتبع التحويل عند النقر على زر "استكشف المنتجات"
  const handleExploreClick = () => {
    trackConversion('heroImage');
  };

  // Exit-intent popup logic
  useEffect(() => {
    const handleMouseLeave = (event: MouseEvent) => {
      if (event.clientY <= 0 && !sessionStorage.getItem('exitIntentShown')) {
        setShowExitIntentPopup(true);
        sessionStorage.setItem('exitIntentShown', 'true');
      }
    };

    document.addEventListener('mouseleave', handleMouseLeave);
    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  // Adapter function to make ClearanceItem compatible with Product-expecting components
  const clearanceProductAdapter = (item: ClearanceItem): Product => ({
    id: item.id,
    name: item.name,
    slug: `/clearance/${item.id}`, // Generate a relevant slug
    description: item.description,
    price: item.clearancePrice,
    compareAtPrice: item.originalPrice,
    images: item.image ? [item.image] : [],
    category: item.category,
    tags: [item.category], // Simple tagging based on category
    stock: item.availableQuantity,
    featured: false, // Clearance items are generally not featured
    specifications: {}, // No detailed specs for clearance items
    createdAt: new Date().toISOString(),
    // Optional fields from Product, can be undefined or have default values
    reviews: [],
    rating: 0,
    reviewCount: 0,
    relatedProducts: [],
  });

  return (
    <div>
      {/* نافذة منبثقة عند محاولة المغادرة */}
      {showExitIntentPopup && featuredProducts.length > 0 && (
        <ExitIntentPopup
          onClose={() => setShowExitIntentPopup(false)}
          product={featuredProducts[Math.floor(Math.random() * featuredProducts.length)]} // Use a random product from fetched list
        />
      )}

      {/* Hero Section - تصميم محسن مع عناصر تفاعلية متقدمة */}
      <section className="relative h-[90vh] overflow-hidden bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-900 text-white" dir={direction}>
        {/* طبقة التراكب الأساسية مع تأثيرات محسنة */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-900/90 via-black/50 to-secondary-900/80 z-10 mix-blend-multiply" />

        {/* تأثير الجسيمات المتحركة */}
        <div className="absolute inset-0 z-5 opacity-30">
          <div className="absolute inset-0 overflow-hidden">
            <div className="particles-container absolute inset-0">
              {[...Array(20)].map((_, i) => (
                <div
                  key={`particle-${i}`}
                  className={`absolute rounded-full bg-white/20 animate-pulse`}
                  style={{
                    width: `${Math.random() * 5 + 2}px`,
                    height: `${Math.random() * 5 + 2}px`,
                    top: `${Math.random() * 100}%`,
                    left: `${Math.random() * 100}%`,
                    animationDuration: `${Math.random() * 8 + 2}s`,
                    animationDelay: `${Math.random() * 5}s`
                  }}
                />
              ))}
            </div>
          </div>
        </div>

        {/* أنماط هندسية متحركة في الخلفية */}
        <div className="absolute inset-0 z-5 opacity-20">
          <div className="absolute top-0 left-0 w-full h-full">
            <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <defs>
                <linearGradient id="grid-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="var(--color-primary-300)" stopOpacity="0.4" />
                  <stop offset="100%" stopColor="var(--color-secondary-300)" stopOpacity="0.4" />
                </linearGradient>
                <pattern id="dots-pattern" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse">
                  <circle cx="5" cy="5" r="0.5" fill="rgba(255,255,255,0.2)" />
                </pattern>
              </defs>
              <path d="M0,0 L100,0 L100,100 L0,100 Z" fill="url(#grid-gradient)" />
              <rect x="0" y="0" width="100" height="100" fill="url(#dots-pattern)" />

              {/* خطوط شبكية متحركة محسنة */}
              {[...Array(12)].map((_, i) => (
                <line
                  key={`h-${i}`}
                  x1="0"
                  y1={i * 8.33}
                  x2="100"
                  y2={i * 8.33}
                  stroke="rgba(255,255,255,0.15)"
                  strokeWidth="0.1"
                  strokeDasharray="0.5,3"
                />
              ))}
              {[...Array(12)].map((_, i) => (
                <line
                  key={`v-${i}`}
                  x1={i * 8.33}
                  y1="0"
                  x2={i * 8.33}
                  y2="100"
                  stroke="rgba(255,255,255,0.15)"
                  strokeWidth="0.1"
                  strokeDasharray="0.5,3"
                />
              ))}
            </svg>
          </div>
        </div>

        {/* صور الخلفية المتحركة - تحسين أبعاد الصورة */}
        <div className="absolute inset-0 z-0">
          {heroImages.map((image, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-all duration-2000 ${
                index === currentImageIndex
                  ? 'opacity-100 scale-100'
                  : 'opacity-0 scale-110'
              }`}
            >
              <div className="absolute inset-0 bg-gradient-to-t from-primary-900/80 via-primary-900/40 to-transparent z-10" />
              <EnhancedImage
                src={image.url}
                alt={image.alt}
                fill={true}
                objectFit="cover"
                progressive={true}
                placeholder="shimmer"
                className="object-center filter brightness-[0.8] contrast-[1.1]"
                containerClassName="w-full h-full"
                sizes="(max-width: 768px) 100vw, 100vw"
                priority={index === 0}
                quality={95}
              />


            </div>
          ))}
        </div>

        {/* المحتوى الرئيسي - مركز مع تأثيرات محسنة */}
        <div className="container-custom relative z-20 h-full flex items-center">
          <div className="flex flex-col items-center justify-center h-full py-16 w-full">
            {/* المحتوى النصي المركز */}
            <div className="flex flex-col justify-center text-center w-full max-w-4xl mx-auto">
              {/* شعار الشركة المتحرك مع تأثيرات محسنة */}
              <SmoothTransition type="fade" duration={0.5} delay={0.1}>
                <div className="flex items-center justify-center mb-8 relative">
                  <div className="absolute -inset-4 bg-gradient-to-r from-accent-500/0 via-accent-500/20 to-accent-500/0 rounded-full blur-xl opacity-70 animate-pulse"></div>
                  <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-accent-500/40 to-primary-500/40 backdrop-blur-md flex items-center justify-center shadow-lg shadow-primary-500/20 relative overflow-hidden group">
                    <div className="absolute inset-0 bg-gradient-to-br from-accent-500/0 to-primary-500/0 group-hover:from-accent-500/20 group-hover:to-primary-500/20 transition-all duration-700"></div>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="32"
                      height="32"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-accent-300 relative z-10"
                    >
                      <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                      <path d="M2 17l10 5 10-5"></path>
                      <path d="M2 12l10 5 10-5"></path>
                    </svg>
                  </div>
                  <span className={`text-3xl font-bold ${currentLanguage === 'ar' ? 'mr-4' : 'ml-4'} bg-clip-text text-transparent bg-gradient-to-r from-accent-300 to-white relative`}>
                    {currentLanguage === 'ar' ? 'ارتال' : 'ARTAL'}
                    <span className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-accent-500/0 via-accent-500/80 to-accent-500/0"></span>
                  </span>
                </div>
              </SmoothTransition>

              {/* العنوان الرئيسي مع تأثيرات محسنة */}
              <SmoothTransition type="slide" direction="up" duration={0.7} delay={0.2}>
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-extrabold mb-8 leading-tight">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-white via-primary-100 to-white relative inline-block">
                    {currentLanguage === 'ar'
                      ? 'حلول أعمال متكاملة'
                      : 'Integrated Business Solutions'}
                  </span>
                  <br />
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent-300 via-white to-accent-300 relative inline-block mt-2">
                    {currentLanguage === 'ar'
                      ? 'من ارتال'
                      : 'by ARTAL'}
                    <div className="absolute -bottom-2 left-1/4 right-1/4 h-1 bg-gradient-to-r from-accent-500/0 via-accent-500 to-accent-500/0 rounded-full"></div>
                  </span>
                </h1>
              </SmoothTransition>

              {/* العنوان الفرعي مع تأثيرات محسنة */}
              <SmoothTransition type="slide" direction="up" duration={0.7} delay={0.3}>
                <p className="text-xl md:text-2xl text-white/90 mb-10 max-w-3xl mx-auto leading-relaxed">
                  {currentLanguage === 'ar'
                    ? 'نقدم حلولاً متكاملة للتجارة الإلكترونية والخدمات اللوجستية وخطوط الإنتاج لتعزيز نمو أعمالك'
                    : 'We offer integrated solutions for e-commerce, logistics, and production lines to enhance your business growth'}
                </p>
              </SmoothTransition>

              {/* أزرار الدعوة للعمل مع تأثيرات محسنة */}
              <SmoothTransition type="slide" direction="up" duration={0.7} delay={0.5}>
                <div className="flex flex-wrap gap-6 mb-16 justify-center">
                  <HeroButton
                    href={`/${currentLanguage}/shop`}
                    variant="accent"
                    className="flex items-center py-4 px-10 relative overflow-hidden shadow-xl hover:shadow-accent-500/30 group rounded-xl text-lg font-medium transition-all duration-300"
                    onClick={handleExploreClick}
                  >
                    <span className="absolute inset-0 bg-gradient-to-r from-accent-600 to-accent-500 z-0"></span>
                    <span className="absolute inset-0 bg-gradient-to-r from-accent-500 to-accent-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-0"></span>
                    <span className="relative z-10">{currentLanguage === 'ar' ? 'استكشف حلولنا' : 'Explore Our Solutions'}</span>
                    <ArrowRight className={`w-5 h-5 ${currentLanguage === 'ar' ? 'mr-3 rotate-180' : 'ml-3'} relative z-10 transition-transform duration-300 group-hover:translate-x-1`} />
                  </HeroButton>
                  <HeroButton
                    href={`/${currentLanguage}/contact`}
                    variant="outline"
                    className="py-4 px-10 relative overflow-hidden border-2 backdrop-blur-sm rounded-xl hover:bg-white/10 transition-all duration-300 text-lg font-medium group"
                  >
                    <span className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/0 group-hover:from-white/5 group-hover:to-white/10 transition-all duration-300 z-0"></span>
                    <span className="relative z-10">{currentLanguage === 'ar' ? 'طلب استشارة مجانية' : 'Free Consultation'}</span>
                  </HeroButton>
                </div>
              </SmoothTransition>
            </div>
          </div>
        </div>

        {/* مؤشرات الشرائح مع تصميم محسن */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
          <div className="flex justify-center gap-4">
            {heroImages.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={`relative h-3 rounded-full transition-all duration-500 shadow-lg overflow-hidden ${
                  index === currentImageIndex
                    ? 'bg-accent-500 w-12'
                    : 'bg-white/40 hover:bg-white/60 w-3'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              >
                {index === currentImageIndex && (
                  <span className="absolute inset-0 bg-gradient-to-r from-accent-500/0 via-white/30 to-accent-500/0 animate-slide-right"></span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* إحصائيات الخدمات - أسفل قسم Hero - تصميم محسن ومتطور بحجم أصغر */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-r from-primary-900/90 via-secondary-900/90 to-primary-900/90 backdrop-blur-xl py-3 border-t border-white/20">
          <div className="container-custom">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center relative">
              {/* زخرفة خلفية متطورة */}
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.15)_0%,transparent_70%)] opacity-60"></div>
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute w-full h-full opacity-10">
                  <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                    <defs>
                      <pattern id="stats-grid" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse">
                        <path d="M10 0H0V10" fill="none" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5" />
                      </pattern>
                    </defs>
                    <rect x="0" y="0" width="100" height="100" fill="url(#stats-grid)" />
                  </svg>
                </div>
              </div>

              {/* إحصائية 1 - خدمات احترافية */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-purple-700/10 rounded-xl transform scale-0 group-hover:scale-100 transition-transform duration-500 ease-out"></div>
                <div className="flex flex-col items-center relative z-10 transform transition-transform duration-500 hover:translate-y-[-3px]">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500/40 to-purple-700/40 flex items-center justify-center mb-2 shadow-md shadow-purple-500/20 group-hover:shadow-purple-500/40 transition-all duration-500 relative overflow-hidden">
                    <div className="absolute inset-0 bg-[conic-gradient(from_0deg,transparent_0%,transparent_40%,rgba(255,255,255,0.2)_45%,transparent_50%,transparent_100%)] animate-rotate-slow opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-500/0 to-purple-700/0 group-hover:from-purple-500/20 group-hover:to-purple-700/20 transition-all duration-500"></div>
                    <Box className="w-6 h-6 text-purple-300 group-hover:text-purple-200 transition-colors duration-300 relative z-10" />
                  </div>
                  <div className="relative">
                    <div className="text-xl md:text-3xl font-extrabold text-white bg-clip-text text-transparent bg-gradient-to-r from-purple-300 to-white mb-0.5 group-hover:scale-110 transition-transform duration-300">6+</div>
                    <div className="absolute -inset-1 bg-purple-500/20 blur-lg rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-500"></div>
                  </div>
                  <div className="text-xs md:text-sm text-white/90 font-medium relative">
                    <span className="relative inline-block">
                      {currentLanguage === 'ar' ? 'خدمات احترافية' : 'Professional Services'}
                      <span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-purple-400 group-hover:w-full transition-all duration-300"></span>
                    </span>
                  </div>
                </div>
              </div>

              {/* إحصائية 2 - دعم العملاء */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-blue-700/10 rounded-xl transform scale-0 group-hover:scale-100 transition-transform duration-500 ease-out"></div>
                <div className="flex flex-col items-center relative z-10 transform transition-transform duration-500 hover:translate-y-[-3px]">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/40 to-blue-700/40 flex items-center justify-center mb-2 shadow-md shadow-blue-500/20 group-hover:shadow-blue-500/40 transition-all duration-500 relative overflow-hidden">
                    <div className="absolute inset-0 bg-[conic-gradient(from_90deg,transparent_0%,transparent_40%,rgba(255,255,255,0.2)_45%,transparent_50%,transparent_100%)] animate-rotate-slow opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/0 to-blue-700/0 group-hover:from-blue-500/20 group-hover:to-blue-700/20 transition-all duration-500"></div>
                    <Clock className="w-6 h-6 text-blue-300 group-hover:text-blue-200 transition-colors duration-300 relative z-10" />
                  </div>
                  <div className="relative">
                    <div className="text-xl md:text-3xl font-extrabold text-white bg-clip-text text-transparent bg-gradient-to-r from-blue-300 to-white mb-0.5 group-hover:scale-110 transition-transform duration-300">24/7</div>
                    <div className="absolute -inset-1 bg-blue-500/20 blur-lg rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-500"></div>
                  </div>
                  <div className="text-xs md:text-sm text-white/90 font-medium relative">
                    <span className="relative inline-block">
                      {currentLanguage === 'ar' ? 'دعم العملاء' : 'Customer Support'}
                      <span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-blue-400 group-hover:w-full transition-all duration-300"></span>
                    </span>
                  </div>
                </div>
              </div>

              {/* إحصائية 3 - عملاء راضون */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-500/10 to-amber-700/10 rounded-xl transform scale-0 group-hover:scale-100 transition-transform duration-500 ease-out"></div>
                <div className="flex flex-col items-center relative z-10 transform transition-transform duration-500 hover:translate-y-[-3px]">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-amber-500/40 to-amber-700/40 flex items-center justify-center mb-2 shadow-md shadow-amber-500/20 group-hover:shadow-amber-500/40 transition-all duration-500 relative overflow-hidden">
                    <div className="absolute inset-0 bg-[conic-gradient(from_180deg,transparent_0%,transparent_40%,rgba(255,255,255,0.2)_45%,transparent_50%,transparent_100%)] animate-rotate-slow opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-amber-500/0 to-amber-700/0 group-hover:from-amber-500/20 group-hover:to-amber-700/20 transition-all duration-500"></div>
                    <Star className="w-6 h-6 text-amber-300 group-hover:text-amber-200 transition-colors duration-300 relative z-10" />
                  </div>
                  <div className="relative">
                    <div className="text-xl md:text-3xl font-extrabold text-white bg-clip-text text-transparent bg-gradient-to-r from-amber-300 to-white mb-0.5 group-hover:scale-110 transition-transform duration-300">500+</div>
                    <div className="absolute -inset-1 bg-amber-500/20 blur-lg rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-500"></div>
                  </div>
                  <div className="text-xs md:text-sm text-white/90 font-medium relative">
                    <span className="relative inline-block">
                      {currentLanguage === 'ar' ? 'عملاء راضون' : 'Satisfied Clients'}
                      <span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-amber-400 group-hover:w-full transition-all duration-300"></span>
                    </span>
                  </div>
                </div>
              </div>

              {/* إحصائية 4 - ضمان الجودة */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-green-700/10 rounded-xl transform scale-0 group-hover:scale-100 transition-transform duration-500 ease-out"></div>
                <div className="flex flex-col items-center relative z-10 transform transition-transform duration-500 hover:translate-y-[-3px]">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500/40 to-green-700/40 flex items-center justify-center mb-2 shadow-md shadow-green-500/20 group-hover:shadow-green-500/40 transition-all duration-500 relative overflow-hidden">
                    <div className="absolute inset-0 bg-[conic-gradient(from_270deg,transparent_0%,transparent_40%,rgba(255,255,255,0.2)_45%,transparent_50%,transparent_100%)] animate-rotate-slow opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-green-500/0 to-green-700/0 group-hover:from-green-500/20 group-hover:to-green-700/20 transition-all duration-500"></div>
                    <Shield className="w-6 h-6 text-green-300 group-hover:text-green-200 transition-colors duration-300 relative z-10" />
                  </div>
                  <div className="relative">
                    <div className="text-xl md:text-3xl font-extrabold text-white bg-clip-text text-transparent bg-gradient-to-r from-green-300 to-white mb-0.5 group-hover:scale-110 transition-transform duration-300">100%</div>
                    <div className="absolute -inset-1 bg-green-500/20 blur-lg rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-500"></div>
                  </div>
                  <div className="text-xs md:text-sm text-white/90 font-medium relative">
                    <span className="relative inline-block">
                      {currentLanguage === 'ar' ? 'ضمان الجودة' : 'Quality Guarantee'}
                      <span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-green-400 group-hover:w-full transition-all duration-300"></span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Our Solutions Section - تحسين قسم الحلول بشكل احترافي */}
      <section id="solutions" className="py-24 bg-gradient-to-b from-slate-50 via-white to-slate-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900" dir={direction}>
        <div className="container-custom">
          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}
          <div className="text-center mb-20 relative">
            {/* خط زخرفي علوي */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-1.5 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full opacity-80"></div>

            {/* أيقونة القسم */}
            <div className="inline-flex items-center justify-center w-16 h-16 mb-6 rounded-full bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400">
              <Settings className="w-8 h-8" />
            </div>

            {/* العنوان الرئيسي */}
            <h2 className="text-3xl md:text-5xl font-bold tracking-tight text-gray-900 dark:text-white mb-6 relative inline-block">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary-600 to-accent-600 dark:from-primary-400 dark:to-accent-400">
                {t('solutions.title')}
              </span>
              <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full"></div>
            </h2>

            {/* العنوان الفرعي */}
            <p className="mt-4 text-lg md:text-xl leading-8 text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              {t('solutions.subtitle')}
            </p>
          </div>

          {/* شبكة الحلول */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
            {currentSolutions.map((solution, index) => (
              <div
                key={index}
                className="group relative overflow-hidden rounded-xl bg-white dark:bg-slate-800/90 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2"
              >
                {/* خط علوي متحرك */}
                <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-primary-500 to-accent-500 transform origin-left transition-transform duration-500 scale-x-0 group-hover:scale-x-100"></div>

                {/* رأس البطاقة مع الأيقونة والعنوان */}
                <div className="relative p-6 pb-0">
                  <div className="flex items-center mb-4">
                    <div className="p-3 bg-primary-50 dark:bg-primary-900/30 rounded-xl text-primary-600 dark:text-primary-400 shadow-sm">
                      {solution.icon}
                    </div>
                    <h3 className={`text-xl font-bold ${currentLanguage === 'ar' ? 'mr-4' : 'ml-4'} text-slate-900 dark:text-white`}>
                      {solution.title}
                    </h3>
                  </div>

                  {/* وصف الحل */}
                  <p className="text-base text-slate-600 dark:text-slate-300 mb-6 leading-relaxed">
                    {solution.description}
                  </p>
                </div>

                {/* محتوى الحل والميزات */}
                <div className="p-6 pt-0">
                  {solution.features && solution.features.length > 0 && (
                    <div className="mt-2">
                      <h4 className="text-sm font-semibold text-slate-800 dark:text-slate-200 mb-4 flex items-center">
                        <Star size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} text-accent-500`} />
                        {t('solutions.features')}
                      </h4>
                      <ul className="space-y-3 text-sm text-slate-600 dark:text-slate-300">
                        {solution.features.map((feature, fIndex) => (
                          <li key={fIndex} className="flex items-start">
                            <CheckCircle size={16} className={`flex-shrink-0 w-4 h-4 text-primary-500 dark:text-primary-400 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} mt-0.5`} />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* زر التفاصيل مع رابط صحيح */}
                  <div className="mt-6 pt-4 border-t border-slate-100 dark:border-slate-700/50">
                    <Link
                      href={`/${currentLanguage}/${solution.title === "Retail (B2C)" || solution.title === "البيع بالتجزئة (B2C)" ? 'shop' :
                             solution.title === "Wholesale (B2B)" || solution.title === "البيع بالجملة (B2B)" ? 'wholesale' :
                             solution.title === "Production Lines" || solution.title === "خطوط الإنتاج" ? 'production-lines' :
                             solution.title === "Business Services" || solution.title === "خدمات الأعمال" ? 'services' :
                             solution.title === "Clearance Sales" || solution.title === "تصفية المبيعات" ? 'clearance' :
                             solution.title === "Industry Insights" || solution.title === "رؤى الصناعة" ? 'blog' : 'solutions'}`}
                      className="flex items-center justify-between text-primary-600 dark:text-primary-400 font-medium hover:text-primary-700 dark:hover:text-primary-300 transition-colors group"
                      aria-label={`${currentLanguage === 'ar' ? 'اكتشف المزيد عن' : 'Discover more about'} ${solution.title}`}
                    >
                      <span>{currentLanguage === 'ar' ? 'اكتشف المزيد' : 'Discover More'}</span>
                      <ArrowRight className={`w-4 h-4 transform transition-transform duration-300 group-hover:translate-x-1 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />
                    </Link>
                  </div>
                </div>

                {/* خط سفلي متحرك */}
                <div className="absolute bottom-0 left-0 w-full h-1.5 bg-gradient-to-r from-accent-500 to-primary-500 transform origin-right transition-transform duration-500 scale-x-0 group-hover:scale-x-100"></div>
              </div>
            ))}
          </div>


        </div>
      </section>

      {/* Featured Products - قسم المنتجات المميزة */}
      <section id="featured-products" className="py-16 md:py-24 bg-gradient-to-b from-background via-background to-slate-50 dark:from-background dark:via-background dark:to-slate-900/20" dir={direction}>
        <div className="container-custom">
          {/* عنوان القسم مع تأثيرات بصرية */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12">
            <div className="relative">
              <div className={`absolute ${currentLanguage === 'ar' ? '-right-3' : '-left-3'} top-0 h-12 w-1 bg-gradient-to-b from-primary-500 to-primary-700 rounded-full hidden md:block`}></div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl">
                {currentLanguage === 'ar' ? 'اكتشف أحدث ابتكاراتنا وأفضل الحلول الصناعية مبيعاً' : 'Discover our latest innovations and best-selling industrial solutions'}
              </p>
            </div>
            <div className="flex flex-wrap items-center gap-4 mt-6 md:mt-0">
              <Link href={`/${currentLanguage}/shop`}>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 shadow-sm hover:shadow transition-shadow"
                  aria-label={currentLanguage === 'ar' ? 'عرض جميع المنتجات' : 'View all products'}
                >
                  {currentLanguage === 'ar' ? 'عرض جميع المنتجات' : 'View All Products'}
                  <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />
                </Button>
              </Link>
              <Link href={`/${currentLanguage}/shop/new-arrivals`}>
                <Button
                  variant="primary"
                  className="flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow"
                  aria-label={currentLanguage === 'ar' ? 'تصفح المنتجات الجديدة' : 'Browse new arrivals'}
                >
                  {currentLanguage === 'ar' ? 'واصل حديثاً' : 'New Arrivals'}
                  <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />
                </Button>
              </Link>
            </div>
          </div>

          {/* فلاتر المنتجات */}
          <div className="flex flex-wrap items-center gap-2 mb-8 bg-slate-50 dark:bg-slate-800/50 p-4 rounded-lg shadow-sm">
            <span className={`text-sm font-medium text-slate-700 dark:text-slate-300 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`}>
              {currentLanguage === 'ar' ? 'تصفية حسب:' : 'Filter by:'}
            </span>
            <Button
              variant="ghost"
              size="sm"
              className="rounded-full bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 hover:bg-primary-100 dark:hover:bg-primary-900/50"
              aria-pressed="true"
              onClick={() => {/* يمكن إضافة وظيفة تصفية هنا */}}
            >
              {currentLanguage === 'ar' ? 'الكل' : 'All'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="rounded-full hover:bg-primary-50 hover:text-primary-700 dark:hover:bg-primary-900/30 dark:hover:text-primary-300"
              aria-pressed="false"
              onClick={() => {/* يمكن إضافة وظيفة تصفية هنا */}}
            >
              {currentLanguage === 'ar' ? 'الأكثر مبيعاً' : 'Best Sellers'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="rounded-full hover:bg-primary-50 hover:text-primary-700 dark:hover:bg-primary-900/30 dark:hover:text-primary-300"
              aria-pressed="false"
              onClick={() => {/* يمكن إضافة وظيفة تصفية هنا */}}
            >
              {currentLanguage === 'ar' ? 'واصل حديثاً' : 'New Arrivals'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="rounded-full hover:bg-primary-50 hover:text-primary-700 dark:hover:bg-primary-900/30 dark:hover:text-primary-300"
              aria-pressed="false"
              onClick={() => {/* يمكن إضافة وظيفة تصفية هنا */}}
            >
              {currentLanguage === 'ar' ? 'العروض' : 'On Sale'}
            </Button>
          </div>

          {/* عرض المنتجات مع حالة التحميل */}
          {isLoadingProducts ? (
            // حالة التحميل - عرض بطاقات نبضية
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
              {[...Array(4)].map((_, index) => (
                <Card key={index} className="flex flex-col h-full overflow-hidden bg-white dark:bg-slate-800 shadow-lg">
                  <div className="relative w-full aspect-square bg-slate-200 dark:bg-slate-700 animate-pulse"></div>
                  <div className="p-4 flex flex-col flex-grow">
                    <div className="h-6 bg-slate-200 dark:bg-slate-700 rounded mb-2 animate-pulse w-3/4"></div>
                    <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded mb-4 animate-pulse w-1/2"></div>
                    <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded animate-pulse w-full mt-auto"></div>
                  </div>
                </Card>
              ))}
            </div>
          ) : featuredProducts.length > 0 ? (
            // حالة وجود منتجات مميزة
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                {featuredProducts.map((product, index) => (
                  <div key={product.id} className="group relative overflow-hidden rounded-xl bg-white dark:bg-slate-800 shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full">
                    {/* شارة المنتج المميز */}
                    <div className="absolute top-3 left-3 z-10">
                      <span className="bg-amber-500 text-white text-xs font-bold px-2.5 py-1 rounded-full">
                        {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                      </span>
                    </div>

                    {/* شارة الخصم (إذا كان هناك خصم) */}
                    {product.compareAtPrice && product.compareAtPrice > product.price && (
                      <div className="absolute top-3 right-3 z-10">
                        <span className="bg-red-500 text-white text-xs font-bold px-2.5 py-1 rounded-full">
                          {`${product.discount}% ${currentLanguage === 'ar' ? 'خصم' : 'OFF'}`}
                        </span>
                      </div>
                    )}

                    {/* صورة المنتج مع رابط للتفاصيل */}
                    <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block relative overflow-hidden">
                      <div className="relative w-full aspect-square overflow-hidden">
                        <EnhancedImage
                          src={product.images && product.images.length > 0 ? product.images[0] : '/images/product-placeholder-light.svg'}
                          alt={product.name}
                          fill={true}
                          objectFit="cover"
                          progressive={true}
                          placeholder="shimmer"
                          className="transition-transform duration-500 group-hover:scale-105"
                          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                          priority={index < 4}
                        />
                      </div>

                      {/* أزرار الإجراءات السريعة */}
                      <div className="absolute top-12 right-3 flex flex-col gap-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        {/* زر المفضلة */}
                        <Button
                          variant="icon"
                          size="sm"
                          className={`p-2 rounded-full shadow-md transform transition-transform duration-300 hover:scale-110 ${
                            useWishlistStore.getState().isInWishlist(product.id)
                              ? "bg-primary-500 text-white"
                              : "bg-white text-slate-700 dark:bg-slate-700 dark:text-white"
                          }`}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleAddToWishlist(product);
                          }}
                          aria-label={currentLanguage === 'ar' ? 'إضافة إلى المفضلة' : 'Add to wishlist'}
                        >
                          <Heart
                            className={`h-5 w-5 ${useWishlistStore.getState().isInWishlist(product.id) && "fill-current"}`}
                          />
                        </Button>

                        {/* زر العرض السريع */}
                        <Button
                          variant="icon"
                          size="sm"
                          className="p-2 rounded-full bg-white text-slate-700 shadow-md dark:bg-slate-700 dark:text-white transform transition-transform duration-300 hover:scale-110"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleQuickView(product);
                          }}
                          aria-label={currentLanguage === 'ar' ? 'عرض سريع' : 'Quick view'}
                        >
                          <Eye className="h-5 w-5" />
                        </Button>
                      </div>
                    </Link>

                    {/* معلومات المنتج */}
                    <div className="p-4 flex flex-col flex-grow">
                      {/* التصنيف والتقييم */}
                      <div className="flex items-center justify-between mb-2">
                        {product.category && (
                          <span className="text-xs px-2 py-0.5 bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300 rounded-full">
                            {product.category}
                          </span>
                        )}

                        <div className="flex items-center text-sm text-slate-500 dark:text-slate-400">
                          <Star className={`h-4 w-4 text-yellow-400 ${currentLanguage === 'ar' ? 'ml-1' : 'mr-1'}`} />
                          <span>
                            {product.rating?.toFixed(1) ?? 'N/A'}
                            {product.reviewCount ? `(${product.reviewCount})` : ''}
                          </span>
                        </div>
                      </div>

                      {/* اسم المنتج */}
                      <Link href={`/${currentLanguage}/shop/${product.slug}`} className="block">
                        <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-1 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 line-clamp-1">
                          {currentLanguage === 'ar' ? (product.name_ar || product.name) : product.name}
                        </h3>
                      </Link>

                      {/* وصف المنتج */}
                      <p className="text-slate-600 dark:text-slate-300 mb-4 line-clamp-2 text-sm">
                        {currentLanguage === 'ar'
                          ? (product.description_ar || product.description)
                          : product.description}
                      </p>

                      {/* السعر والمخزون */}
                      <div className="flex items-center justify-between mb-3 mt-auto">
                        <div className="flex items-baseline gap-1">
                          <span className="text-lg font-bold text-slate-900 dark:text-white">
                            {formatCurrency(product.price)}
                          </span>
                          {product.compareAtPrice && product.compareAtPrice > product.price && (
                            <span className="text-sm text-slate-500 line-through">
                              {formatCurrency(product.compareAtPrice)}
                            </span>
                          )}
                        </div>

                        <div className="text-xs font-medium">
                          {product.stock > 0 ? (
                            <span className="text-green-600 dark:text-green-400">
                              {currentLanguage === 'ar' ? 'متوفر' : 'In Stock'}
                            </span>
                          ) : (
                            <span className="text-red-600 dark:text-red-400">
                              {currentLanguage === 'ar' ? 'نفذ المخزون' : 'Out of Stock'}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* أزرار الإجراءات */}
                      <div className="flex gap-2">
                        {/* زر إضافة إلى السلة */}
                        <Button
                          variant="primary"
                          size="sm"
                          className="flex-1 rounded-md"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleAddToCart(product);
                          }}
                          disabled={product.stock <= 0}
                          aria-label={currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to cart'}
                        >
                          <ShoppingCart className={`h-4 w-4 ${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'}`} />
                          <span>{currentLanguage === 'ar' ? 'أضف للسلة' : 'Add to Cart'}</span>
                        </Button>

                        {/* زر طلب عرض سعر للجملة */}
                        <Button
                          variant="outline"
                          size="sm"
                          className="rounded-md flex items-center gap-1"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setSelectedProduct(product);
                            setShowWholesaleForm(true);
                          }}
                          aria-label={currentLanguage === 'ar' ? 'طلب عرض سعر جملة' : 'Request wholesale quote'}
                        >
                          <Users className="h-4 w-4" />
                          <span>{currentLanguage === 'ar' ? 'طلب سعر جملة' : 'Wholesale Quote'}</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* نافذة العرض السريع للمنتج */}
              {showQuickView && selectedProduct && (
                <QuickView
                  product={selectedProduct}
                  isOpen={showQuickView}
                  onClose={() => setShowQuickView(false)}
                />
              )}

              {/* نموذج طلب عرض سعر للجملة */}
              {showWholesaleForm && selectedProduct && (
                <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
                  <div className="max-w-2xl w-full">
                    <WholesaleQuoteForm
                      onClose={() => {
                        setShowWholesaleForm(false);
                        setSelectedProduct(null);
                      }}
                      isCustomProduct={false}
                      selectedProduct={selectedProduct}
                    />
                  </div>
                </div>
              )}
            </>
          ) : (
            // حالة عدم وجود منتجات مميزة
            <div className="text-center py-12 bg-slate-50 dark:bg-slate-800/30 rounded-lg">
              <Package className="w-12 h-12 mx-auto text-slate-400 dark:text-slate-500 mb-4" />
              <p className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-2">
                {currentLanguage === 'ar' ? 'لا توجد منتجات مميزة حالياً' : 'No featured products available'}
              </p>
              <p className="text-slate-600 dark:text-slate-400 max-w-md mx-auto mb-6">
                {currentLanguage === 'ar'
                  ? 'لم يتم العثور على منتجات مميزة. يرجى التحقق مرة أخرى لاحقاً أو تصفح جميع المنتجات.'
                  : 'No featured products found. Please check back later or browse all products.'}
              </p>
              <Link href={`/${currentLanguage}/shop`}>
                <Button variant="primary">
                  {currentLanguage === 'ar' ? 'تصفح جميع المنتجات' : 'Browse all products'}
                </Button>
              </Link>
            </div>
          )}

          {/* زر عرض جميع المنتجات */}
          {featuredProducts.length > 0 && (
            <div className="mt-12 text-center">
              <Link href={`/${currentLanguage}/shop?featured=true`}>
                <Button
                  variant="outline"
                  size="lg"
                  className="px-8 shadow-sm hover:shadow transition-shadow group"
                  aria-label={currentLanguage === 'ar' ? 'عرض جميع المنتجات المميزة في المتجر' : 'View all featured products in shop'}
                >
                  {currentLanguage === 'ar' ? 'عرض جميع المنتجات المميزة' : 'View All Featured Products'}
                  <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} />
                </Button>
              </Link>
            </div>
          )}
        </div>
      </section>



      {/* أحدث خطوط الإنتاج - Latest Production Lines */}
      <section className="py-16 md:py-24 bg-gradient-to-b from-slate-50 to-white dark:from-slate-900 dark:to-slate-800" dir={direction}>
        <div className="container-custom">
          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12">
            <div className="relative">
              {/* شارة القسم */}
              <div className="inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/50 dark:to-accent-900/50 text-primary-600 dark:text-primary-300 text-sm font-medium mb-4 shadow-sm">
                <Factory className="w-4 h-4 mr-2 text-accent-500" />
                {currentLanguage === 'ar' ? 'تقنيات متطورة' : 'Advanced Technology'}
              </div>

              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white">
                {currentLanguage === 'ar' ? 'أحدث خطوط الإنتاج' : 'Latest Production Lines'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl">
                {currentLanguage === 'ar'
                  ? 'استكشف أحدث خطوط الإنتاج المتطورة لتعزيز كفاءة عملياتك الصناعية وزيادة الإنتاجية.'
                  : 'Explore our cutting-edge production lines to enhance your industrial operations and increase productivity.'}
              </p>
            </div>
            <div className="flex flex-wrap items-center gap-4 mt-6 md:mt-0">
              <Link href={`/${currentLanguage}/production-lines`}>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 shadow-sm hover:shadow transition-shadow"
                >
                  {currentLanguage === 'ar' ? 'عرض جميع خطوط الإنتاج' : 'View All Production Lines'}
                  <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />
                </Button>
              </Link>
              <Link href={`/${currentLanguage}/contact?subject=production-line-inquiry`}>
                <Button
                  variant="primary"
                  className="flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow"
                >
                  {currentLanguage === 'ar' ? 'طلب خط إنتاج مخصص' : 'Request Custom Line'}
                  <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />
                </Button>
              </Link>
            </div>
          </div>

          {/* عرض خطوط الإنتاج */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {productionLines.slice(0, 3).map((line) => (
              <div key={line.id} className="group relative overflow-hidden bg-white dark:bg-slate-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full">
                {/* شارة الفئة */}
                <div className="absolute top-4 left-4 z-10 bg-primary-500/90 text-white px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm">
                  {line.category}
                </div>

                {/* صورة خط الإنتاج */}
                <div className="relative aspect-video overflow-hidden">
                  <EnhancedImage
                    src={line.images[0]}
                    alt={line.name}
                    fill={true}
                    objectFit="cover"
                    progressive={true}
                    placeholder="shimmer"
                    className="transition-transform duration-500 group-hover:scale-105"
                    containerClassName="w-full h-full"
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0" />
                </div>

                <div className="p-6 flex-grow flex flex-col">
                  {/* اسم خط الإنتاج */}
                  <Link href={`/${currentLanguage}/production-lines/${line.slug}`} className="block">
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                      {line.name}
                    </h3>
                  </Link>

                  {/* وصف خط الإنتاج */}
                  <p className="text-slate-600 dark:text-slate-300 mb-4 line-clamp-2">
                    {line.description}
                  </p>

                  {/* سعة الإنتاج */}
                  <div className="flex items-center text-sm text-slate-700 dark:text-slate-300 mb-4">
                    <Gauge className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0 w-4 h-4 text-primary-500`} />
                    <span>
                      {currentLanguage === 'ar' ? `سعة الإنتاج: ${line.capacity}` : `Capacity: ${line.capacity}`}
                    </span>
                  </div>

                  {/* المميزات الرئيسية */}
                  <div className="space-y-2 mb-6">
                    {line.features.slice(0, 3).map((feature, index) => (
                      <div key={index} className="flex items-start text-sm">
                        <div className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} mt-0.5 text-accent-500`}>
                          <CheckCircle className="w-4 h-4" />
                        </div>
                        <span className="text-slate-600 dark:text-slate-300">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="mt-auto space-y-3">
                    <Link href={`/${currentLanguage}/production-lines/${line.slug}`} className="block w-full">
                      <Button
                        variant="outline"
                        className="w-full flex items-center justify-center gap-2 group"
                      >
                        <span>{currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details'}</span>
                        <ArrowRight className={`w-4 h-4 transition-transform duration-300 group-hover:translate-x-1 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />
                      </Button>
                    </Link>
                    <Link href={`/${currentLanguage}/production-lines/${line.slug}?action=quote`} className="block w-full">
                      <Button
                        variant="primary"
                        className="w-full flex items-center justify-center gap-2"
                      >
                        {currentLanguage === 'ar' ? 'طلب عرض سعر' : 'Request Quote'}
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* زر عرض جميع خطوط الإنتاج */}
          <div className="mt-12 text-center">
            <Link href={`/${currentLanguage}/production-lines`}>
              <Button
                variant="outline"
                size="lg"
                className="px-8 shadow-sm hover:shadow transition-shadow group"
              >
                {currentLanguage === 'ar' ? 'استكشاف جميع خطوط الإنتاج' : 'Explore All Production Lines'}
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* تصفية المخزون وأفضل العروض */}
      <section className="py-16 md:py-24 bg-gradient-to-b from-background to-primary-50" dir={direction}>
        <div className="container-custom">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {language === 'ar' ? 'تصفية المخزون بأسعار زهيدة' : 'Clearance Stock at Low Prices'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl">
                {language === 'ar'
                  ? 'فرصة ذهبية للحصول على منتجات بكميات كبيرة وبأسعار مخفضة. مثالية للشركات التي تبحث عن توفير التكاليف.'
                  : 'Golden opportunity to get products in bulk quantities at discounted prices. Perfect for businesses looking to save costs.'}
              </p>
            </div>
            <div className="flex items-center gap-4 mt-6 md:mt-0">
              <Link href={`/${currentLanguage}/clearance`}>
                <Button
                  variant="primary"
                  className="flex items-center gap-2"
                >
                  {currentLanguage === 'ar' ? 'عرض جميع منتجات التصفية' : 'View All Clearance Items'}
                  <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />
                </Button>
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {clearanceItems.slice(0, 3).map((item: ClearanceItem) => (
              <div key={item.id} className="group relative overflow-hidden bg-white dark:bg-slate-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                {/* شارة الخصم */}
                <div className="absolute top-4 left-4 z-10 bg-error-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                  {currentLanguage === 'ar'
                    ? `خصم ${Math.round((1 - item.clearancePrice / item.originalPrice) * 100)}%`
                    : `${Math.round((1 - item.clearancePrice / item.originalPrice) * 100)}% OFF`}
                </div>

                {/* شارة تصفية المخزون */}
                <div className="absolute top-4 right-4 z-10 bg-amber-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                  {currentLanguage === 'ar' ? 'تصفية' : 'Clearance'}
                </div>

                {/* صورة المنتج */}
                <div className="relative aspect-video overflow-hidden">
                  <EnhancedImage
                    src={item.image}
                    alt={item.name}
                    fill={true}
                    objectFit="cover"
                    progressive={true}
                    placeholder="shimmer"
                    className="transition-transform duration-500 group-hover:scale-105"
                    containerClassName="w-full h-full"
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0" />
                </div>

                <div className="p-6">
                  {/* تصنيف المنتج وحالته */}
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-xs font-medium px-2.5 py-0.5 rounded-full bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300">
                      {item.category}
                    </span>
                    <span className={`text-xs font-medium px-2.5 py-0.5 rounded-full ${
                      item.condition === 'new' ? 'bg-success-50 dark:bg-success-900/30 text-success-700 dark:text-success-300' :
                      item.condition === 'like-new' ? 'bg-info-50 dark:bg-info-900/30 text-info-700 dark:text-info-300' :
                      'bg-warning-50 dark:bg-warning-900/30 text-warning-700 dark:text-warning-300'
                    }`}>
                      {currentLanguage === 'ar'
                        ? (item.condition === 'new' ? 'جديد' : item.condition === 'like-new' ? 'كالجديد' : 'مستعمل')
                        : item.condition}
                    </span>
                  </div>

                  {/* اسم المنتج */}
                  <Link
                    href={`/${currentLanguage}/clearance/${item.id}`}
                    className="block"
                  >
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2 line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                      {item.name}
                    </h3>
                  </Link>

                  {/* وصف المنتج */}
                  <p className="text-slate-600 dark:text-slate-300 text-sm mb-4 line-clamp-2">
                    {item.description}
                  </p>

                  {/* معلومات إضافية */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                      <Package size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0`} />
                      {currentLanguage === 'ar'
                        ? `الحد الأدنى للطلب: ${item.minOrder} وحدة`
                        : `Min. Order: ${item.minOrder} units`}
                    </div>
                    <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
                      <MapPin size={16} className={`${currentLanguage === 'ar' ? 'ml-2' : 'mr-2'} flex-shrink-0`} />
                      {item.location}
                    </div>
                  </div>

                  {/* السعر والكمية المتوفرة */}
                  <div className="flex items-end justify-between mb-4">
                    <div>
                      <div className="text-2xl font-bold text-slate-900 dark:text-white">
                        {formatCurrency(item.clearancePrice)}
                      </div>
                      <div className="text-sm text-slate-500 dark:text-slate-400 line-through">
                        {formatCurrency(item.originalPrice)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-slate-600 dark:text-slate-400">
                        {currentLanguage === 'ar' ? 'الكمية المتوفرة:' : 'Available:'}
                      </div>
                      <div className="font-medium text-slate-900 dark:text-white">
                        {currentLanguage === 'ar'
                          ? `${item.availableQuantity} وحدة`
                          : `${item.availableQuantity} units`}
                      </div>
                    </div>
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="space-y-2">
                    <Button
                      variant="primary"
                      className="w-full flex items-center justify-center gap-2"
                      onClick={(e) => {
                        e.preventDefault();
                        setSelectedClearanceItem(item);
                        setShowClearanceQuoteForm(true);
                      }}
                    >
                      <Send size={16} />
                      {currentLanguage === 'ar' ? 'طلب عرض سعر للكمية' : 'Request Bulk Quote'}
                    </Button>
                    <Link
                      href={`/${currentLanguage}/clearance/${item.id}`}
                      className="block text-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                    >
                      {currentLanguage === 'ar' ? 'عرض التفاصيل الكاملة' : 'View Full Details'}
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <Link href={`/${currentLanguage}/clearance`}>
              <Button
                variant="outline"
                size="lg"
                className="px-8 shadow-sm hover:shadow transition-shadow group"
              >
                {currentLanguage === 'ar' ? 'عرض جميع منتجات التصفية' : 'View All Clearance Items'}
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* نموذج طلب عرض سعر لمنتجات التصفية */}
      {showClearanceQuoteForm && selectedClearanceItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full">
            <Card className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-xl">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                  {currentLanguage === 'ar'
                    ? `طلب عرض سعر لمنتج التصفية: ${selectedClearanceItem.name}`
                    : `Request Quote for Clearance Item: ${selectedClearanceItem.name}`}
                </h3>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    setShowClearanceQuoteForm(false);
                    setSelectedClearanceItem(null);
                  }}
                  className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
                >
                  <X size={20} />
                </Button>
              </div>

              <WholesaleQuoteForm
                onClose={() => {
                  setShowClearanceQuoteForm(false);
                  setSelectedClearanceItem(null);
                }}
                isCustomProduct={false}
                product={clearanceProductAdapter(selectedClearanceItem)}
              />
            </Card>
          </div>
        </div>
      )}

      {/* Most Requested Services - قسم الخدمات الأكثر طلباً */}
      <section className="py-20 md:py-28 bg-gradient-to-b from-white via-primary-50/30 to-primary-50 dark:from-slate-900 dark:via-slate-900/80 dark:to-primary-900/20" dir={direction}>
        <div className="container-custom">
          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}
          <div className="text-center mb-20 relative">
            {/* شارة القسم */}
            <div className="inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/50 dark:to-accent-900/50 text-primary-600 dark:text-primary-300 text-sm font-medium mb-6 shadow-sm">
              <Star className="w-4 h-4 mr-2 text-accent-500" />
              {language === 'ar' ? 'خدمات متميزة' : 'Premium Services'}
            </div>

            {/* العنوان الرئيسي مع تأثير التدرج */}
            <h2 className="text-3xl md:text-5xl font-bold mb-6 leading-tight relative inline-block">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary-600 to-accent-600 dark:from-primary-400 dark:to-accent-400">
                {language === 'ar' ? 'الخدمات الأكثر طلباً' : 'Most Requested Services'}
              </span>
              <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full transform scale-x-100"></div>
            </h2>

            {/* الوصف مع تحسين الخط والمساحة */}
            <p className="text-lg md:text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed">
              {language === 'ar' ? 'اكتشف مجموعتنا الشاملة من خدمات الأعمال المصممة خصيصاً لتحسين عملياتك وتعزيز النمو وتحقيق النجاح المستدام.' : 'Discover our comprehensive range of business services tailored to optimize your operations, drive growth, and achieve sustainable success.'}
            </p>

            {/* زخرفة خلفية */}
            <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 w-64 h-64 bg-primary-500/5 dark:bg-primary-500/10 rounded-full blur-3xl -z-10"></div>
          </div>

          {/* شبكة بطاقات الخدمات مع تباعد محسن */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
            {services.slice(0, 6).map((service: Service, index: number) => {
              const Icon = service.icon === 'Search' ? Search :
                          service.icon === 'Package' ? Package :
                          service.icon === 'Truck' ? Truck :
                          service.icon === 'FileCheck' ? FileCheck :
                          service.icon === 'Users' ? Users :
                          service.icon === 'ClipboardList' ? FileText :
                          Building2;

              return (
                <div
                  key={service.id}
                >
                  <div className="group h-full perspective-1000">
                    <Card className="relative overflow-hidden h-full hover:shadow-xl transition-all duration-500 border border-slate-200/80 dark:border-slate-700/50 hover:border-primary-200 dark:hover:border-primary-800/70 flex flex-col transform-gpu group-hover:translate-y-[-8px] group-hover:rotate-y-1">
                      {/* حدود متدرجة متحركة */}
                      <div className="absolute inset-0 rounded-xl border-2 border-transparent bg-gradient-to-br from-primary-500/0 via-primary-500/0 to-accent-500/0 group-hover:from-primary-500/20 group-hover:via-primary-500/10 group-hover:to-accent-500/20 transition-colors duration-700 -z-10"></div>

                      {/* شريط علوي متدرج */}
                      <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-primary-500 to-accent-500 transform origin-left transition-transform duration-500 scale-x-0 group-hover:scale-x-100"></div>

                      {/* شارة الخدمة */}
                      <div className="absolute top-6 right-6 bg-primary-50/90 dark:bg-primary-900/50 text-primary-600 dark:text-primary-300 px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm shadow-sm">
                        {currentLanguage === 'ar' ? 'خدمة احترافية' : 'Professional Service'}
                      </div>

                      <div className="p-8 flex-grow flex flex-col">
                        {/* أيقونة الخدمة مع خلفية متحركة */}
                        <div className="relative mb-8 group-hover:scale-105 transition-transform duration-500">
                          <div className="w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-50 dark:from-primary-900/60 dark:to-primary-800/30 rounded-2xl flex items-center justify-center text-primary-600 dark:text-primary-400 transform transition-all duration-500 group-hover:shadow-lg group-hover:shadow-primary-500/10">
                            <Icon size={36} className="transform transition-transform duration-700 group-hover:rotate-12" />
                          </div>
                          <div className="absolute -inset-2 bg-primary-200 dark:bg-primary-800/50 rounded-3xl -z-10 blur-xl opacity-0 group-hover:opacity-40 transition-opacity duration-700"></div>
                        </div>

                        {/* عنوان الخدمة */}
                        <h3 className="text-2xl font-bold mb-4 text-slate-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                          {currentLanguage === 'ar' ? service.name_ar || service.name : service.name}
                        </h3>

                        {/* وصف الخدمة */}
                        <p className="text-slate-600 dark:text-slate-300 mb-6 line-clamp-3 leading-relaxed">
                          {currentLanguage === 'ar' ? service.description_ar || service.description : service.description}
                        </p>

                        {/* الميزات الرئيسية مع تحسين العرض */}
                        <div className="space-y-3 mb-8 flex-grow">
                          {(currentLanguage === 'ar' ? service.features_ar || service.features : service.features).slice(0, 4).map((feature: string, fIndex: number) => (
                            <div key={fIndex} className="flex items-start text-sm group/feature">
                              <div className="mt-0.5 transform transition-transform duration-300 group-hover/feature:scale-110">
                                <CheckCircle className={`w-5 h-5 text-accent-500 dark:text-accent-400 ${currentLanguage === 'ar' ? 'ml-3' : 'mr-3'} flex-shrink-0`} />
                              </div>
                              <span className="text-slate-700 dark:text-slate-300 group-hover/feature:text-primary-700 dark:group-hover/feature:text-primary-300 transition-colors">{feature}</span>
                            </div>
                          ))}
                        </div>

                        {/* أزرار الإجراءات مع تحسين التفاعل */}
                        <div className="mt-auto">
                          <Link href={`/${currentLanguage}/services/${service.slug}`} className="block w-full">
                            <Button
                              variant="primary"
                              className="w-full flex items-center justify-center gap-2 group-hover:shadow-md transition-all duration-300 group-hover:shadow-primary-500/20 py-3"
                            >
                              {currentLanguage === 'ar' ? 'احجز الخدمة الآن' : 'Book This Service'}
                              <ArrowRight className={`w-4 h-4 transition-transform duration-300 group-hover:translate-x-1 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />
                            </Button>
                          </Link>
                        </div>
                      </div>

                      {/* شريط سفلي متدرج */}
                      <div className="absolute bottom-0 left-0 w-full h-1.5 bg-gradient-to-r from-accent-500 to-primary-500 transform origin-right transition-transform duration-500 scale-x-0 group-hover:scale-x-100"></div>
                    </Card>
                  </div>
                </div>
              );
            })}
          </div>

          {/* زر عرض جميع الخدمات مع تحسين التصميم */}
          <div className="mt-20 text-center">
            <Link href={`/${currentLanguage}/services`}>
              <Button
                variant="outline"
                size="lg"
                className="px-10 py-6 text-lg border-2 hover:border-primary-500 dark:hover:border-primary-400 group shadow-sm hover:shadow-md transition-shadow"
              >
                {currentLanguage === 'ar' ? 'استكشاف جميع خدماتنا الاحترافية' : 'Explore All Our Professional Services'}
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-3' : 'ml-3'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-2 ${currentLanguage === 'ar' ? 'rotate-rtl' : ''}`} />
              </Button>
            </Link>
          </div>


        </div>
      </section>

      {/* CTA Section - قسم الدعوة للعمل المحسن */}
      <section className="relative py-12 md:py-16 overflow-hidden" dir={direction}>
        {/* خلفية متدرجة مع تأثيرات */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-800 via-primary-700 to-secondary-800 z-0"></div>

        {/* طبقة التراكب مع تأثير الشبكة */}
        <div className="absolute inset-0 bg-black/20 z-10"></div>
        <div className="absolute inset-0 z-10 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full">
            <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <defs>
                <pattern id="cta-grid" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse">
                  <path d="M10 0H0V10" fill="none" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5" />
                </pattern>
              </defs>
              <rect x="0" y="0" width="100" height="100" fill="url(#cta-grid)" />
            </svg>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div className="container-custom relative z-20">
          <div className="max-w-4xl mx-auto">
            {/* بطاقة محتوى CTA مع تأثيرات زجاجية */}
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 md:p-8 border border-white/20 shadow-xl relative overflow-hidden">
              {/* تأثير التوهج */}
              <div className="absolute -top-20 -left-20 w-40 h-40 bg-accent-500/30 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-20 -right-20 w-40 h-40 bg-primary-500/30 rounded-full blur-3xl"></div>

              <div className="text-center relative">
                {/* العنوان الرئيسي مع تأثير متدرج */}
                <h2 className="text-3xl md:text-4xl font-bold mb-4 leading-tight">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-white via-primary-100 to-white relative inline-block">
                    {language === 'ar' ? 'هل أنت مستعد لتحويل أعمالك؟' : 'Ready to Transform Your Business?'}
                  </span>
                </h2>

                {/* وصف مع تأثيرات متحركة */}
                <p className="text-base md:text-lg text-white/90 mb-6 max-w-2xl mx-auto">
                  {language === 'ar'
                    ? 'انضم إلى آلاف العملاء الراضين الذين رفعوا مستوى عملياتهم مع حلولنا الشاملة.'
                    : 'Join thousands of satisfied customers who have elevated their operations with our comprehensive solutions.'}
                </p>

                {/* أزرار الدعوة للعمل المحسنة */}
                <div className="flex flex-wrap justify-center gap-4">
                  <HeroButton
                    href={`/${currentLanguage}/shop`}
                    variant="accent"
                    className="py-2.5 px-6 shadow-lg hover:shadow-accent-500/30 transition-all duration-300 group"
                  >
                    <span className="relative z-10">{currentLanguage === 'ar' ? 'استكشف منتجاتنا' : 'Explore Our Products'}</span>
                    <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} relative z-10 transition-transform duration-300 group-hover:translate-x-1`} />
                  </HeroButton>
                  <HeroButton
                    href={`/${currentLanguage}/contact`}
                    variant="outline"
                    className="py-2.5 px-6 border backdrop-blur-sm hover:bg-white/10 transition-all duration-300"
                  >
                    <span className="relative z-10">{currentLanguage === 'ar' ? 'طلب استشارة' : 'Request Consultation'}</span>
                  </HeroButton>
                </div>

                {/* شارة الثقة */}
                <div className="mt-6 inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-sm border border-white/20">
                  <Shield className="w-4 h-4 text-accent-300 mr-1.5" />
                  <span className="text-white/90 text-xs font-medium">
                    {currentLanguage === 'ar' ? 'ضمان الجودة 100%' : '100% Quality Guarantee'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Latest Industry Insights - قسم أحدث رؤى الصناعة */}
      <section className="py-16 md:py-24 bg-gradient-to-b from-background to-slate-50 dark:from-slate-900 dark:to-slate-800/50" dir={direction}>
        <div className="container-custom">
          {/* عنوان القسم مع تأثيرات بصرية متقدمة */}
          <div className="relative mb-16">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div className="relative mb-8 md:mb-0">
                {/* شارة القسم */}
                <div className="inline-flex items-center justify-center px-6 py-2.5 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/50 dark:to-accent-900/50 text-primary-600 dark:text-primary-300 text-sm font-medium mb-4 shadow-sm">
                  <FileText className="w-4 h-4 mr-2 text-accent-500" />
                  {currentLanguage === 'ar' ? 'رؤى وتحليلات صناعية' : 'Industry Insights'}
                </div>

                <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white">
                  {currentLanguage === 'ar' ? 'أحدث المقالات والتحليلات' : 'Latest Articles & Analysis'}
                </h2>
                <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl">
                  {currentLanguage === 'ar'
                    ? 'اكتشف أحدث الاتجاهات والرؤى في مجال الأعمال والصناعة من خبرائنا المتخصصين'
                    : 'Discover the latest trends and insights in business and industry from our expert specialists'}
                </p>

                {/* خط زخرفي */}
                <div className="absolute -bottom-4 left-0 w-20 h-1 bg-accent-500 rounded-full"></div>
              </div>

              <div className="flex flex-wrap items-center gap-4">
                <Link href={`/${currentLanguage}/blog`}>
                  <Button
                    variant="outline"
                    className="flex items-center gap-2 shadow-sm hover:shadow transition-shadow"
                  >
                    {currentLanguage === 'ar' ? 'جميع المقالات' : 'All Articles'}
                    <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />
                  </Button>
                </Link>
                <Link href={`/${currentLanguage}/blog/featured`}>
                  <Button
                    variant="primary"
                    className="flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow"
                  >
                    {currentLanguage === 'ar' ? 'المقالات المميزة' : 'Featured Articles'}
                    <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* عرض المقالات المميزة والأحدث - 4 مقالات في صف واحد */}
          <div className="relative mt-8 mb-16">
            {/* خلفية زخرفية */}
            <div className="absolute inset-0 -z-10 bg-gradient-to-r from-primary-50/30 to-accent-50/30 dark:from-primary-900/10 dark:to-accent-900/10 rounded-3xl transform -skew-y-1"></div>

            {/* شريط زخرفي */}
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded-t-xl"></div>

            {/* المقالات */}
            <div className="py-10 px-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
                {(() => {
                  // تحضير المقالات المميزة
                  const featuredPosts = blogPosts.filter(post => post.featured);

                  // تحضير المقالات الأحدث
                  const latestPosts = blogPosts
                    .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
                    .filter(post => !post.featured);

                  // دمج المقالات المميزة والأحدث، مع الأولوية للمميزة
                  const postsToShow = [...featuredPosts, ...latestPosts].slice(0, 4);

                  return postsToShow.map((post, index) => {
                    const isFeatured = post.featured;

                    return (
                      <div key={post.id} className="group relative">
                        {/* بطاقة المقال */}
                        <div className="relative h-full bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-md group-hover:shadow-xl transition-all duration-500 flex flex-col border border-slate-200/50 dark:border-slate-700/50 transform group-hover:-translate-y-1 group-hover:scale-[1.01]">
                          {/* شارة المقال المميز */}
                          {isFeatured && (
                            <div className="absolute top-3 right-3 z-10">
                              <span className="bg-accent-500 text-white text-xs font-bold px-2.5 py-1 rounded-full shadow-lg flex items-center">
                                <Star className="w-3 h-3 mr-1" fill="white" />
                                {currentLanguage === 'ar' ? 'مميز' : 'Featured'}
                              </span>
                            </div>
                          )}

                          {/* صورة المقال */}
                          <Link href={`/${currentLanguage}/blog/${post.slug}`} className="block">
                            <div className="relative aspect-[4/3] overflow-hidden">
                              <EnhancedImage
                                src={post.coverImage}
                                alt={post.title}
                                fill={true}
                                objectFit="cover"
                                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
                                progressive={true}
                                placeholder="shimmer"
                                className="transition-transform duration-700 group-hover:scale-105"
                              />
                              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/10" />

                              {/* فئة المقال */}
                              <div className="absolute top-3 left-3">
                                <span className="px-2.5 py-1 rounded-lg text-xs font-medium bg-white/90 backdrop-blur-sm text-primary-700 dark:text-primary-700 shadow-sm">
                                  {post.category}
                                </span>
                              </div>

                              {/* تاريخ النشر */}
                              <div className="absolute bottom-3 left-3 right-3 flex justify-between items-center">
                                <div className="flex items-center gap-2 text-white text-xs bg-black/30 backdrop-blur-sm px-2.5 py-1 rounded-lg">
                                  <Calendar className="w-3 h-3" />
                                  {new Date(post.publishedAt).toLocaleDateString(currentLanguage === 'ar' ? 'ar-SA' : 'en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric'
                                  })}
                                </div>
                                <div className="flex items-center gap-1 text-white text-xs bg-black/30 backdrop-blur-sm px-2.5 py-1 rounded-lg">
                                  <Clock className="w-3 h-3" />
                                  {post.readTime}
                                </div>
                              </div>
                            </div>
                          </Link>

                          {/* محتوى المقال */}
                          <div className="p-5 flex-grow flex flex-col">
                            <Link href={`/${currentLanguage}/blog/${post.slug}`} className="block group-hover:text-primary-600 dark:group-hover:text-primary-400">
                              <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-3 transition-colors line-clamp-2">
                                {post.title}
                              </h3>
                            </Link>

                            <p className="text-slate-600 dark:text-slate-400 mb-4 line-clamp-2 text-sm">
                              {post.excerpt}
                            </p>

                            {/* معلومات الكاتب */}
                            <div className="mt-auto pt-4 border-t border-slate-100 dark:border-slate-700/50 flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-white dark:border-slate-700 shadow-sm">
                                  <EnhancedImage
                                    src={post.authorImage}
                                    alt={post.author}
                                    fill={true}
                                    objectFit="cover"
                                    rounded="full"
                                    progressive={true}
                                    placeholder="blur"
                                    className="w-full h-full"
                                    containerClassName="w-full h-full"
                                  />
                                </div>
                                <div>
                                  <p className="text-xs font-medium text-slate-900 dark:text-white line-clamp-1">
                                    {post.author}
                                  </p>
                                  <p className="text-xs text-slate-500 dark:text-slate-400">
                                    {post.authorTitle.split(' ')[0]}
                                  </p>
                                </div>
                              </div>

                              {/* زر قراءة المزيد */}
                              <Link href={`/${currentLanguage}/blog/${post.slug}`} className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 transition-transform group-hover:scale-110">
                                <ArrowRight className={`w-4 h-4 ${currentLanguage === 'ar' ? 'rotate-180' : ''}`} />
                              </Link>
                            </div>
                          </div>
                        </div>

                        {/* تأثير الظل */}
                        <div className="absolute -bottom-2 left-2 right-2 h-full rounded-xl bg-gradient-to-r from-primary-500/10 to-accent-500/10 blur-xl -z-10 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                      </div>
                    );
                  });
                })()}
              </div>
            </div>
          </div>

          <div className="mt-12 text-center">
            <Link href={`/${currentLanguage}/blog`}>
              <Button
                variant="outline"
                size="lg"
                className="px-8 shadow-sm hover:shadow transition-shadow group"
              >
                {currentLanguage === 'ar' ? 'استكشاف جميع المقالات' : 'Explore All Articles'}
                <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2'} h-5 w-5 transition-transform duration-300 group-hover:translate-x-1`} />
              </Button>
            </Link>
          </div>

          {/* Newsletter subscription - قسم الاشتراك بالنشرة البريدية */}
          <div className="mt-20 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-primary-600 to-accent-600 rounded-2xl opacity-90"></div>
            <div className="absolute inset-0 bg-[url('/images/pattern-dots.svg')] bg-repeat opacity-10"></div>

            {/* تأثير الزخرفة */}
            <div className="absolute -top-5 -left-5 w-20 h-20 bg-primary-300 rounded-full blur-3xl opacity-30"></div>
            <div className="absolute -bottom-5 -right-5 w-20 h-20 bg-accent-300 rounded-full blur-3xl opacity-30"></div>

            <div className="relative p-8 md:p-12 rounded-2xl overflow-hidden">
              <div className="max-w-4xl mx-auto">
                <div className="flex flex-col md:flex-row items-center justify-between gap-8">
                  <div className="text-white text-center md:text-left md:max-w-md">
                    <div className="inline-flex items-center justify-center px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-sm text-white text-sm font-medium mb-4">
                      <Bell className="w-4 h-4 mr-2" />
                      {currentLanguage === 'ar' ? 'ابق على اطلاع' : 'Stay Updated'}
                    </div>
                    <h3 className="text-2xl md:text-3xl font-bold mb-4">
                      {currentLanguage === 'ar'
                        ? 'اشترك في نشرتنا الإخبارية للحصول على آخر التحديثات'
                        : 'Subscribe to Our Newsletter for Latest Updates'}
                    </h3>
                    <p className="text-white/80 text-base md:text-lg">
                      {currentLanguage === 'ar'
                        ? 'احصل على آخر العروض والمنتجات الجديدة والتحديثات الصناعية مباشرة إلى بريدك الإلكتروني.'
                        : 'Get the latest offers, new products, and industry updates delivered directly to your inbox.'}
                    </p>
                  </div>

                  <div className="w-full md:w-auto bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20 shadow-xl">
                    <NewsletterForm
                      variant="inline"
                      className="w-full md:min-w-[400px]"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {showWholesaleForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full">
            <WholesaleQuoteForm
              onClose={() => {
                setShowWholesaleForm(false);
                setSelectedProduct(null);
                setSelectedService(null);
              }}
              isCustomProduct={false}
              product={selectedProduct}
              serviceName={selectedService?.name}
            />
          </div>
        </div>
      )}

      {/* Quick View Modal */}
      <QuickView
        product={selectedProduct}
        isOpen={showQuickView}
        onClose={() => setShowQuickView(false)}
      />
    </div>
  );
}

export default HomePage;