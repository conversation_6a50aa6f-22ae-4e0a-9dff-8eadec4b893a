'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { services } from '../../data/services';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Search, Package, Truck, FileCheck, Users, ArrowRight, CheckCircle, Calendar } from 'lucide-react';
import { ServiceInquiryForm } from '../../components/forms/ServiceInquiryForm';
import { useLanguageStore } from '../../stores/languageStore';
import { useTranslation } from '../../translations';
import { ScrollAnimation, ScrollStagger, HoverAnimation } from '../../components/ui/animations';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';

const icons = {
  Search,
  Package,
  Truck,
  FileCheck,
  Users
};

export default function ServicePage({ slug }: { slug: string }) {
  const router = useRouter();
  const service = services.find(s => s.slug === slug);
  const [showInquiryForm, setShowInquiryForm] = useState(false);
  const { language } = useLanguageStore();
  const { t, locale } = useTranslation();
  const { isDarkMode } = useThemeStore();

  // استخدام اللغة من المسار أو من المتجر
  const currentLanguage = (locale as 'ar' | 'en') || language;

  if (!service) {
    return (
      <div className="container-custom py-12">
        <ScrollAnimation animation="fade" delay={0.1}>
          <Card className="p-8 text-center">
            <h1 className="text-2xl font-semibold mb-4 text-slate-900 dark:text-white">
              {currentLanguage === 'ar' ? 'الخدمة غير موجودة' : 'Service Not Found'}
            </h1>
            <p className="text-slate-600 dark:text-slate-300">
              {currentLanguage === 'ar'
                ? 'الخدمة التي تبحث عنها غير موجودة.'
                : 'The service you\'re looking for doesn\'t exist.'}
            </p>
            <div className="mt-6">
              <HoverAnimation animation="scale">
                <Button onClick={() => router.push(`/${currentLanguage}/services`)}>
                  {currentLanguage === 'ar' ? 'العودة إلى الخدمات' : 'Back to Services'}
                </Button>
              </HoverAnimation>
            </div>
          </Card>
        </ScrollAnimation>
      </div>
    );
  }

  const Icon = icons[service.icon as keyof typeof icons];

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-800 to-slate-900 text-white py-20">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.1}>
            <div className="max-w-3xl mx-auto text-center">
              <div className="inline-flex justify-center items-center w-20 h-20 rounded-full bg-primary-500/20 text-primary-300 mb-6">
                <Icon size={36} />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                {currentLanguage === 'ar' ? service.name_ar || service.name : service.name}
              </h1>
              <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
                {currentLanguage === 'ar' ? service.description_ar || service.description : service.description}
              </p>
              <HoverAnimation animation="scale">
                <Button
                  size="lg"
                  variant="accent"
                  className="px-8"
                  onClick={() => setShowInquiryForm(true)}
                >
                  {currentLanguage === 'ar' ? 'احجز الآن' : 'Book Now'}
                  <ArrowRight className={`${currentLanguage === 'ar' ? 'mr-2' : 'ml-2'} h-5 w-5`} />
                </Button>
              </HoverAnimation>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Features Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-900" : "bg-white")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'ميزات الخدمة' : 'Service Features'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'حلول شاملة مصممة خصيصًا لتلبية احتياجات عملك'
                  : 'Comprehensive solutions tailored to your business needs'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="up"
            staggerDelay={0.1}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {service.features.map((feature, index) => (
              <HoverAnimation key={index} animation="lift">
                <Card className="p-6 h-full">
                  <div className="flex items-start h-full">
                    <div className="flex-shrink-0">
                      <CheckCircle className={`h-6 w-6 text-primary-500 ${currentLanguage === 'ar' ? 'ml-2' : ''}`} />
                    </div>
                    <div className={`${currentLanguage === 'ar' ? 'mr-4' : 'ml-4'}`}>
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                        {currentLanguage === 'ar' && service.features_ar ? service.features_ar[index] || feature : feature}
                      </h3>
                    </div>
                  </div>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* Process Section */}
      <section className={cn("py-16 md:py-24", isDarkMode ? "bg-slate-800" : "bg-slate-50")}>
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {currentLanguage === 'ar' ? 'عمليتنا' : 'Our Process'}
              </h2>
              <p className="text-lg text-slate-600 dark:text-slate-300">
                {currentLanguage === 'ar'
                  ? 'نهج مبسط لتقديم نتائج استثنائية'
                  : 'A streamlined approach to deliver exceptional results'}
              </p>
            </div>
          </ScrollAnimation>

          <ScrollStagger
            animation="slide"
            direction="right"
            staggerDelay={0.15}
            className="grid grid-cols-1 md:grid-cols-4 gap-8"
          >
            {[
              {
                title: currentLanguage === 'ar' ? "استشارة أولية" : "Initial Consultation",
                description: currentLanguage === 'ar'
                  ? "نبدأ باستشارة شاملة لفهم احتياجاتك ومتطلباتك المحددة."
                  : "We begin with a thorough consultation to understand your specific needs and requirements."
              },
              {
                title: currentLanguage === 'ar' ? "حل مخصص" : "Custom Solution",
                description: currentLanguage === 'ar'
                  ? "يقوم خبراؤنا بتطوير حل مخصص بناءً على أهداف عملك."
                  : "Our experts develop a tailored solution based on your business objectives."
              },
              {
                title: currentLanguage === 'ar' ? "التنفيذ" : "Implementation",
                description: currentLanguage === 'ar'
                  ? "ننفذ الخطة المتفق عليها بدقة واهتمام بالتفاصيل."
                  : "We execute the agreed plan with precision and attention to detail."
              },
              {
                title: currentLanguage === 'ar' ? "الدعم المستمر" : "Ongoing Support",
                description: currentLanguage === 'ar'
                  ? "مراقبة ودعم مستمر لضمان النتائج المثلى."
                  : "Continuous monitoring and support to ensure optimal results."
              }
            ].map((step, index) => (
              <HoverAnimation key={index} animation="lift" className="relative text-center">
                <div className="bg-primary-500 text-white rounded-full h-14 w-14 flex items-center justify-center font-bold text-lg mb-4 mx-auto shadow-md">
                  {index + 1}
                </div>
                <Card className="p-6 h-full">
                  <h3 className="text-xl font-semibold mb-3 text-slate-900 dark:text-white">{step.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300">{step.description}</p>
                </Card>
              </HoverAnimation>
            ))}
          </ScrollStagger>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-primary-500 text-white">
        <div className="container-custom">
          <ScrollAnimation animation="fade" delay={0.2}>
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                {currentLanguage === 'ar' ? 'هل أنت مستعد للبدء؟' : 'Ready to Get Started?'}
              </h2>
              <p className="text-xl mb-8 text-primary-50">
                {currentLanguage === 'ar'
                  ? `اتصل بفريقنا لمناقشة كيف يمكن لخدمة ${service.name_ar || service.name} أن تفيد عملك.`
                  : `Contact our team to discuss how our ${service.name ? service.name.toLowerCase() : ''} can benefit your business.`}
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <HoverAnimation animation="scale">
                  <Button
                    size="lg"
                    variant="accent"
                    className="px-8"
                    onClick={() => setShowInquiryForm(true)}
                  >
                    {currentLanguage === 'ar' ? 'احجز الآن' : 'Book Now'}
                  </Button>
                </HoverAnimation>
                <HoverAnimation animation="scale">
                  <Button
                    size="lg"
                    variant="outline"
                    className="bg-transparent border-white text-white hover:bg-primary-600 px-8"
                    onClick={() => setShowInquiryForm(true)}
                  >
                    {currentLanguage === 'ar' ? 'معرفة المزيد' : 'Learn More'}
                  </Button>
                </HoverAnimation>
              </div>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Inquiry Form Modal */}
      {showInquiryForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <ScrollAnimation animation="scale" duration={0.3}>
            <div className="max-w-2xl w-full">
              <ServiceInquiryForm
                serviceName={currentLanguage === 'ar' ? service.name_ar || service.name : service.name}
                onClose={() => setShowInquiryForm(false)}
              />
            </div>
          </ScrollAnimation>
        </div>
      )}
    </div>
  );
}