globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/[locale]/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/providers.tsx":{"*":{"id":"(ssr)/./src/app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/RootLayout.tsx":{"*":{"id":"(ssr)/./src/components/layout/RootLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/not-found.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/terms-of-service/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/terms-of-service/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/privacy-policy/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/privacy-policy/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/returns/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/returns/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/shipping/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/shipping/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/faq/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/faq/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/contact/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/shop/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/shop/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/production-lines/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/production-lines/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/services/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/services/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/blog/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/blog/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/clearance/page.tsx":{"*":{"id":"(ssr)/./src/app/[locale]/clearance/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/pages/shop/ProductDetailPage.tsx":{"*":{"id":"(ssr)/./src/pages/shop/ProductDetailPage.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\index.css":{"id":"(app-pages-browser)/./src/index.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\"],\"weight\":[\"200\",\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\[locale]\\\\layout.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\"],\"weight\":[\"200\",\"300\",\"400\",\"500\",\"700\",\"800\",\"900\"],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"}","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\providers.tsx":{"id":"(app-pages-browser)/./src/app/providers.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\components\\layout\\RootLayout.tsx":{"id":"(app-pages-browser)/./src/components/layout/RootLayout.tsx","name":"*","chunks":["app/[locale]/layout","static/chunks/app/%5Blocale%5D/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\not-found.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/not-found.tsx","name":"*","chunks":["app/[locale]/not-found","static/chunks/app/%5Blocale%5D/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/page.tsx","name":"*","chunks":["app/[locale]/page","static/chunks/app/%5Blocale%5D/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\terms-of-service\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/terms-of-service/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\privacy-policy\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/privacy-policy/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\returns\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/returns/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\shipping\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/shipping/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\faq\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/faq/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\contact\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/contact/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\shop\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/shop/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\production-lines\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/production-lines/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\services\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/services/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\blog\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/blog/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\clearance\\page.tsx":{"id":"(app-pages-browser)/./src/app/[locale]/clearance/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\pages\\shop\\ProductDetailPage.tsx":{"id":"(app-pages-browser)/./src/pages/shop/ProductDetailPage.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\":[],"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\layout":[{"inlined":false,"path":"static/css/app/[locale]/layout.css"}],"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\not-found":[],"C:\\Users\\<USER>\\Desktop\\ecommercepro\\src\\app\\[locale]\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/index.css":{"*":{"id":"(rsc)/./src/index.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.tsx":{"*":{"id":"(rsc)/./src/app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/RootLayout.tsx":{"*":{"id":"(rsc)/./src/components/layout/RootLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/not-found.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/terms-of-service/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/terms-of-service/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/privacy-policy/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/privacy-policy/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/returns/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/returns/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/shipping/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/shipping/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/faq/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/faq/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/contact/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/shop/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/shop/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/production-lines/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/production-lines/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/services/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/services/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/blog/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/blog/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/[locale]/clearance/page.tsx":{"*":{"id":"(rsc)/./src/app/[locale]/clearance/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/pages/shop/ProductDetailPage.tsx":{"*":{"id":"(rsc)/./src/pages/shop/ProductDetailPage.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}