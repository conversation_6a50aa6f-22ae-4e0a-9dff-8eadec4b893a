"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./src/lib/sqlite.ts":
/*!***************************!*\
  !*** ./src/lib/sqlite.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sqlite: () => (/* binding */ sqlite)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(app-pages-browser)/./node_modules/bcryptjs/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * مكتبة SQLite للتعامل مع قاعدة البيانات المحلية\n * تستبدل هذه المكتبة استخدام Supabase في المشروع\n */ \n // Added for password hashing\nconst IS_SERVER = \"object\" === 'undefined';\nconst SALT_ROUNDS = 10; // For bcrypt hashing\nlet db = null;\nif (IS_SERVER) {\n    try {\n        const BetterSqlite3 = __webpack_require__(/*! better-sqlite3 */ \"(app-pages-browser)/./node_modules/better-sqlite3/lib/index.js\");\n        const path = __webpack_require__(/*! path */ \"(app-pages-browser)/./node_modules/next/dist/compiled/path-browserify/index.js\");\n        const fs = __webpack_require__(/*! fs */ \"?da0f\");\n        const dbDir = path.join(process.cwd(), 'server', 'db');\n        const dbPath = path.join(dbDir, 'ecommerce.sqlite');\n        if (!fs.existsSync(dbDir)) {\n            fs.mkdirSync(dbDir, {\n                recursive: true\n            });\n            console.log(\"[DB_SERVER] Created database directory: \".concat(dbDir));\n        }\n        console.log(\"[DB_SERVER] Attempting to connect to SQLite database at: \".concat(dbPath));\n        db = new BetterSqlite3(dbPath, {}); // Verbose logging can be noisy\n        console.log('[DB_SERVER] Successfully connected to SQLite database.');\n        // Ensure tables are created (idempotent)\n        db.exec(\"\\n      CREATE TABLE IF NOT EXISTS users (\\n        id TEXT PRIMARY KEY,\\n        email TEXT UNIQUE NOT NULL,\\n        password_hash TEXT NOT NULL,\\n        first_name TEXT,\\n        last_name TEXT,\\n        role TEXT DEFAULT 'user' NOT NULL,\\n        created_at TEXT NOT NULL,\\n        updated_at TEXT,\\n        avatar_url TEXT,\\n        phone_number TEXT,\\n        addresses TEXT, -- Store as JSON string\\n        email_verified INTEGER DEFAULT 0, -- 0 for false, 1 for true\\n        last_login TEXT\\n      );\\n    \");\n        console.log('[DB_SERVER] Users table checked/created.');\n        db.exec(\"\\n      CREATE TABLE IF NOT EXISTS products (\\n        id TEXT PRIMARY KEY,\\n        name TEXT NOT NULL,\\n        slug TEXT UNIQUE NOT NULL,\\n        description TEXT,\\n        price REAL NOT NULL,\\n        compare_at_price REAL,\\n        images TEXT,\\n        category TEXT,\\n        tags TEXT,\\n        stock INTEGER DEFAULT 0,\\n        featured INTEGER DEFAULT 0,\\n        specifications TEXT,\\n        created_at TEXT NOT NULL,\\n        updated_at TEXT,\\n        rating REAL,\\n        review_count INTEGER,\\n        related_products TEXT\\n      );\\n    \");\n        console.log('[DB_SERVER] Products table checked/created.');\n        db.exec(\"\\n      CREATE TABLE IF NOT EXISTS service_bookings (\\n        id TEXT PRIMARY KEY,\\n        service_name TEXT NOT NULL,\\n        customer_name TEXT NOT NULL,\\n        customer_email TEXT NOT NULL,\\n        customer_phone TEXT NOT NULL,\\n        company_name TEXT,\\n        service_date TEXT NOT NULL,\\n        preferred_time TEXT,\\n        urgency TEXT DEFAULT 'normal',\\n        message TEXT,\\n        status TEXT DEFAULT 'pending',\\n        notes TEXT,\\n        created_at TEXT NOT NULL,\\n        updated_at TEXT\\n      );\\n    \");\n        console.log('[DB_SERVER] Service bookings table checked/created.');\n        // Add other table creations here as needed (services, blog_posts, etc.)\n        console.log('[DB_SERVER] Database schema checked/initialized.');\n    } catch (error) {\n        console.error('[DB_SERVER] Critical error during database initialization:', error);\n        db = null;\n    }\n} else {\n    console.log('[MockDB_Client] Running in client mode, SQLite DB not initialized.');\n}\n// --- Server-side Helper Functions ---\nasync function _server_hashPassword(password) {\n    if (!IS_SERVER) throw new Error('_server_hashPassword can only be called on the server.');\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, SALT_ROUNDS);\n}\nasync function _server_comparePassword(password, hash) {\n    if (!IS_SERVER) throw new Error('_server_comparePassword can only be called on the server.');\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hash);\n}\nfunction _server_getUserByEmailWithPasswordHash(email) {\n    if (!IS_SERVER || !db) return null;\n    try {\n        const stmt = db.prepare('SELECT * FROM users WHERE email = ?');\n        const row = stmt.get(email.toLowerCase());\n        return row ? mapUserFromDbRow(row) : null;\n    } catch (error) {\n        console.error('[DB_SERVER] Error in _server_getUserByEmailWithPasswordHash:', error);\n        return null;\n    }\n}\nfunction _server_getUserById(id) {\n    if (!IS_SERVER || !db) return null;\n    try {\n        const stmt = db.prepare('SELECT * FROM users WHERE id = ?');\n        const row = stmt.get(id);\n        return row ? mapUserFromDbRow(row) : null;\n    } catch (error) {\n        console.error('[DB_SERVER] Error in _server_getUserById:', error);\n        return null;\n    }\n}\nfunction _server_createUser(userData) {\n    if (!IS_SERVER || !db) return null;\n    try {\n        const now = new Date().toISOString();\n        // Prepare addresses as JSON string if provided\n        const addressesJson = userData.addresses && userData.addresses.length > 0 ? JSON.stringify(userData.addresses) : null;\n        // Generate a new ID if not provided\n        const userId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const stmt = db.prepare(\"\\n      INSERT INTO users (\\n        id, email, password_hash, first_name, last_name, role,\\n        created_at, updated_at, avatar_url, phone_number,\\n        addresses, email_verified, last_login\\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\\n    \");\n        stmt.run(userId, userData.email.toLowerCase(), userData.password_hash, userData.firstName || '', userData.lastName || '', userData.role || 'user', now, now, userData.avatarUrl || null, userData.phoneNumber || null, addressesJson, userData.emailVerified ? 1 : 0, userData.lastLogin || null);\n        return _server_getUserById(userId);\n    } catch (error) {\n        console.error('[DB_SERVER] Error in _server_createUser:', error);\n        return null;\n    }\n}\nfunction _server_getProducts() {\n    if (!IS_SERVER || !db) return [];\n    try {\n        const stmt = db.prepare('SELECT * FROM products ORDER BY created_at DESC');\n        const rows = stmt.all();\n        return rows.map(mapProductFromDbRow);\n    } catch (error) {\n        console.error('[DB_SERVER] Error in _server_getProducts:', error);\n        return [];\n    }\n}\nfunction _server_createProduct(productData) {\n    if (!IS_SERVER || !db) return null;\n    const newId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    const now = new Date().toISOString();\n    try {\n        const stmt = db.prepare(\"\\n      INSERT INTO products (id, name, slug, description, price, compare_at_price, images, category, tags, stock, featured, specifications, created_at)\\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\\n    \");\n        stmt.run(newId, productData.name, productData.slug, productData.description, productData.price, productData.compareAtPrice, JSON.stringify(productData.images || []), productData.category, JSON.stringify(productData.tags || []), productData.stock, productData.featured ? 1 : 0, JSON.stringify(productData.specifications || {}), now);\n        // To get the full product, we'd ideally query it back, but for now let's construct it\n        return {\n            ...productData,\n            id: newId,\n            createdAt: now,\n            reviews: [],\n            rating: 0,\n            reviewCount: 0\n        }; // Cast as Product, assuming defaults for reviews/rating\n    } catch (error) {\n        console.error('[DB_SERVER] Error in _server_createProduct:', error);\n        return null;\n    }\n}\n// --- Helper function to safely parse JSON (used for localStorage) ---\nfunction safeJsonParse(jsonString, defaultValue) {\n    if (jsonString == null) return defaultValue;\n    try {\n        return JSON.parse(jsonString);\n    } catch (e) {\n        // console.error('Failed to parse JSON string:', e, '\\nString was:', jsonString);\n        return defaultValue;\n    }\n}\n// --- localStorage keys ---\nconst USER_STORAGE_KEY = 'sqlite_users';\nconst PRODUCT_STORAGE_KEY = 'sqlite_products';\n// ... other keys\n// --- Default data for client-side localStorage initialization ---\nconst DEFAULT_LOCAL_USERS = [\n    {\n        email: '<EMAIL>',\n        firstName: 'Admin',\n        lastName: 'Local',\n        role: 'admin'\n    },\n    {\n        email: '<EMAIL>',\n        firstName: 'Test',\n        lastName: 'Local',\n        role: 'user'\n    }\n];\n// ... DEFAULT_LOCAL_PRODUCTS (ensure it matches Product type, omitting server-generated fields)\nconst DEFAULT_LOCAL_PRODUCTS = [\n    {\n        name: 'Default Local Product 1',\n        slug: 'default-local-product-1',\n        description: 'This is a default product for localStorage.',\n        price: 19.99,\n        compareAtPrice: 29.99,\n        images: [],\n        category: 'Local Category',\n        tags: [\n            'default',\n            'localstorage'\n        ],\n        stock: 100,\n        featured: true,\n        specifications: {\n            material: 'local_plastic'\n        }\n    }\n];\n// --- Helper function to map database row to User type (includes password_hash for internal use) ---\nfunction mapUserFromDbRow(row) {\n    if (!row) return row;\n    // Parse addresses from JSON string if present\n    let addresses = [];\n    try {\n        if (row.addresses) {\n            addresses = JSON.parse(row.addresses);\n        }\n    } catch (e) {\n        console.error('[DB] Error parsing user addresses:', e);\n    }\n    // Map all fields from the database row to the User type\n    const user = {\n        id: row.id,\n        email: row.email,\n        firstName: row.first_name || '',\n        lastName: row.last_name || '',\n        role: row.role || 'user',\n        createdAt: row.created_at,\n        updatedAt: row.updated_at || null,\n        avatarUrl: row.avatar_url || null,\n        phoneNumber: row.phone_number || null,\n        addresses: addresses,\n        emailVerified: Boolean(row.email_verified),\n        lastLogin: row.last_login || null\n    };\n    // Include password_hash for internal authentication\n    if (row.password_hash) {\n        user.password_hash = row.password_hash;\n    }\n    return user;\n}\n// --- Helper function to map database row to Product type ---\nfunction mapProductFromDbRow(row) {\n    if (!row) return null;\n    return {\n        id: row.id,\n        name: row.name,\n        slug: row.slug,\n        description: row.description,\n        price: row.price ? parseFloat(row.price) : 0,\n        compareAtPrice: row.compare_at_price ? parseFloat(row.compare_at_price) : undefined,\n        images: row.images ? safeJsonParse(row.images, []) : [],\n        category: row.category,\n        tags: row.tags ? safeJsonParse(row.tags, []) : [],\n        stock: row.stock ? parseInt(row.stock, 10) : 0,\n        featured: Boolean(row.featured),\n        specifications: row.specifications ? safeJsonParse(row.specifications, {}) : {},\n        createdAt: row.created_at,\n        updatedAt: row.updated_at,\n        reviews: [],\n        rating: row.rating ? parseFloat(row.rating) : undefined,\n        reviewCount: row.review_count ? parseInt(row.review_count, 10) : undefined,\n        relatedProducts: row.related_products ? safeJsonParse(row.related_products, []) : []\n    };\n}\nclass MockSQLiteDatabase {\n    loadFromLocalStorage(key, defaultValue) {\n        if (IS_SERVER) return defaultValue; // Should not happen with current constructor logic\n        try {\n            const data = localStorage.getItem(key);\n            const parsedData = data ? safeJsonParse(data, defaultValue) : defaultValue;\n            // تأكد من أن البيانات المحملة هي مصفوفة\n            if (!Array.isArray(parsedData)) {\n                console.warn(\"[MockDB_Client] Data loaded from \".concat(key, \" is not an array, using default value\"));\n                return defaultValue;\n            }\n            return parsedData;\n        } catch (error) {\n            console.error(\"[MockDB_Client] Error loading data from localStorage key \".concat(key, \":\"), error);\n            return defaultValue;\n        }\n    }\n    saveToLocalStorage(key, data) {\n        if (IS_SERVER) return;\n        localStorage.setItem(key, JSON.stringify(data));\n    }\n    _initializeDefaultLocalData() {\n        if (IS_SERVER) return;\n        if (this.users.length === 0) {\n            this.users = DEFAULT_LOCAL_USERS.map((u)=>({\n                    ...u,\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n                    createdAt: new Date().toISOString()\n                }));\n            this.saveToLocalStorage(this.userKey, this.users);\n        }\n        if (this.products.length === 0) {\n            this.products = DEFAULT_LOCAL_PRODUCTS.map((p)=>({\n                    ...p,\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n                    createdAt: new Date().toISOString(),\n                    reviews: [],\n                    rating: 0,\n                    reviewCount: 0,\n                    relatedProducts: []\n                }));\n            this.saveToLocalStorage(this.productKey, this.products);\n        }\n    }\n    // --- User Methods ---\n    async getUsers() {\n        if (IS_SERVER) {\n            if (!this.db_conn) return [];\n            try {\n                const stmt = this.db_conn.prepare('SELECT * FROM users');\n                const rows = stmt.all();\n                // Exclude password_hash from results sent to client/general use\n                return rows.map(mapUserFromDbRow).map((userWithHash)=>{\n                    const { password_hash, ...userWithoutHash } = userWithHash;\n                    return userWithoutHash; // Ensure the final object is also User\n                });\n            } catch (error) {\n                console.error('[DB_SERVER] Error in getUsers:', error);\n                return [];\n            }\n        }\n        // تأكد من أن this.users هو مصفوفة\n        if (!Array.isArray(this.users)) {\n            console.warn('[MockDB_Client] this.users is not an array, initializing empty array');\n            this.users = [];\n            this.saveToLocalStorage(this.userKey, this.users);\n        }\n        return [\n            ...this.users\n        ];\n    }\n    async getUserByEmail(email) {\n        if (IS_SERVER) {\n            const userWithHash = _server_getUserByEmailWithPasswordHash(email.toLowerCase());\n            if (userWithHash) {\n                const { password_hash, ...userWithoutHash } = userWithHash;\n                return userWithoutHash;\n            }\n            return null;\n        }\n        // تأكد من أن this.users هو مصفوفة\n        if (!Array.isArray(this.users)) {\n            console.warn('[MockDB_Client] this.users is not an array in getUserByEmail, initializing empty array');\n            this.users = [];\n            this.saveToLocalStorage(this.userKey, this.users);\n            return null;\n        }\n        const user = this.users.find((u)=>u.email.toLowerCase() === email.toLowerCase());\n        return user ? {\n            ...user\n        } : null; // Return a copy\n    }\n    async getUserById(id) {\n        if (IS_SERVER) {\n            const userWithHash = _server_getUserById(id);\n            if (userWithHash) {\n                const { password_hash, ...userWithoutHash } = userWithHash;\n                return userWithoutHash;\n            }\n            return null;\n        }\n        // تأكد من أن this.users هو مصفوفة\n        if (!Array.isArray(this.users)) {\n            console.warn('[MockDB_Client] this.users is not an array in getUserById, initializing empty array');\n            this.users = [];\n            this.saveToLocalStorage(this.userKey, this.users);\n            return null;\n        }\n        const user = this.users.find((u)=>u.id === id);\n        return user ? {\n            ...user\n        } : null;\n    }\n    async createUser(userData) {\n        if (IS_SERVER) {\n            if (!this.db_conn) {\n                console.error('[DB_SERVER] Database connection not available');\n                return null;\n            }\n            if (!userData.email || !userData.password) {\n                console.error('[DB_SERVER] Email and password required for user creation');\n                return null;\n            }\n            try {\n                // Check if user already exists\n                const existingUser = _server_getUserByEmailWithPasswordHash(userData.email.toLowerCase());\n                if (existingUser) {\n                    console.error(\"[DB_SERVER] User with email \".concat(userData.email, \" already exists\"));\n                    return null;\n                }\n                // Create a new user with basic required fields\n                const now = new Date().toISOString();\n                const newUser = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n                    email: userData.email.toLowerCase(),\n                    firstName: userData.firstName || '',\n                    lastName: userData.lastName || '',\n                    role: userData.role || 'user',\n                    createdAt: now,\n                    updatedAt: now,\n                    avatarUrl: userData.avatarUrl || undefined,\n                    phoneNumber: userData.phoneNumber || undefined,\n                    addresses: userData.addresses || [],\n                    emailVerified: userData.emailVerified || false,\n                    lastLogin: undefined\n                };\n                // Hash the password\n                const passwordHash = await _server_hashPassword(userData.password);\n                // Create user in database including password hash\n                // We need to remove fields that aren't in the expected type\n                const { id, createdAt, updatedAt, ...userDataForCreate } = newUser;\n                const user = _server_createUser({\n                    ...userDataForCreate,\n                    password_hash: passwordHash\n                });\n                if (!user) {\n                    console.error('[DB_SERVER] Failed to create user in database');\n                    return null;\n                }\n                // Don't return password hash to client\n                const { password_hash, ...userWithoutHash } = user;\n                return userWithoutHash;\n            } catch (error) {\n                console.error('[DB_SERVER] Error creating user:', error);\n                return null;\n            }\n        }\n        // Client-side mock\n        try {\n            // Check if email already exists in mock data\n            const existingUser = this.users.find((u)=>u.email.toLowerCase() === userData.email.toLowerCase());\n            if (existingUser) {\n                console.error(\"[MockDB_Client] User with email \".concat(userData.email, \" already exists\"));\n                return null;\n            }\n            const now = new Date().toISOString();\n            const newUser = {\n                ...userData,\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n                email: userData.email.toLowerCase(),\n                role: userData.role || 'user',\n                createdAt: now,\n                updatedAt: now,\n                avatarUrl: userData.avatarUrl || undefined,\n                phoneNumber: userData.phoneNumber || undefined,\n                addresses: userData.addresses || [],\n                emailVerified: userData.emailVerified || false,\n                lastLogin: undefined\n            };\n            this.users.push(newUser);\n            this.saveToLocalStorage(this.userKey, this.users);\n            return {\n                ...newUser\n            };\n        } catch (error) {\n            console.error('[MockDB_Client] Error creating user:', error);\n            return null;\n        }\n    }\n    async authenticateUser(email, password) {\n        if (IS_SERVER) {\n            if (!this.db_conn) {\n                console.error('[DB_SERVER] Database connection not available');\n                return null;\n            }\n            try {\n                // First retrieve the user with password hash by email\n                const user = _server_getUserByEmailWithPasswordHash(email.toLowerCase());\n                if (!user || !user.password_hash) {\n                    console.log('[DB_SERVER] User not found or missing password hash:', email);\n                    return null;\n                }\n                // Compare provided password with stored hash\n                const passwordMatch = await _server_comparePassword(password, user.password_hash);\n                if (!passwordMatch) {\n                    console.log('[DB_SERVER] Password mismatch for user:', email);\n                    return null;\n                }\n                // Update last login time\n                const now = new Date().toISOString();\n                const updateStmt = this.db_conn.prepare('UPDATE users SET last_login = ?, email_verified = 1 WHERE id = ?');\n                updateStmt.run(now, user.id);\n                // Fetch the updated user record\n                const updatedUser = _server_getUserById(user.id);\n                if (!updatedUser) {\n                    console.error('[DB_SERVER] Failed to fetch updated user after login');\n                    return null;\n                }\n                // Don't return password hash to the client\n                const { password_hash, ...userWithoutHash } = updatedUser;\n                return userWithoutHash;\n            } catch (error) {\n                console.error('[DB_SERVER] Error during authentication:', error);\n                return null;\n            }\n        }\n        // Client-side mock authentication\n        const user = this.users.find((u)=>u.email.toLowerCase() === email.toLowerCase());\n        if (!user) {\n            console.log('[MockDB_Client] User not found in mock database:', email);\n            return null;\n        }\n        // In client mode, we simulate a successful login by updating last login\n        const now = new Date().toISOString();\n        const updatedUser = {\n            ...user,\n            lastLogin: now,\n            emailVerified: true\n        };\n        // Update the user in local storage\n        const userIndex = this.users.findIndex((u)=>u.id === user.id);\n        if (userIndex !== -1) {\n            this.users[userIndex] = updatedUser;\n            this.saveToLocalStorage(this.userKey, this.users);\n        }\n        return updatedUser;\n    }\n    async updateUser(id, userData) {\n        if (IS_SERVER) {\n            if (!this.db_conn) return null;\n            const existingUser = _server_getUserById(id);\n            if (!existingUser) return null;\n            const fieldsToUpdate = {\n                ...userData,\n                updated_at: new Date().toISOString()\n            };\n            const setClauses = Object.keys(fieldsToUpdate).map((key)=>\"\".concat(key.replace(/[A-Z]/g, (letter)=>\"_\".concat(letter.toLowerCase())), \" = ?\")).join(', ');\n            const values = Object.values(fieldsToUpdate);\n            if (values.length === 0) return existingUser; // No fields to update\n            try {\n                const stmt = this.db_conn.prepare(\"UPDATE users SET \".concat(setClauses, \" WHERE id = ?\"));\n                stmt.run(...values, id);\n                const updatedUser = _server_getUserById(id);\n                if (updatedUser) {\n                    const { password_hash, ...userWithoutHash } = updatedUser;\n                    return userWithoutHash;\n                }\n                return null;\n            } catch (error) {\n                console.error('[DB_SERVER] Error in updateUser:', error);\n                return null;\n            }\n        }\n        // Client-side mock\n        const userIndex = this.users.findIndex((u)=>u.id === id);\n        if (userIndex === -1) return null;\n        this.users[userIndex] = {\n            ...this.users[userIndex],\n            ...userData,\n            updatedAt: new Date().toISOString()\n        };\n        this.saveToLocalStorage(this.userKey, this.users);\n        return {\n            ...this.users[userIndex]\n        };\n    }\n    // --- Product Methods ---\n    async getProducts() {\n        if (IS_SERVER) {\n            return _server_getProducts();\n        }\n        return [\n            ...this.products\n        ];\n    }\n    async createProduct(productData) {\n        if (IS_SERVER) {\n            return _server_createProduct(productData);\n        }\n        // Client-side mock\n        const newId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = new Date().toISOString();\n        const newProduct = {\n            ...productData,\n            id: newId,\n            createdAt: now,\n            reviews: [],\n            rating: 0,\n            reviewCount: 0\n        };\n        this.products.push(newProduct);\n        this.saveToLocalStorage(this.productKey, this.products);\n        return {\n            ...newProduct\n        };\n    }\n    // ... (Other methods like getProductById, updateProduct, deleteProduct should follow similar pattern)\n    async getProductById(id) {\n        if (IS_SERVER) {\n            if (!this.db_conn) return null;\n            try {\n                const stmt = this.db_conn.prepare('SELECT * FROM products WHERE id = ?');\n                const row = stmt.get(id);\n                return row ? mapProductFromDbRow(row) : null;\n            } catch (error) {\n                console.error('[DB_SERVER] Error in getProductById:', error);\n                return null;\n            }\n        }\n        const product = this.products.find((p)=>p.id === id);\n        return product ? {\n            ...product\n        } : null;\n    }\n    // Example for reset (useful for testing or initial setup)\n    async resetLocalUsers() {\n        if (IS_SERVER) {\n            console.warn(\"[DB_SERVER] resetLocalUsers called on server. This will clear the users table.\");\n            if (!this.db_conn) return;\n            try {\n                this.db_conn.exec('DELETE FROM users');\n                console.log(\"[DB_SERVER] All users deleted from database.\");\n            } catch (error) {\n                console.error(\"[DB_SERVER] Error deleting users from database:\", error);\n            }\n            return;\n        }\n        this.users = [];\n        this.saveToLocalStorage(this.userKey, this.users);\n        this._initializeDefaultLocalData(); // Re-initialize with defaults if needed\n        console.log('[MockDB_Client] Local users reset and defaults re-initialized.');\n    }\n    // Service Booking Methods\n    getServiceBookings() {\n        if (IS_SERVER) {\n            if (!this.db_conn) return [];\n            try {\n                const stmt = this.db_conn.prepare('SELECT * FROM service_bookings ORDER BY created_at DESC');\n                return stmt.all();\n            } catch (error) {\n                console.error('[DB_SERVER] Error getting service bookings:', error);\n                return [];\n            }\n        }\n        // Client-side localStorage fallback\n        return this.loadFromLocalStorage('service_bookings', []);\n    }\n    saveServiceBookings(bookings) {\n        if (IS_SERVER) {\n            if (!this.db_conn) return;\n            try {\n                // Clear existing bookings\n                this.db_conn.prepare('DELETE FROM service_bookings').run();\n                // Insert all bookings\n                const stmt = this.db_conn.prepare(\"\\n          INSERT INTO service_bookings (\\n            id, service_name, customer_name, customer_email, customer_phone,\\n            company_name, service_date, preferred_time, urgency, message,\\n            status, notes, created_at, updated_at\\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\\n        \");\n                for (const booking of bookings){\n                    stmt.run(booking.id, booking.serviceName, booking.customerName, booking.customerEmail, booking.customerPhone, booking.companyName, booking.serviceDate, booking.preferredTime, booking.urgency, booking.message, booking.status, booking.notes, booking.createdAt, booking.updatedAt);\n                }\n            } catch (error) {\n                console.error('[DB_SERVER] Error saving service bookings:', error);\n            }\n        } else {\n            // Client-side localStorage\n            this.saveToLocalStorage('service_bookings', bookings);\n        }\n    }\n    saveServiceBooking(booking) {\n        if (IS_SERVER) {\n            if (!this.db_conn) return;\n            try {\n                const stmt = this.db_conn.prepare(\"\\n          INSERT INTO service_bookings (\\n            id, service_name, customer_name, customer_email, customer_phone,\\n            company_name, service_date, preferred_time, urgency, message,\\n            status, notes, created_at, updated_at\\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\\n        \");\n                stmt.run(booking.id, booking.serviceName, booking.customerName, booking.customerEmail, booking.customerPhone, booking.companyName, booking.serviceDate, booking.preferredTime, booking.urgency, booking.message, booking.status, booking.notes, booking.createdAt, booking.updatedAt);\n            } catch (error) {\n                console.error('[DB_SERVER] Error saving service booking:', error);\n            }\n        } else {\n            // Client-side localStorage\n            const bookings = this.getServiceBookings();\n            bookings.push(booking);\n            this.saveToLocalStorage('service_bookings', bookings);\n        }\n    }\n    updateServiceBookingStatus(bookingId, status, notes) {\n        if (IS_SERVER) {\n            if (!this.db_conn) return;\n            try {\n                const stmt = this.db_conn.prepare(\"\\n          UPDATE service_bookings\\n          SET status = ?, notes = ?, updated_at = ?\\n          WHERE id = ?\\n        \");\n                stmt.run(status, notes || '', new Date().toISOString(), bookingId);\n            } catch (error) {\n                console.error('[DB_SERVER] Error updating service booking status:', error);\n            }\n        } else {\n            // Client-side localStorage\n            const bookings = this.getServiceBookings();\n            const bookingIndex = bookings.findIndex((b)=>b.id === bookingId);\n            if (bookingIndex !== -1) {\n                bookings[bookingIndex] = {\n                    ...bookings[bookingIndex],\n                    status,\n                    notes: notes || bookings[bookingIndex].notes,\n                    updatedAt: new Date().toISOString()\n                };\n                this.saveToLocalStorage('service_bookings', bookings);\n            }\n        }\n    }\n    // ... other keys\n    constructor(server_db_connection){\n        this.users = [];\n        this.products = [];\n        // ... other local stores\n        this.userKey = USER_STORAGE_KEY;\n        this.productKey = PRODUCT_STORAGE_KEY;\n        this.db_conn = server_db_connection; // This is the actual 'db' object from server scope\n        if (!IS_SERVER) {\n            this.users = this.loadFromLocalStorage(this.userKey, []);\n            this.products = this.loadFromLocalStorage(this.productKey, []);\n            // ... load other stores\n            this._initializeDefaultLocalData();\n        }\n    }\n}\n// Export a single instance of the database client\n// The 'db' variable from the server scope is passed here.\n// On the client, 'db' will be null, and MockSQLiteDatabase will use localStorage.\nconst sqlite = new MockSQLiteDatabase(db);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/sqlite.ts\n"));

/***/ })

});