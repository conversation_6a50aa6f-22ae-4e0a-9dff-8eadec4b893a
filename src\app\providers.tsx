'use client';

import { ReactNode, useEffect, useState } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from '../lib/queryClient';
import { useLanguageStore } from '../stores/languageStore';
import { Locale } from '../lib/i18n';
import { ErrorBoundary } from '../components/error/ErrorBoundary';
import { initializeDefaultUsers } from '../services/AuthService';

// Dynamically import providers to prevent SSR issues
import dynamic from 'next/dynamic';

const ABTestingProvider = dynamic(
  () => import('../components/marketing/ABTestingProvider').then(mod => ({ default: mod.ABTestingProvider })),
  {
    ssr: false,
    loading: () => null
  }
);

const ThemeProvider = dynamic(
  () => import('next-themes').then(mod => ({ default: mod.ThemeProvider })),
  {
    ssr: false,
    loading: () => null
  }
);

export function Providers({
  children,
  locale
}: {
  children: ReactNode;
  locale?: string;
}) {
  const { setLanguage } = useLanguageStore();
  const [isClient, setIsClient] = useState(false);

  // Effect for client-side initialization
  useEffect(() => {
    setIsClient(true);

    // Initialize language based on locale prop
    if (locale && (locale === 'ar' || locale === 'en')) {
      setLanguage(locale as Locale);
    }

    // Initialize default users for development
    try {
      initializeDefaultUsers();
    } catch (error) {
      console.warn('Failed to initialize default users:', error);
    }
  }, [locale, setLanguage]);

  // Render minimal version during SSR
  if (!isClient) {
    return (
      <ErrorBoundary>
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      </ErrorBoundary>
    );
  }

  // Full provider tree for client-side
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          storageKey="ui-theme"
        >
          <ABTestingProvider>
            {children}
          </ABTestingProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}
