'use client';

import { ReactNode, useEffect } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { queryClient } from '../lib/queryClient';
import { useLanguageStore } from '../stores/languageStore';
import { Locale } from '../lib/i18n';
import { AuthModalProvider } from '../components/auth/AuthModalProvider';
import { ABTestingProvider } from '../components/marketing/ABTestingProvider';
import { initializeDefaultUsers } from '../services/AuthService';
import dynamic from 'next/dynamic';

// Dynamically import ThemeProvider to avoid SSR issues
const ThemeProvider = dynamic(
  () => import('next-themes').then((mod) => mod.ThemeProvider),
  { ssr: false }
);

export function Providers({
  children,
  locale
}: {
  children: ReactNode;
  locale?: string;
}) {
  const { setLanguage } = useLanguageStore();

  // Effect for initializing language based on locale prop
  useEffect(() => {
    if (locale && (locale === 'ar' || locale === 'en')) {
      setLanguage(locale as Locale);
    }
  }, [locale, setLanguage]);

  // Effect for one-time client-side initializations
  useEffect(() => {
    // إنشاء مستخدمين افتراضيين للتطوير المحلي
    if (typeof window !== 'undefined') {
      initializeDefaultUsers();
    }
  }, []); // Empty dependency array ensures this runs only once on mount

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        storageKey="ui-theme"
      >
        <ABTestingProvider>
          <AuthModalProvider>
            {children}
          </AuthModalProvider>
        </ABTestingProvider>
      </ThemeProvider>
      {/* {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />} */}
    </QueryClientProvider>
  );
}
