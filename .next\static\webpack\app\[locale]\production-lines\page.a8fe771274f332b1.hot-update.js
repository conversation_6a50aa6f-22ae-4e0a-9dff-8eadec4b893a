"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/production-lines/page",{

/***/ "(app-pages-browser)/./src/pages/production/ProductionLinesPage.tsx":
/*!******************************************************!*\
  !*** ./src/pages/production/ProductionLinesPage.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductionLinesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/factory.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,Award,BarChart3,Calendar,CheckCircle,Clock,DollarSign,Download,Eye,Factory,Gauge,Grid3X3,List,MapPin,PenTool,Play,Search,Settings,Shield,SlidersHorizontal,Star,TrendingUp,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_EnhancedImage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/ui/EnhancedImage */ \"(app-pages-browser)/./src/components/ui/EnhancedImage.tsx\");\n/* harmony import */ var _data_productionLines__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../data/productionLines */ \"(app-pages-browser)/./src/data/productionLines.ts\");\n/* harmony import */ var _stores_themeStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../stores/themeStore */ \"(app-pages-browser)/./src/stores/themeStore.ts\");\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n/* harmony import */ var _translations__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../translations */ \"(app-pages-browser)/./src/translations/index.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_animations__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../components/ui/animations */ \"(app-pages-browser)/./src/components/ui/animations/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst categoriesData = {\n    en: [\n        {\n            id: 'all',\n            name: 'All Lines',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Manufacturing',\n            name: 'Manufacturing',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Food & Beverage',\n            name: 'Food & Beverage',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Packaging',\n            name: 'Packaging',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Pharmaceutical',\n            name: 'Pharmaceutical',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            count: 0\n        }\n    ],\n    ar: [\n        {\n            id: 'all',\n            name: 'جميع الخطوط',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Manufacturing',\n            name: 'التصنيع',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Food & Beverage',\n            name: 'الأغذية والمشروبات',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Packaging',\n            name: 'التعبئة والتغليف',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            count: 0\n        },\n        {\n            id: 'Pharmaceutical',\n            name: 'الصناعات الدوائية',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            count: 0\n        }\n    ]\n};\nconst statusData = {\n    en: [\n        {\n            id: 'all',\n            name: 'All Status',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            color: 'text-slate-500'\n        },\n        {\n            id: 'active',\n            name: 'Active',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            color: 'text-green-500'\n        },\n        {\n            id: 'maintenance',\n            name: 'Maintenance',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            color: 'text-yellow-500'\n        },\n        {\n            id: 'inactive',\n            name: 'Inactive',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            color: 'text-red-500'\n        }\n    ],\n    ar: [\n        {\n            id: 'all',\n            name: 'جميع الحالات',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            color: 'text-slate-500'\n        },\n        {\n            id: 'active',\n            name: 'نشط',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            color: 'text-green-500'\n        },\n        {\n            id: 'maintenance',\n            name: 'صيانة',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            color: 'text-yellow-500'\n        },\n        {\n            id: 'inactive',\n            name: 'غير نشط',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            color: 'text-red-500'\n        }\n    ]\n};\nconst sortOptions = {\n    en: [\n        {\n            id: 'name',\n            name: 'Name A-Z',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"]\n        },\n        {\n            id: 'efficiency',\n            name: 'Efficiency',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"]\n        },\n        {\n            id: 'capacity',\n            name: 'Capacity',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            id: 'priority',\n            name: 'Priority',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"]\n        },\n        {\n            id: 'createdAt',\n            name: 'Date Added',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"]\n        }\n    ],\n    ar: [\n        {\n            id: 'name',\n            name: 'الاسم أ-ي',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"]\n        },\n        {\n            id: 'efficiency',\n            name: 'الكفاءة',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"]\n        },\n        {\n            id: 'capacity',\n            name: 'السعة',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            id: 'priority',\n            name: 'الأولوية',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"]\n        },\n        {\n            id: 'createdAt',\n            name: 'تاريخ الإضافة',\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"]\n        }\n    ]\n};\nfunction ProductionLinesPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { isDarkMode } = (0,_stores_themeStore__WEBPACK_IMPORTED_MODULE_9__.useThemeStore)();\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_10__.useLanguageStore)();\n    const { t, locale } = (0,_translations__WEBPACK_IMPORTED_MODULE_11__.useTranslation)();\n    // استخدام اللغة من المسار أو من المتجر\n    const currentLanguage = locale || language;\n    // State management for enhanced filtering and UI\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: 'all',\n        status: 'all',\n        efficiency: {\n            min: 0,\n            max: 100\n        },\n        capacity: {\n            min: 0,\n            max: 100000\n        },\n        manufacturer: 'all',\n        searchQuery: '',\n        tags: [],\n        featured: false\n    });\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('priority');\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('asc');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // استخدام البيانات المناسبة حسب اللغة\n    const categories = categoriesData[currentLanguage];\n    const statusOptions = statusData[currentLanguage];\n    const sortingOptions = sortOptions[currentLanguage];\n    // Calculate category counts\n    const categoriesWithCounts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductionLinesPage.useMemo[categoriesWithCounts]\": ()=>{\n            return categories.map({\n                \"ProductionLinesPage.useMemo[categoriesWithCounts]\": (category)=>({\n                        ...category,\n                        count: category.id === 'all' ? _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.length : _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.filter({\n                            \"ProductionLinesPage.useMemo[categoriesWithCounts]\": (line)=>line.category === category.id\n                        }[\"ProductionLinesPage.useMemo[categoriesWithCounts]\"]).length\n                    })\n            }[\"ProductionLinesPage.useMemo[categoriesWithCounts]\"]);\n        }\n    }[\"ProductionLinesPage.useMemo[categoriesWithCounts]\"], [\n        categories\n    ]);\n    // Advanced filtering logic\n    const filteredLines = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductionLinesPage.useMemo[filteredLines]\": ()=>{\n            let filtered = _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines;\n            // Category filter\n            if (filters.category !== 'all') {\n                filtered = filtered.filter({\n                    \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>line.category === filters.category\n                }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            }\n            // Status filter\n            if (filters.status !== 'all') {\n                filtered = filtered.filter({\n                    \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>line.status === filters.status\n                }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            }\n            // Search filter\n            if (filters.searchQuery) {\n                const query = filters.searchQuery.toLowerCase();\n                filtered = filtered.filter({\n                    \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>{\n                        var _line_manufacturer, _line_tags;\n                        return line.name.toLowerCase().includes(query) || line.description.toLowerCase().includes(query) || ((_line_manufacturer = line.manufacturer) === null || _line_manufacturer === void 0 ? void 0 : _line_manufacturer.toLowerCase().includes(query)) || ((_line_tags = line.tags) === null || _line_tags === void 0 ? void 0 : _line_tags.some({\n                            \"ProductionLinesPage.useMemo[filteredLines]\": (tag)=>tag.toLowerCase().includes(query)\n                        }[\"ProductionLinesPage.useMemo[filteredLines]\"]));\n                    }\n                }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            }\n            // Efficiency filter\n            filtered = filtered.filter({\n                \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>line.efficiency >= filters.efficiency.min && line.efficiency <= filters.efficiency.max\n            }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            // Featured filter\n            if (filters.featured) {\n                filtered = filtered.filter({\n                    \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>line.featured\n                }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            }\n            // Tags filter\n            if (filters.tags.length > 0) {\n                filtered = filtered.filter({\n                    \"ProductionLinesPage.useMemo[filteredLines]\": (line)=>filters.tags.some({\n                            \"ProductionLinesPage.useMemo[filteredLines]\": (tag)=>{\n                                var _line_tags;\n                                return (_line_tags = line.tags) === null || _line_tags === void 0 ? void 0 : _line_tags.includes(tag);\n                            }\n                        }[\"ProductionLinesPage.useMemo[filteredLines]\"])\n                }[\"ProductionLinesPage.useMemo[filteredLines]\"]);\n            }\n            return filtered;\n        }\n    }[\"ProductionLinesPage.useMemo[filteredLines]\"], [\n        filters\n    ]);\n    // Sorting logic\n    const sortedLines = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductionLinesPage.useMemo[sortedLines]\": ()=>{\n            const sorted = [\n                ...filteredLines\n            ].sort({\n                \"ProductionLinesPage.useMemo[sortedLines].sorted\": (a, b)=>{\n                    let aValue = a[sortBy];\n                    let bValue = b[sortBy];\n                    // Handle special cases\n                    if (sortBy === 'capacity') {\n                        aValue = parseInt(a.capacity.replace(/[^\\d]/g, ''));\n                        bValue = parseInt(b.capacity.replace(/[^\\d]/g, ''));\n                    } else if (sortBy === 'createdAt') {\n                        aValue = new Date(a.createdAt).getTime();\n                        bValue = new Date(b.createdAt).getTime();\n                    }\n                    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;\n                    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;\n                    return 0;\n                }\n            }[\"ProductionLinesPage.useMemo[sortedLines].sorted\"]);\n            return sorted;\n        }\n    }[\"ProductionLinesPage.useMemo[sortedLines]\"], [\n        filteredLines,\n        sortBy,\n        sortDirection\n    ]);\n    // Statistics calculation\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductionLinesPage.useMemo[stats]\": ()=>{\n            const activeLines = _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.filter({\n                \"ProductionLinesPage.useMemo[stats]\": (line)=>line.status === 'active'\n            }[\"ProductionLinesPage.useMemo[stats]\"]).length;\n            const maintenanceLines = _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.filter({\n                \"ProductionLinesPage.useMemo[stats]\": (line)=>line.status === 'maintenance'\n            }[\"ProductionLinesPage.useMemo[stats]\"]).length;\n            const averageEfficiency = _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.reduce({\n                \"ProductionLinesPage.useMemo[stats]\": (sum, line)=>sum + line.efficiency\n            }[\"ProductionLinesPage.useMemo[stats]\"], 0) / _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.length;\n            return {\n                totalLines: _data_productionLines__WEBPACK_IMPORTED_MODULE_8__.productionLines.length,\n                activeLines,\n                maintenanceLines,\n                averageEfficiency: Math.round(averageEfficiency * 10) / 10,\n                filteredCount: sortedLines.length\n            };\n        }\n    }[\"ProductionLinesPage.useMemo[stats]\"], [\n        sortedLines\n    ]);\n    // Handle filter changes\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    // Handle sort changes\n    const handleSortChange = (field)=>{\n        if (field === sortBy) {\n            setSortDirection((prev)=>prev === 'asc' ? 'desc' : 'asc');\n        } else {\n            setSortBy(field);\n            setSortDirection('asc');\n        }\n    };\n    // Reset filters\n    const resetFilters = ()=>{\n        setFilters({\n            category: 'all',\n            status: 'all',\n            efficiency: {\n                min: 0,\n                max: 100\n            },\n            capacity: {\n                min: 0,\n                max: 100000\n            },\n            manufacturer: 'all',\n            searchQuery: '',\n            tags: [],\n            featured: false\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"relative py-16 overflow-hidden transition-colors duration-500\", isDarkMode ? \"bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900\" : \"bg-gradient-to-br from-slate-50 via-white to-slate-100\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"absolute inset-0 opacity-5 transition-opacity duration-500\", isDarkMode ? \"opacity-10\" : \"opacity-5\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:50px_50px]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-4xl md:text-5xl font-bold leading-tight tracking-tight mb-6\", isDarkMode ? \"text-white\" : \"text-slate-900\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block\",\n                                                children: currentLanguage === 'ar' ? 'خطوط الإنتاج' : 'Production'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"block bg-gradient-to-r bg-clip-text text-transparent\", isDarkMode ? \"from-primary-400 to-blue-400\" : \"from-primary-600 to-blue-600\"),\n                                                children: currentLanguage === 'ar' ? 'الصناعية' : 'Lines'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-lg md:text-xl leading-relaxed max-w-3xl mx-auto mb-8\", isDarkMode ? \"text-slate-300\" : \"text-slate-600\"),\n                                        children: currentLanguage === 'ar' ? 'تقنيات إنتاج متقدمة مصممة للكفاءة والموثوقية مع أنظمة ذكية للتحكم والمراقبة.' : 'Advanced production technologies engineered for efficiency and reliability with intelligent control and monitoring systems.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"primary\",\n                                                    className: \"px-8 py-3 text-lg font-semibold group\",\n                                                    onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/contact\")),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5 group-hover:scale-110 transition-transform\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'استشارة متخصصة' : 'Expert Consultation'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"lg\",\n                                                    variant: \"outline\",\n                                                    className: \"px-8 py-3 text-lg font-semibold group\",\n                                                    onClick: ()=>{\n                                                        var _document_getElementById;\n                                                        return (_document_getElementById = document.getElementById('production-lines')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                            behavior: 'smooth'\n                                                        });\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'mr-2 rotate-180' : 'ml-2', \" h-5 w-5 group-hover:translate-x-1 transition-transform\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'استكشف المنتجات' : 'Explore Products'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"py-16\", isDarkMode ? \"bg-slate-900\" : \"bg-slate-50\"),\n                id: \"production-lines\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.2,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                            type: \"text\",\n                                            placeholder: currentLanguage === 'ar' ? 'البحث في خطوط الإنتاج...' : 'Search production lines...',\n                                            value: filters.searchQuery,\n                                            onChange: (e)=>handleFilterChange('searchQuery', e.target.value),\n                                            className: \"pl-12 pr-4 py-4 text-lg rounded-xl border-2 border-slate-200 dark:border-slate-700 focus:border-primary-500 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this),\n                                        filters.searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleFilterChange('searchQuery', ''),\n                                            className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-slate-600 dark:text-slate-300 mb-3\",\n                                                children: currentLanguage === 'ar' ? 'الفئات' : 'Categories'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: categoriesWithCounts.map((category)=>{\n                                                    const Icon = category.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                        animation: \"scale\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: filters.category === category.id ? 'primary' : 'outline',\n                                                            onClick: ()=>handleFilterChange('category', category.id),\n                                                            className: \"flex items-center gap-2 text-sm\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                category.name,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-0.5 rounded-full text-xs\", filters.category === category.id ? \"bg-white/20 text-white\" : \"bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300\"),\n                                                                    children: category.count\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, category.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:w-64\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-slate-600 dark:text-slate-300 mb-3\",\n                                                children: currentLanguage === 'ar' ? 'الحالة' : 'Status'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: statusOptions.map((status)=>{\n                                                    const Icon = status.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                        animation: \"scale\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: filters.status === status.id ? 'primary' : 'outline',\n                                                            onClick: ()=>handleFilterChange('status', status.id),\n                                                            className: \"flex items-center gap-2 text-sm\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    size: 16,\n                                                                    className: status.color\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                status.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, status.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setShowFilters(!showFilters),\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    currentLanguage === 'ar' ? 'مرشحات متقدمة' : 'Advanced Filters'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center gap-2 text-sm text-slate-600 dark:text-slate-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: filters.featured,\n                                                            onChange: (e)=>handleFilterChange('featured', e.target.checked),\n                                                            className: \"rounded border-slate-300 text-primary-600 focus:ring-primary-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"text-yellow-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'مميز فقط' : 'Featured Only'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this),\n                                            (filters.searchQuery || filters.category !== 'all' || filters.status !== 'all' || filters.featured) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: resetFilters,\n                                                className: \"text-sm text-slate-500 hover:text-slate-700\",\n                                                children: currentLanguage === 'ar' ? 'إعادة تعيين' : 'Reset Filters'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                children: currentLanguage === 'ar' ? \"\".concat(stats.filteredCount, \" من \").concat(stats.totalLines, \" خط إنتاج\") : \"\".concat(stats.filteredCount, \" of \").concat(stats.totalLines, \" production lines\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: sortBy,\n                                                onChange: (e)=>handleSortChange(e.target.value),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-3 py-2 rounded-lg border text-sm\", isDarkMode ? \"bg-slate-800 border-slate-700 text-white\" : \"bg-white border-slate-300 text-slate-900\"),\n                                                children: sortingOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: option.id,\n                                                        children: option.name\n                                                    }, option.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex rounded-lg border border-slate-300 dark:border-slate-700 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setViewMode('grid'),\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"p-2 transition-colors\", viewMode === 'grid' ? \"bg-primary-500 text-white\" : \"bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setViewMode('list'),\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"p-2 transition-colors\", viewMode === 'list' ? \"bg-primary-500 text-white\" : \"bg-white dark:bg-slate-800 text-slate-600 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, this),\n                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"slide\",\n                            direction: \"down\",\n                            delay: 0.1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: \"p-6 mb-8 border-2 border-primary-200 dark:border-primary-800\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-slate-600 dark:text-slate-300 mb-2\",\n                                                    children: currentLanguage === 'ar' ? 'نطاق الكفاءة (%)' : 'Efficiency Range (%)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            max: \"100\",\n                                                            value: filters.efficiency.min,\n                                                            onChange: (e)=>handleFilterChange('efficiency', {\n                                                                    ...filters.efficiency,\n                                                                    min: Number(e.target.value)\n                                                                }),\n                                                            className: \"w-20\",\n                                                            placeholder: \"Min\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-slate-400\",\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                            type: \"number\",\n                                                            min: \"0\",\n                                                            max: \"100\",\n                                                            value: filters.efficiency.max,\n                                                            onChange: (e)=>handleFilterChange('efficiency', {\n                                                                    ...filters.efficiency,\n                                                                    max: Number(e.target.value)\n                                                                }),\n                                                            className: \"w-20\",\n                                                            placeholder: \"Max\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-slate-600 dark:text-slate-300 mb-2\",\n                                                    children: currentLanguage === 'ar' ? 'الشركة المصنعة' : 'Manufacturer'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filters.manufacturer,\n                                                    onChange: (e)=>handleFilterChange('manufacturer', e.target.value),\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"w-full px-3 py-2 rounded-lg border\", isDarkMode ? \"bg-slate-800 border-slate-700 text-white\" : \"bg-white border-slate-300 text-slate-900\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"all\",\n                                                            children: currentLanguage === 'ar' ? 'جميع الشركات' : 'All Manufacturers'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"AFTAL Industries\",\n                                                            children: \"AFTAL Industries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"AFTAL Food Systems\",\n                                                            children: \"AFTAL Food Systems\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"AFTAL Packaging Solutions\",\n                                                            children: \"AFTAL Packaging Solutions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"AFTAL Pharma Systems\",\n                                                            children: \"AFTAL Pharma Systems\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-semibold text-slate-600 dark:text-slate-300 mb-2\",\n                                                    children: currentLanguage === 'ar' ? 'العلامات' : 'Tags'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1\",\n                                                    children: [\n                                                        'automation',\n                                                        'robotics',\n                                                        'ai',\n                                                        'quality-control',\n                                                        'haccp',\n                                                        'gmp'\n                                                    ].map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                const newTags = filters.tags.includes(tag) ? filters.tags.filter((t)=>t !== tag) : [\n                                                                    ...filters.tags,\n                                                                    tag\n                                                                ];\n                                                                handleFilterChange('tags', newTags);\n                                                            },\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-1 rounded text-xs transition-colors\", filters.tags.includes(tag) ? \"bg-primary-500 text-white\" : \"bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-600\"),\n                                                            children: tag\n                                                        }, tag, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"py-12\", isDarkMode ? \"bg-slate-800\" : \"bg-white\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 13\n                    }, this) : sortedLines.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                        animation: \"fade\",\n                        delay: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"p-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-16 w-16 text-slate-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-slate-900 dark:text-white\",\n                                    children: currentLanguage === 'ar' ? 'لا توجد خطوط إنتاج' : 'No Production Lines Found'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 dark:text-slate-300 mb-6\",\n                                    children: currentLanguage === 'ar' ? 'لم يتم العثور على خطوط إنتاج تطابق معايير البحث الخاصة بك.' : 'No production lines match your search criteria.'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: resetFilters,\n                                    variant: \"outline\",\n                                    children: currentLanguage === 'ar' ? 'إعادة تعيين المرشحات' : 'Reset Filters'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 13\n                    }, this) : viewMode === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollStagger, {\n                        animation: \"slide\",\n                        direction: \"up\",\n                        staggerDelay: 0.1,\n                        delay: 0.3,\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: sortedLines.map((line)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                animation: \"lift\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"flex flex-col overflow-hidden group h-full relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 z-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-1 rounded-full text-xs font-medium\", line.status === 'active' && \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\", line.status === 'maintenance' && \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\", line.status === 'inactive' && \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200\"),\n                                                children: [\n                                                    line.status === 'active' && (currentLanguage === 'ar' ? 'نشط' : 'Active'),\n                                                    line.status === 'maintenance' && (currentLanguage === 'ar' ? 'صيانة' : 'Maintenance'),\n                                                    line.status === 'inactive' && (currentLanguage === 'ar' ? 'غير نشط' : 'Inactive')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 21\n                                        }, this),\n                                        line.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4 z-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        size: 12\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    currentLanguage === 'ar' ? 'مميز' : 'Featured'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_EnhancedImage__WEBPACK_IMPORTED_MODULE_7__.EnhancedImage, {\n                                                    src: line.images[0],\n                                                    alt: line.name,\n                                                    fill: true,\n                                                    objectFit: \"cover\",\n                                                    effect: \"zoom\",\n                                                    progressive: true,\n                                                    placeholder: \"shimmer\",\n                                                    className: \"w-full h-56\",\n                                                    containerClassName: \"w-full h-56\",\n                                                    sizes: \"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            line.videoUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"secondary\",\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                        size: 14,\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    currentLanguage === 'ar' ? 'فيديو' : 'Video'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"secondary\",\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                        size: 14,\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 689,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    currentLanguage === 'ar' ? 'كتالوج' : 'Catalog'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 flex-grow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"inline-block px-2 py-1 rounded text-xs font-medium mb-2\", isDarkMode ? \"bg-primary-900/20 text-primary-400\" : \"bg-primary-100 text-primary-800\"),\n                                                                    children: currentLanguage === 'ar' ? line.category_ar || line.category : line.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-slate-900 dark:text-white\",\n                                                                    children: currentLanguage === 'ar' ? line.name_ar || line.name : line.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 705,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-slate-600 dark:text-slate-300\",\n                                                                    children: currentLanguage === 'ar' ? 'الكفاءة' : 'Efficiency'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-lg font-bold\", line.efficiency >= 95 && \"text-green-600\", line.efficiency >= 90 && line.efficiency < 95 && \"text-yellow-600\", line.efficiency < 90 && \"text-red-600\"),\n                                                                    children: [\n                                                                        line.efficiency,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 dark:text-slate-300 mb-4 line-clamp-2\",\n                                                    children: currentLanguage === 'ar' ? line.description_ar || line.description : line.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentLanguage === 'ar' ? \"السعة: \".concat(line.capacity_ar || line.capacity) : \"Capacity: \".concat(line.capacity)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                    className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentLanguage === 'ar' ? \"استهلاك الطاقة: \".concat(line.energyConsumption) : \"Energy: \".concat(line.energyConsumption)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                    className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                currentLanguage === 'ar' ? \"التكلفة التشغيلية: $\".concat(line.operatingCost, \"/يوم\") : \"Operating Cost: $\".concat(line.operatingCost, \"/day\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 23\n                                                }, this),\n                                                line.tags && line.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-1 mb-4\",\n                                                    children: [\n                                                        line.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded text-xs\",\n                                                                children: tag\n                                                            }, tag, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 29\n                                                            }, this)),\n                                                        line.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded text-xs\",\n                                                            children: [\n                                                                \"+\",\n                                                                line.tags.length - 3\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 pt-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                        animation: \"scale\",\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/\".concat(currentLanguage, \"/production-lines/\").concat(line.slug),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                className: \"w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                        className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 768,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                        animation: \"scale\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"px-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 19\n                                }, this)\n                            }, line.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 13\n                    }, this) : /* List View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollStagger, {\n                        animation: \"slide\",\n                        direction: \"up\",\n                        staggerDelay: 0.05,\n                        delay: 0.3,\n                        className: \"space-y-4\",\n                        children: sortedLines.map((line)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                animation: \"lift\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"p-6 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col lg:flex-row gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"lg:w-64 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_EnhancedImage__WEBPACK_IMPORTED_MODULE_7__.EnhancedImage, {\n                                                            src: line.images[0],\n                                                            alt: line.name,\n                                                            fill: true,\n                                                            objectFit: \"cover\",\n                                                            effect: \"zoom\",\n                                                            progressive: true,\n                                                            placeholder: \"shimmer\",\n                                                            className: \"w-full h-40 lg:h-32 rounded-lg\",\n                                                            containerClassName: \"w-full h-40 lg:h-32 rounded-lg overflow-hidden\",\n                                                            sizes: \"(max-width: 1024px) 100vw, 256px\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        line.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-medium flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    size: 10\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                currentLanguage === 'ar' ? 'مميز' : 'Featured'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col lg:flex-row lg:items-start lg:justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3 mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-1 rounded text-xs font-medium\", isDarkMode ? \"bg-primary-900/20 text-primary-400\" : \"bg-primary-100 text-primary-800\"),\n                                                                                children: currentLanguage === 'ar' ? line.category_ar || line.category : line.category\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 826,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"px-2 py-1 rounded text-xs font-medium\", line.status === 'active' && \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\", line.status === 'maintenance' && \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\", line.status === 'inactive' && \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200\"),\n                                                                                children: [\n                                                                                    line.status === 'active' && (currentLanguage === 'ar' ? 'نشط' : 'Active'),\n                                                                                    line.status === 'maintenance' && (currentLanguage === 'ar' ? 'صيانة' : 'Maintenance'),\n                                                                                    line.status === 'inactive' && (currentLanguage === 'ar' ? 'غير نشط' : 'Inactive')\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 832,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 825,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-slate-900 dark:text-white mb-2\",\n                                                                        children: currentLanguage === 'ar' ? line.name_ar || line.name : line.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 843,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-600 dark:text-slate-300 mb-3 line-clamp-2\",\n                                                                        children: currentLanguage === 'ar' ? line.description_ar || line.description : line.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 846,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right lg:ml-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-slate-600 dark:text-slate-300 mb-1\",\n                                                                        children: currentLanguage === 'ar' ? 'الكفاءة' : 'Efficiency'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"text-2xl font-bold\", line.efficiency >= 95 && \"text-green-600\", line.efficiency >= 90 && line.efficiency < 95 && \"text-yellow-600\", line.efficiency < 90 && \"text-red-600\"),\n                                                                        children: [\n                                                                            line.efficiency,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 855,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 851,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 869,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: currentLanguage === 'ar' ? 'السعة' : 'Capacity'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 871,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: currentLanguage === 'ar' ? line.capacity_ar || line.capacity : line.capacity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 872,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 870,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                        className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 876,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: currentLanguage === 'ar' ? 'الطاقة' : 'Energy'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 878,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: line.energyConsumption\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 879,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 877,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                        className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 883,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: currentLanguage === 'ar' ? 'التكلفة' : 'Cost'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 885,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    line.operatingCost,\n                                                                                    \"/day\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 886,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 884,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-slate-600 dark:text-slate-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4 text-primary-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 890,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: currentLanguage === 'ar' ? 'الصيانة' : 'Maintenance'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 892,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: new Date(line.maintenanceSchedule || '').toLocaleDateString()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 893,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 891,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"/\".concat(currentLanguage, \"/production-lines/\").concat(line.slug),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                                className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                                lineNumber: 903,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                        lineNumber: 902,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            line.videoUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                            lineNumber: 911,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        currentLanguage === 'ar' ? 'فيديو' : 'Video'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 910,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 909,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                            lineNumber: 918,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        currentLanguage === 'ar' ? 'كتالوج' : 'Catalog'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 917,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 916,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                                animation: \"scale\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-4 w-4\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                            lineNumber: 924,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        currentLanguage === 'ar' ? 'إحصائيات' : 'Analytics'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                    lineNumber: 923,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 796,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 19\n                                }, this)\n                            }, line.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 794,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 786,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                lineNumber: 606,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"py-20\", isDarkMode ? \"bg-slate-900\" : \"bg-slate-50\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold mb-6 text-slate-900 dark:text-white\",\n                                        children: currentLanguage === 'ar' ? 'لماذا تختار خطوط إنتاجنا؟' : 'Why Choose Our Production Lines?'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-slate-600 dark:text-slate-300 leading-relaxed\",\n                                        children: currentLanguage === 'ar' ? 'حلول رائدة في الصناعة مدعومة بالابتكار والخبرة مع أحدث التقنيات والمعايير العالمية' : 'Industry-leading solutions backed by innovation and expertise with cutting-edge technology and global standards'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 947,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 943,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 942,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollStagger, {\n                            animation: \"slide\",\n                            direction: \"up\",\n                            staggerDelay: 0.1,\n                            delay: 0.4,\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                            children: [\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-12 w-12 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 964,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: currentLanguage === 'ar' ? 'أتمتة متقدمة' : 'Advanced Automation',\n                                    description: currentLanguage === 'ar' ? 'أنظمة روبوتية وتحكم متطورة مع الذكاء الاصطناعي لتحقيق أقصى قدر من الكفاءة والدقة' : 'State-of-the-art robotics and AI-powered control systems for maximum efficiency and precision',\n                                    features: currentLanguage === 'ar' ? [\n                                        'تحكم PLC متقدم',\n                                        'أنظمة SCADA',\n                                        'صيانة تنبؤية'\n                                    ] : [\n                                        'Advanced PLC Control',\n                                        'SCADA Systems',\n                                        'Predictive Maintenance'\n                                    ]\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-12 w-12 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: currentLanguage === 'ar' ? 'ضمان الجودة' : 'Quality Assurance',\n                                    description: currentLanguage === 'ar' ? 'أنظمة متكاملة لمراقبة الجودة مع مراقبة في الوقت الفعلي ومعايير دولية' : 'Integrated quality control systems with real-time monitoring and international standards',\n                                    features: currentLanguage === 'ar' ? [\n                                        'مراقبة في الوقت الفعلي',\n                                        'معايير ISO',\n                                        'تتبع كامل'\n                                    ] : [\n                                        'Real-time Monitoring',\n                                        'ISO Standards',\n                                        'Full Traceability'\n                                    ]\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-12 w-12 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: currentLanguage === 'ar' ? 'خيارات التخصيص' : 'Customization Options',\n                                    description: currentLanguage === 'ar' ? 'تكوينات مرنة ومعيارية لتلبية احتياجات الإنتاج الخاصة بك مع إمكانية التوسع' : 'Flexible and modular configurations to meet your specific production needs with scalability',\n                                    features: currentLanguage === 'ar' ? [\n                                        'تصميم معياري',\n                                        'قابلية التوسع',\n                                        'تكامل سهل'\n                                    ] : [\n                                        'Modular Design',\n                                        'Scalability',\n                                        'Easy Integration'\n                                    ]\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                        className: \"h-12 w-12 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 994,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: currentLanguage === 'ar' ? 'كفاءة الطاقة' : 'Energy Efficiency',\n                                    description: currentLanguage === 'ar' ? 'تقنيات موفرة للطاقة مع مراقبة استهلاك الطاقة وتحسين التكاليف التشغيلية' : 'Energy-saving technologies with power consumption monitoring and operational cost optimization',\n                                    features: currentLanguage === 'ar' ? [\n                                        'توفير الطاقة',\n                                        'مراقبة الاستهلاك',\n                                        'تحسين التكاليف'\n                                    ] : [\n                                        'Energy Saving',\n                                        'Consumption Monitoring',\n                                        'Cost Optimization'\n                                    ]\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        className: \"h-12 w-12 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1004,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: currentLanguage === 'ar' ? 'دعم شامل' : 'Comprehensive Support',\n                                    description: currentLanguage === 'ar' ? 'دعم فني متخصص وتدريب شامل مع خدمة عملاء على مدار الساعة' : 'Expert technical support and comprehensive training with 24/7 customer service',\n                                    features: currentLanguage === 'ar' ? [\n                                        'دعم 24/7',\n                                        'تدريب متخصص',\n                                        'صيانة دورية'\n                                    ] : [\n                                        '24/7 Support',\n                                        'Expert Training',\n                                        'Regular Maintenance'\n                                    ]\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                        className: \"h-12 w-12 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1014,\n                                        columnNumber: 23\n                                    }, this),\n                                    title: currentLanguage === 'ar' ? 'شهادات عالمية' : 'Global Certifications',\n                                    description: currentLanguage === 'ar' ? 'معتمدة من أهم المنظمات العالمية مع ضمان الامتثال للمعايير الدولية' : 'Certified by leading global organizations with guaranteed compliance to international standards',\n                                    features: currentLanguage === 'ar' ? [\n                                        'شهادات ISO',\n                                        'معايير FDA',\n                                        'امتثال GMP'\n                                    ] : [\n                                        'ISO Certified',\n                                        'FDA Standards',\n                                        'GMP Compliance'\n                                    ]\n                                }\n                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                    animation: \"lift\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"p-8 text-center h-full group hover:shadow-xl transition-shadow duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\", isDarkMode ? \"bg-primary-900/20\" : \"bg-primary-50\"),\n                                                children: feature.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1026,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-4 text-slate-900 dark:text-white\",\n                                                children: feature.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1032,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600 dark:text-slate-300 mb-6 leading-relaxed\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: feature.features.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center text-sm text-slate-500 dark:text-slate-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-500 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                                lineNumber: 1037,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            item\n                                                        ]\n                                                    }, idx, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1025,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                    lineNumber: 1024,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"p-8 text-center\", isDarkMode ? \"bg-primary-900/20\" : \"bg-primary-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold mb-8 text-slate-900 dark:text-white\",\n                                        children: currentLanguage === 'ar' ? 'أداء مثبت' : 'Proven Performance'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1053,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-primary-600 mb-2\",\n                                                        children: \"99.5%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                        children: currentLanguage === 'ar' ? 'معدل التشغيل' : 'Uptime Rate'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1059,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1057,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-primary-600 mb-2\",\n                                                        children: \"30%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1064,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                        children: currentLanguage === 'ar' ? 'توفير في التكاليف' : 'Cost Savings'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1065,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1063,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-primary-600 mb-2\",\n                                                        children: \"50+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1070,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                        children: currentLanguage === 'ar' ? 'دولة' : 'Countries'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1071,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1069,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-primary-600 mb-2\",\n                                                        children: \"24/7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1076,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                        children: currentLanguage === 'ar' ? 'دعم فني' : 'Technical Support'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1077,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1075,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1056,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 1049,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 1048,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                    lineNumber: 941,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                lineNumber: 940,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 bg-gradient-to-br from-primary-600 via-primary-500 to-primary-700 text-white overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-[url('/images/industrial-pattern.svg')] bg-repeat\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 1091,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 1090,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.ScrollAnimation, {\n                            animation: \"fade\",\n                            delay: 0.5,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold mb-6\",\n                                        children: currentLanguage === 'ar' ? 'هل أنت مستعد لتحويل إنتاجك؟' : 'Ready to Transform Your Production?'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1097,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl mb-8 text-primary-50 max-w-3xl mx-auto leading-relaxed\",\n                                        children: currentLanguage === 'ar' ? 'انضم إلى أكثر من 500 شركة حول العالم تثق في حلولنا الصناعية المتطورة. احصل على استشارة مجانية من خبرائنا اليوم.' : 'Join over 500 companies worldwide who trust our advanced industrial solutions. Get a free consultation from our experts today.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"h-6 w-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 1110,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1109,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold mb-2\",\n                                                        children: currentLanguage === 'ar' ? 'استشارة مجانية' : 'Free Consultation'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1112,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-primary-100\",\n                                                        children: currentLanguage === 'ar' ? 'تحدث مع خبرائنا' : 'Talk to our experts'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1115,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                            className: \"h-6 w-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 1121,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold mb-2\",\n                                                        children: currentLanguage === 'ar' ? 'زيارة موقعية' : 'Site Visit'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-primary-100\",\n                                                        children: currentLanguage === 'ar' ? 'تقييم احتياجاتك' : 'Assess your needs'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1126,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1119,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                            className: \"h-6 w-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 1132,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1131,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold mb-2\",\n                                                        children: currentLanguage === 'ar' ? 'كتالوج مجاني' : 'Free Catalog'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1134,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-primary-100\",\n                                                        children: currentLanguage === 'ar' ? 'احصل على المواصفات' : 'Get specifications'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1137,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-center gap-4 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"accent\",\n                                                    size: \"lg\",\n                                                    className: \"px-8 py-4 text-lg font-semibold\",\n                                                    onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/contact\")),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 1151,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'احجز استشارة مجانية' : 'Book Free Consultation'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 1145,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animations__WEBPACK_IMPORTED_MODULE_13__.HoverAnimation, {\n                                                animation: \"scale\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    className: \"px-8 py-4 text-lg font-semibold border-white/30 text-white hover:bg-white/10\",\n                                                    onClick: ()=>router.push(\"/\".concat(currentLanguage, \"/services\")),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                            className: \"\".concat(currentLanguage === 'ar' ? 'ml-2' : 'mr-2', \" h-5 w-5\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                            lineNumber: 1162,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        currentLanguage === 'ar' ? 'تحميل الكتالوج' : 'Download Catalog'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                    lineNumber: 1156,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1155,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-center items-center gap-6 text-primary-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1171,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: currentLanguage === 'ar' ? 'متاح الآن للاستشارة' : 'Available now for consultation'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1172,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_Award_BarChart3_Calendar_CheckCircle_Clock_DollarSign_Download_Eye_Factory_Gauge_Grid3X3_List_MapPin_PenTool_Play_Search_Settings_Shield_SlidersHorizontal_Star_TrendingUp_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1177,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: currentLanguage === 'ar' ? 'استجابة خلال 24 ساعة' : 'Response within 24 hours'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                        lineNumber: 1178,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                                lineNumber: 1176,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                        lineNumber: 1169,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                                lineNumber: 1096,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                            lineNumber: 1095,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                        lineNumber: 1094,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n                lineNumber: 1088,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ecommercepro\\\\src\\\\pages\\\\production\\\\ProductionLinesPage.tsx\",\n        lineNumber: 254,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductionLinesPage, \"eItUqtpcS1lGa7uqoxyRC41kOPI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _stores_themeStore__WEBPACK_IMPORTED_MODULE_9__.useThemeStore,\n        _stores_languageStore__WEBPACK_IMPORTED_MODULE_10__.useLanguageStore,\n        _translations__WEBPACK_IMPORTED_MODULE_11__.useTranslation\n    ];\n});\n_c = ProductionLinesPage;\nvar _c;\n$RefreshReg$(_c, \"ProductionLinesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/pages/production/ProductionLinesPage.tsx\n"));

/***/ })

});