import { NextRequest, NextResponse } from 'next/server';
import { MockSQLiteDatabase } from '../../../lib/sqlite';

// Initialize database instance
const sqliteDB = new MockSQLiteDatabase();

// Interface for service booking request
interface ServiceBookingRequest {
  serviceName: string;
  fullName: string;
  email: string;
  phone: string;
  companyName: string;
  serviceDate: string;
  preferredTime: string;
  urgency: string;
  message: string;
}

// Interface for service booking record
interface ServiceBooking {
  id: string;
  serviceName: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  companyName: string;
  serviceDate: string;
  preferredTime: string;
  urgency: string;
  message: string;
  status: 'pending' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
}

// POST - Create new service booking
export async function POST(request: NextRequest) {
  try {
    const body: ServiceBookingRequest = await request.json();

    // Validate required fields
    const requiredFields = ['serviceName', 'fullName', 'email', 'phone', 'serviceDate'];
    const missingFields = requiredFields.filter(field => !body[field as keyof ServiceBookingRequest]);

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `Missing required fields: ${missingFields.join(', ')}`
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email format'
        },
        { status: 400 }
      );
    }

    // Validate service date (must be in the future)
    const serviceDate = new Date(body.serviceDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (serviceDate < today) {
      return NextResponse.json(
        {
          success: false,
          error: 'Service date must be in the future'
        },
        { status: 400 }
      );
    }

    // Create service booking record
    const bookingId = `sb-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();

    const serviceBooking: ServiceBooking = {
      id: bookingId,
      serviceName: body.serviceName,
      customerName: body.fullName,
      customerEmail: body.email,
      customerPhone: body.phone,
      companyName: body.companyName || '',
      serviceDate: body.serviceDate,
      preferredTime: body.preferredTime || '',
      urgency: body.urgency || 'normal',
      message: body.message || '',
      status: 'pending',
      createdAt: now,
      updatedAt: now
    };

    // Save to database
    try {
      // Get existing bookings
      const existingBookings = sqliteDB.getServiceBookings?.() || [];

      // Add new booking
      existingBookings.push(serviceBooking);

      // Save updated bookings
      sqliteDB.saveServiceBookings?.(existingBookings);

      // Return success response
      return NextResponse.json({
        success: true,
        data: {
          bookingId: serviceBooking.id,
          status: serviceBooking.status,
          message: 'Service booking created successfully'
        }
      }, { status: 201 });

    } catch (dbError) {
      console.error('Database error:', dbError);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to save booking to database'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Service booking error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    );
  }
}

// GET - Retrieve service bookings (for admin)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get all bookings from database
    const allBookings = sqliteDB.getServiceBookings?.() || [];

    // Filter by status if provided
    let filteredBookings = allBookings;
    if (status && status !== 'all') {
      filteredBookings = allBookings.filter(booking => booking.status === status);
    }

    // Sort by creation date (newest first)
    filteredBookings.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Apply pagination
    const paginatedBookings = filteredBookings.slice(offset, offset + limit);

    return NextResponse.json({
      success: true,
      data: {
        bookings: paginatedBookings,
        total: filteredBookings.length,
        limit,
        offset
      }
    });

  } catch (error) {
    console.error('Error retrieving service bookings:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve service bookings'
      },
      { status: 500 }
    );
  }
}

// PUT - Update service booking status
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { bookingId, status, notes } = body;

    if (!bookingId || !status) {
      return NextResponse.json(
        {
          success: false,
          error: 'Booking ID and status are required'
        },
        { status: 400 }
      );
    }

    // Validate status
    const validStatuses = ['pending', 'confirmed', 'in-progress', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid status value'
        },
        { status: 400 }
      );
    }

    // Get existing bookings
    const existingBookings = sqliteDB.getServiceBookings?.() || [];

    // Find and update the booking
    const bookingIndex = existingBookings.findIndex(booking => booking.id === bookingId);

    if (bookingIndex === -1) {
      return NextResponse.json(
        {
          success: false,
          error: 'Booking not found'
        },
        { status: 404 }
      );
    }

    // Update the booking
    existingBookings[bookingIndex] = {
      ...existingBookings[bookingIndex],
      status,
      updatedAt: new Date().toISOString(),
      ...(notes && { notes })
    };

    // Save updated bookings
    sqliteDB.saveServiceBookings?.(existingBookings);

    return NextResponse.json({
      success: true,
      data: {
        booking: existingBookings[bookingIndex],
        message: 'Booking status updated successfully'
      }
    });

  } catch (error) {
    console.error('Error updating service booking:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update booking status'
      },
      { status: 500 }
    );
  }
}
