"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/data/blogPosts.ts":
/*!*******************************!*\
  !*** ./src/data/blogPosts.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blogCategories: () => (/* binding */ blogCategories),\n/* harmony export */   blogPosts: () => (/* binding */ blogPosts)\n/* harmony export */ });\nconst blogPosts = [\n    {\n        id: '1',\n        title: 'Global Supply Chain Trends 2025: Navigating the Future of Trade',\n        title_ar: undefined,\n        slug: 'global-supply-chain-trends-2025',\n        excerpt: 'Explore emerging trends in international trade and how businesses can adapt their supply chain strategies for success.',\n        excerpt_ar: undefined,\n        content: \"\\n      The landscape of global trade is rapidly evolving, driven by technological advancements, changing regulations, and shifting market dynamics. Understanding these trends is crucial for businesses looking to maintain competitive advantage in international markets.\\n\\n      ## Key Trends Shaping International Trade\\n\\n      ### 1. Digital Transformation\\n      The digitalization of trade processes is accelerating, with blockchain technology and AI playing pivotal roles in:\\n      - Supply chain transparency\\n      - Documentation automation\\n      - Risk management\\n      - Trade finance\\n\\n      ### 2. Sustainable Logistics\\n      Environmental consciousness is becoming a core consideration in trade decisions:\\n      - Green shipping alternatives\\n      - Carbon footprint tracking\\n      - Sustainable packaging solutions\\n      - Environmental compliance\\n\\n      ### 3. Regional Trade Networks\\n      We're seeing a shift towards regional trade networks that offer:\\n      - Reduced transportation costs\\n      - Faster delivery times\\n      - Greater supply chain resilience\\n      - Enhanced market access\\n\\n      ## Adapting Your Business Strategy\\n\\n      To thrive in this evolving landscape, businesses should:\\n\\n      1. Invest in digital infrastructure\\n      2. Develop sustainable practices\\n      3. Diversify supply chains\\n      4. Build regional partnerships\\n\\n      ## Our Solutions\\n\\n      At CommercePro, we offer comprehensive solutions to help you navigate these changes:\\n\\n      - Advanced tracking systems for supply chain visibility\\n      - Sustainable packaging options\\n      - Regional distribution networks\\n      - Digital documentation management\\n\\n      Contact us to learn how we can help optimize your international trade operations.\\n    \",\n        content_ar: undefined,\n        author: 'Sarah Chen',\n        authorTitle: 'International Trade Specialist',\n        authorImage: '/images/placeholder-light.svg',\n        coverImage: '/images/placeholder-light.svg',\n        category: 'International Trade',\n        tags: [\n            'Supply Chain',\n            'Technology',\n            'Sustainability',\n            'Trade'\n        ],\n        publishedAt: '2025-03-01T08:00:00Z',\n        readTime: '8 min read',\n        featured: false\n    },\n    {\n        id: '2',\n        title: 'Mastering Export Documentation: A Comprehensive Guide',\n        title_ar: undefined,\n        slug: 'export-documentation-guide',\n        excerpt: 'Essential knowledge about export documentation requirements and best practices for international trade success.',\n        excerpt_ar: undefined,\n        content: \"\\n      Proper documentation is the foundation of successful international trade. This comprehensive guide will help you understand and manage export documentation effectively.\\n\\n      ## Essential Export Documents\\n\\n      ### 1. Commercial Documents\\n      - Commercial Invoice\\n      - Packing List\\n      - Certificate of Origin\\n      - Bill of Lading/Airway Bill\\n\\n      ### 2. Regulatory Documents\\n      - Export License\\n      - Export Declaration\\n      - Safety Certificates\\n      - Health Certificates\\n\\n      ### 3. Financial Documents\\n      - Letter of Credit\\n      - Bank Draft\\n      - Insurance Certificate\\n\\n      ## Common Documentation Challenges\\n\\n      1. Incomplete Information\\n      2. Inconsistent Data\\n      3. Delayed Processing\\n      4. Regulatory Compliance\\n\\n      ## Best Practices\\n\\n      - Maintain accurate records\\n      - Use digital documentation systems\\n      - Regular staff training\\n      - Work with experienced partners\\n\\n      ## Our Documentation Services\\n\\n      CommercePro offers comprehensive documentation support:\\n      - Digital document management\\n      - Compliance checking\\n      - Expert consultation\\n      - Training programs\\n\\n      Contact our team to streamline your export documentation process.\\n    \",\n        content_ar: undefined,\n        author: 'Michael Rodriguez',\n        authorTitle: 'Export Documentation Specialist',\n        authorImage: '/images/placeholder-light.svg',\n        coverImage: '/images/placeholder-light.svg',\n        category: 'Export',\n        tags: [\n            'Documentation',\n            'Compliance',\n            'International Trade'\n        ],\n        publishedAt: '2025-02-28T10:00:00Z',\n        readTime: '10 min read',\n        featured: false\n    },\n    {\n        id: '3',\n        title: 'Smart Manufacturing: Industry 4.0 Implementation Guide',\n        title_ar: undefined,\n        slug: 'smart-manufacturing-guide',\n        excerpt: 'A practical guide to implementing Industry 4.0 technologies in your manufacturing operations.',\n        excerpt_ar: undefined,\n        content: \"\\n      Smart manufacturing is revolutionizing production processes. Learn how to implement Industry 4.0 technologies effectively in your operations.\\n\\n      ## Key Components of Smart Manufacturing\\n\\n      ### 1. IoT Integration\\n      - Real-time monitoring\\n      - Predictive maintenance\\n      - Asset tracking\\n      - Quality control\\n\\n      ### 2. Data Analytics\\n      - Production optimization\\n      - Quality prediction\\n      - Resource management\\n      - Cost reduction\\n\\n      ### 3. Automation Systems\\n      - Robotic process automation\\n      - Automated quality control\\n      - Smart inventory management\\n      - Autonomous logistics\\n\\n      ## Implementation Strategy\\n\\n      1. Assessment Phase\\n      2. Technology Selection\\n      3. Pilot Implementation\\n      4. Scale-up Process\\n\\n      ## Success Stories\\n\\n      Learn how our clients achieved success with our smart manufacturing solutions:\\n\\n      ### Case Study: TechPro Manufacturing\\n      - 40% reduction in downtime\\n      - 25% increase in productivity\\n      - 15% cost savings\\n      - ROI within 18 months\\n\\n      ## Our Smart Manufacturing Solutions\\n\\n      Explore our range of Industry 4.0 products:\\n      - IoT Sensor Systems\\n      - Manufacturing Analytics Platform\\n      - Automated Quality Control\\n      - Smart Inventory Management\\n\\n      Contact us to start your smart manufacturing journey.\\n    \",\n        content_ar: undefined,\n        author: 'David Zhang',\n        authorTitle: 'Manufacturing Technology Expert',\n        authorImage: '/images/placeholder-light.svg',\n        coverImage: '/images/placeholder-light.svg',\n        category: 'Manufacturing',\n        tags: [\n            'Industry 4.0',\n            'Technology',\n            'Automation'\n        ],\n        publishedAt: '2025-02-25T09:00:00Z',\n        readTime: '12 min read',\n        featured: false\n    },\n    {\n        id: '4',\n        title: 'Upcoming Trade Shows and Events 2025',\n        title_ar: undefined,\n        slug: 'trade-shows-2025',\n        excerpt: 'Mark your calendar for the most important international trade shows and industry events in 2025.',\n        excerpt_ar: undefined,\n        content: \"\\n      Stay informed about upcoming trade shows and industry events where you can network, learn about new technologies, and explore business opportunities.\\n\\n      ## Q1 2025 Events\\n\\n      ### International Manufacturing Expo\\n      - Date: January 15-17, 2025\\n      - Location: Singapore\\n      - Focus: Smart manufacturing technologies\\n      - CommercePro Booth: Hall A, Stand 45\\n\\n      ### Global Trade Summit\\n      - Date: March 8-10, 2025\\n      - Location: Dubai, UAE\\n      - Focus: International trade policies\\n      - Special Session: \\\"Future of Digital Trade\\\"\\n\\n      ## Q2 2025 Events\\n\\n      ### European Industry Fair\\n      - Date: May 20-23, 2025\\n      - Location: Frankfurt, Germany\\n      - Focus: Industrial automation\\n      - Product Launch: New IoT Sensor System\\n\\n      ## Meet Us There\\n\\n      Visit our booth at these events to:\\n      - See live product demos\\n      - Meet our experts\\n      - Get exclusive offers\\n      - Network with industry leaders\\n\\n      ## Can't Attend?\\n\\n      We've got you covered:\\n      - Virtual booth tours\\n      - Online product demonstrations\\n      - Digital networking sessions\\n      - Post-event content access\\n\\n      Contact us to schedule a meeting at any of these events.\\n    \",\n        content_ar: undefined,\n        author: 'Emma Thompson',\n        authorTitle: 'Events Coordinator',\n        authorImage: '/images/placeholder-light.svg',\n        coverImage: '/images/placeholder-light.svg',\n        category: 'Events',\n        tags: [\n            'Trade Shows',\n            'Networking',\n            'Industry Events'\n        ],\n        publishedAt: '2025-02-20T10:00:00Z',\n        readTime: '6 min read',\n        featured: false\n    }\n];\nconst blogCategories = [\n    {\n        id: 'international-trade',\n        name: 'International Trade',\n        description: 'Insights and guides on global trade practices'\n    },\n    {\n        id: 'export',\n        name: 'Export',\n        description: 'Expert advice on export procedures and documentation'\n    },\n    {\n        id: 'manufacturing',\n        name: 'Manufacturing',\n        description: 'Latest trends in industrial manufacturing'\n    },\n    {\n        id: 'logistics',\n        name: 'Logistics',\n        description: 'Supply chain and logistics management'\n    },\n    {\n        id: 'events',\n        name: 'Events',\n        description: 'Upcoming trade shows and industry events'\n    },\n    {\n        id: 'market-insights',\n        name: 'Market Insights',\n        description: 'Analysis of market trends and opportunities'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/blogPosts.ts\n"));

/***/ })

});