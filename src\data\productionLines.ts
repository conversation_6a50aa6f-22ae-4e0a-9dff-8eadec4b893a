import { ProductionLine } from '../types/index';

export const productionLines: ProductionLine[] = [
  {
    id: 'automated-assembly',
    name: 'Automated Assembly Line',
    slug: 'automated-assembly',
    description: 'State-of-the-art automated assembly system with advanced robotics and AI-driven quality control.',
    category: 'Manufacturing',
    capacity: '10,000 units per day',
    specifications: {
      'Line Length': '50 meters',
      'Cycle Time': '45 seconds/unit',
      'Power Requirements': '380V, 3-phase',
      'Control System': 'PLC with SCADA integration',
      'Quality Control': 'AI-powered vision system',
    },
    features: [
      'Flexible configuration for different products',
      'Real-time production monitoring',
      'Predictive maintenance system',
      'Integrated quality control',
      'Remote operation capability'
    ],
    images: ['https://images.pexels.com/photos/2159235/pexels-photo-2159235.jpeg'],
    createdAt: new Date().toISOString()
  },
  {
    id: 'food-processing',
    name: 'Food Processing Line',
    slug: 'food-processing',
    description: 'Complete food processing line with hygiene certification and temperature-controlled zones.',
    category: 'Food & Beverage',
    capacity: '5,000 kg per hour',
    specifications: {
      'Processing Area': '200 square meters',
      'Temperature Range': '-5°C to +40°C',
      'Hygiene Standard': 'ISO 22000 certified',
      'Material': '304 Stainless Steel',
      'Cleaning System': 'CIP (Clean-in-Place)',
    },
    features: [
      'HACCP compliant design',
      'Automated cleaning system',
      'Temperature monitoring',
      'Product tracking system',
      'Energy-efficient operation'
    ],
    images: ['https://images.pexels.com/photos/2874793/pexels-photo-2874793.jpeg'],
    createdAt: new Date().toISOString()
  },
  {
    id: 'packaging-automation',
    name: 'Smart Packaging Line',
    slug: 'packaging-automation',
    description: 'Intelligent packaging system with automatic product sorting and custom packaging options.',
    category: 'Packaging',
    capacity: '15,000 packages per day',
    specifications: {
      'Speed': '120 units/minute',
      'Package Sizes': '10cm - 50cm',
      'Label System': 'Digital printing',
      'Vision System': '4K cameras',
      'Sorting Accuracy': '99.9%',
    },
    features: [
      'Multiple packaging formats',
      'Automatic size adjustment',
      'Integrated labeling system',
      'Quality verification',
      'Waste reduction system'
    ],
    images: ['https://images.pexels.com/photos/3862130/pexels-photo-3862130.jpeg'],
    createdAt: new Date().toISOString()
  },
  {
    id: 'pharma-production',
    name: 'Pharmaceutical Production Line',
    slug: 'pharma-production',
    description: 'GMP-compliant pharmaceutical production line with clean room integration.',
    category: 'Pharmaceutical',
    capacity: '100,000 units per batch',
    specifications: {
      'Clean Room Class': 'ISO 7',
      'Process Control': 'FDA 21 CFR Part 11',
      'Batch System': 'Electronic batch records',
      'Air Quality': 'HEPA filtered',
      'Material Handling': 'Automated transfer',
    },
    features: [
      'GMP compliance',
      'Environmental monitoring',
      'Batch tracking system',
      'Cross-contamination prevention',
      'Complete documentation'
    ],
    images: ['https://images.pexels.com/photos/3825584/pexels-photo-3825584.jpeg'],
    createdAt: new Date().toISOString()
  }
];