import { useState } from 'react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card } from '../ui/Card';
import {
  Send,
  X,
  AlertCircle,
  CheckCircle,
  Loader2,
  User,
  Mail,
  Phone,
  Building,
  Calendar,
  MessageSquare,
  Clock
} from 'lucide-react';
import { useLanguageStore } from '../../stores/languageStore';
import { useThemeStore } from '../../stores/themeStore';
import { cn } from '../../lib/utils';

interface ServiceBookingFormProps {
  onClose: () => void;
  serviceName?: string;
}

interface FormData {
  fullName: string;
  email: string;
  phone: string;
  companyName: string;
  serviceDate: string;
  preferredTime: string;
  urgency: string;
  message: string;
}

interface FormErrors {
  [key: string]: string;
}

export function ServiceBookingForm({ onClose, serviceName }: ServiceBookingFormProps) {
  const { language } = useLanguageStore();
  const { isDarkMode } = useThemeStore();
  const [formData, setFormData] = useState<FormData>({
    fullName: '',
    email: '',
    phone: '',
    companyName: '',
    serviceDate: '',
    preferredTime: '',
    urgency: 'normal',
    message: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Get minimum date (today)
  const today = new Date().toISOString().split('T')[0];

  const urgencyOptions = [
    { value: 'low', label: language === 'ar' ? 'عادي' : 'Normal' },
    { value: 'normal', label: language === 'ar' ? 'متوسط' : 'Standard' },
    { value: 'high', label: language === 'ar' ? 'عاجل' : 'Urgent' },
    { value: 'critical', label: language === 'ar' ? 'طارئ' : 'Critical' }
  ];

  const timeSlots = [
    { value: 'morning', label: language === 'ar' ? 'صباحاً (8:00 - 12:00)' : 'Morning (8:00 AM - 12:00 PM)' },
    { value: 'afternoon', label: language === 'ar' ? 'بعد الظهر (12:00 - 17:00)' : 'Afternoon (12:00 PM - 5:00 PM)' },
    { value: 'evening', label: language === 'ar' ? 'مساءً (17:00 - 20:00)' : 'Evening (5:00 PM - 8:00 PM)' },
    { value: 'flexible', label: language === 'ar' ? 'مرن' : 'Flexible' }
  ];

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Full Name validation
    if (!formData.fullName.trim()) {
      newErrors.fullName = language === 'ar' ? 'الاسم الكامل مطلوب' : 'Full name is required';
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = language === 'ar' ? 'الاسم يجب أن يكون حرفين على الأقل' : 'Name must be at least 2 characters';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = language === 'ar' ? 'البريد الإلكتروني مطلوب' : 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = language === 'ar' ? 'البريد الإلكتروني غير صحيح' : 'Invalid email format';
    }

    // Phone validation
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!formData.phone.trim()) {
      newErrors.phone = language === 'ar' ? 'رقم الهاتف مطلوب' : 'Phone number is required';
    } else if (!phoneRegex.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
      newErrors.phone = language === 'ar' ? 'رقم الهاتف غير صحيح' : 'Invalid phone number';
    }

    // Company Name validation
    if (!formData.companyName.trim()) {
      newErrors.companyName = language === 'ar' ? 'اسم الشركة مطلوب' : 'Company name is required';
    }

    // Service Date validation
    if (!formData.serviceDate) {
      newErrors.serviceDate = language === 'ar' ? 'تاريخ الخدمة مطلوب' : 'Service date is required';
    } else if (formData.serviceDate < today) {
      newErrors.serviceDate = language === 'ar' ? 'لا يمكن اختيار تاريخ في الماضي' : 'Cannot select a past date';
    }

    // Preferred Time validation
    if (!formData.preferredTime) {
      newErrors.preferredTime = language === 'ar' ? 'الوقت المفضل مطلوب' : 'Preferred time is required';
    }

    // Message validation
    if (!formData.message.trim()) {
      newErrors.message = language === 'ar' ? 'تفاصيل إضافية مطلوبة' : 'Additional details are required';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = language === 'ar' ? 'التفاصيل يجب أن تكون 10 أحرف على الأقل' : 'Details must be at least 10 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Booking submitted:', { service: serviceName, ...formData });
      setIsSubmitted(true);

      // Auto close after success
      setTimeout(() => {
        onClose();
      }, 3000);
    } catch (error) {
      console.error('Booking error:', error);
      setErrors({ submit: language === 'ar' ? 'حدث خطأ أثناء الحجز' : 'Booking error occurred' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  if (isSubmitted) {
    return (
      <Card className="p-8 text-center">
        <div className="flex justify-center mb-4">
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
          {language === 'ar' ? 'تم حجز الخدمة بنجاح!' : 'Service Booked Successfully!'}
        </h3>
        <p className="text-slate-600 dark:text-slate-300 mb-4">
          {language === 'ar'
            ? 'سنتواصل معك قريباً لتأكيد موعد الخدمة وتفاصيل أخرى.'
            : 'We\'ll contact you soon to confirm the service appointment and other details.'}
        </p>
        <div className={cn(
          "p-3 rounded-lg mb-4",
          isDarkMode ? "bg-slate-700" : "bg-slate-50"
        )}>
          <p className="text-sm text-slate-600 dark:text-slate-300">
            <strong>{language === 'ar' ? 'رقم المرجع:' : 'Reference ID:'}</strong> #{Math.random().toString(36).substr(2, 9).toUpperCase()}
          </p>
        </div>
        <Button onClick={onClose} variant="primary">
          {language === 'ar' ? 'إغلاق' : 'Close'}
        </Button>
      </Card>
    );
  }

  return (
    <Card className="p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
          {language === 'ar'
            ? `حجز خدمة ${serviceName || 'الأعمال'}`
            : `Book ${serviceName || 'Service'}`}
        </h3>
        <button
          onClick={onClose}
          className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
          disabled={isSubmitting}
        >
          <X size={20} />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information Section */}
        <div className={cn(
          "p-4 rounded-lg border-l-4 border-primary-500",
          isDarkMode ? "bg-slate-800/50" : "bg-primary-50/50"
        )}>
          <h4 className="font-medium text-slate-900 dark:text-white mb-4">
            {language === 'ar' ? 'المعلومات الشخصية' : 'Personal Information'}
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Full Name */}
            <div>
              <label className={cn(
                "block text-sm font-medium mb-2",
                isDarkMode ? "text-slate-300" : "text-slate-700"
              )}>
                <User className="inline w-4 h-4 mr-2" />
                {language === 'ar' ? 'الاسم الكامل' : 'Full Name'}
                <span className="text-red-500 ml-1">*</span>
              </label>
              <Input
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                placeholder={language === 'ar' ? 'أدخل اسمك الكامل' : 'Enter your full name'}
                className={cn(
                  "transition-all duration-300",
                  errors.fullName && "border-red-500 focus:ring-red-500"
                )}
                disabled={isSubmitting}
              />
              {errors.fullName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.fullName}
                </p>
              )}
            </div>

            {/* Email */}
            <div>
              <label className={cn(
                "block text-sm font-medium mb-2",
                isDarkMode ? "text-slate-300" : "text-slate-700"
              )}>
                <Mail className="inline w-4 h-4 mr-2" />
                {language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'}
                <span className="text-red-500 ml-1">*</span>
              </label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder={language === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email address'}
                className={cn(
                  "transition-all duration-300",
                  errors.email && "border-red-500 focus:ring-red-500"
                )}
                disabled={isSubmitting}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.email}
                </p>
              )}
            </div>

            {/* Phone */}
            <div>
              <label className={cn(
                "block text-sm font-medium mb-2",
                isDarkMode ? "text-slate-300" : "text-slate-700"
              )}>
                <Phone className="inline w-4 h-4 mr-2" />
                {language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
                <span className="text-red-500 ml-1">*</span>
              </label>
              <Input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder={language === 'ar' ? 'أدخل رقم هاتفك' : 'Enter your phone number'}
                className={cn(
                  "transition-all duration-300",
                  errors.phone && "border-red-500 focus:ring-red-500"
                )}
                disabled={isSubmitting}
              />
              {errors.phone && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.phone}
                </p>
              )}
            </div>

            {/* Company Name */}
            <div>
              <label className={cn(
                "block text-sm font-medium mb-2",
                isDarkMode ? "text-slate-300" : "text-slate-700"
              )}>
                <Building className="inline w-4 h-4 mr-2" />
                {language === 'ar' ? 'اسم الشركة' : 'Company Name'}
                <span className="text-red-500 ml-1">*</span>
              </label>
              <Input
                name="companyName"
                value={formData.companyName}
                onChange={handleChange}
                placeholder={language === 'ar' ? 'أدخل اسم شركتك' : 'Enter your company name'}
                className={cn(
                  "transition-all duration-300",
                  errors.companyName && "border-red-500 focus:ring-red-500"
                )}
                disabled={isSubmitting}
              />
              {errors.companyName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.companyName}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Service Details Section */}
        <div className={cn(
          "p-4 rounded-lg border-l-4 border-blue-500",
          isDarkMode ? "bg-slate-800/50" : "bg-blue-50/50"
        )}>
          <h4 className="font-medium text-slate-900 dark:text-white mb-4">
            {language === 'ar' ? 'تفاصيل الخدمة' : 'Service Details'}
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Service Date */}
            <div>
              <label className={cn(
                "block text-sm font-medium mb-2",
                isDarkMode ? "text-slate-300" : "text-slate-700"
              )}>
                <Calendar className="inline w-4 h-4 mr-2" />
                {language === 'ar' ? 'تاريخ الخدمة المفضل' : 'Preferred Service Date'}
                <span className="text-red-500 ml-1">*</span>
              </label>
              <Input
                type="date"
                name="serviceDate"
                value={formData.serviceDate}
                onChange={handleChange}
                min={today}
                className={cn(
                  "transition-all duration-300",
                  errors.serviceDate && "border-red-500 focus:ring-red-500"
                )}
                disabled={isSubmitting}
              />
              {errors.serviceDate && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.serviceDate}
                </p>
              )}
            </div>

            {/* Preferred Time */}
            <div>
              <label className={cn(
                "block text-sm font-medium mb-2",
                isDarkMode ? "text-slate-300" : "text-slate-700"
              )}>
                <Clock className="inline w-4 h-4 mr-2" />
                {language === 'ar' ? 'الوقت المفضل' : 'Preferred Time'}
                <span className="text-red-500 ml-1">*</span>
              </label>
              <select
                name="preferredTime"
                value={formData.preferredTime}
                onChange={handleChange}
                className={cn(
                  "w-full p-3 rounded-lg border transition-all duration-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
                  isDarkMode
                    ? "bg-slate-700 border-slate-600 text-white"
                    : "bg-white border-slate-200 text-slate-900",
                  errors.preferredTime && "border-red-500 focus:ring-red-500"
                )}
                disabled={isSubmitting}
              >
                <option value="">
                  {language === 'ar' ? 'اختر الوقت المفضل' : 'Select preferred time'}
                </option>
                {timeSlots.map(slot => (
                  <option key={slot.value} value={slot.value}>
                    {slot.label}
                  </option>
                ))}
              </select>
              {errors.preferredTime && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.preferredTime}
                </p>
              )}
            </div>

            {/* Urgency Level */}
            <div className="md:col-span-2">
              <label className={cn(
                "block text-sm font-medium mb-2",
                isDarkMode ? "text-slate-300" : "text-slate-700"
              )}>
                {language === 'ar' ? 'مستوى الأولوية' : 'Priority Level'}
              </label>
              <select
                name="urgency"
                value={formData.urgency}
                onChange={handleChange}
                className={cn(
                  "w-full p-3 rounded-lg border transition-all duration-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500",
                  isDarkMode
                    ? "bg-slate-700 border-slate-600 text-white"
                    : "bg-white border-slate-200 text-slate-900"
                )}
                disabled={isSubmitting}
              >
                {urgencyOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Additional Requirements */}
        <div>
          <label className={cn(
            "block text-sm font-medium mb-2",
            isDarkMode ? "text-slate-300" : "text-slate-700"
          )}>
            <MessageSquare className="inline w-4 h-4 mr-2" />
            {language === 'ar' ? 'متطلبات إضافية' : 'Additional Requirements'}
            <span className="text-red-500 ml-1">*</span>
          </label>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleChange}
            placeholder={language === 'ar'
              ? 'يرجى وصف متطلبات الخدمة والتفاصيل الإضافية...'
              : 'Please describe your service requirements and additional details...'}
            className={cn(
              "w-full min-h-[120px] px-3 py-2 border rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 resize-none",
              isDarkMode
                ? "bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:ring-primary-500 focus:border-primary-500"
                : "bg-white border-slate-200 text-slate-900 placeholder-slate-500 focus:ring-primary-500 focus:border-primary-500",
              errors.message && "border-red-500 focus:ring-red-500"
            )}
            disabled={isSubmitting}
          />
          {errors.message && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.message}
            </p>
          )}
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="p-3 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-2" />
              {errors.submit}
            </p>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t border-slate-200 dark:border-slate-700">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            {language === 'ar' ? 'إلغاء' : 'Cancel'}
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            className="flex items-center gap-2 min-w-[140px]"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                {language === 'ar' ? 'جاري الحجز...' : 'Booking...'}
              </>
            ) : (
              <>
                <Send className="w-4 h-4" />
                {language === 'ar' ? 'تأكيد الحجز' : 'Confirm Booking'}
              </>
            )}
          </Button>
        </div>
      </form>
    </Card>
  );
}