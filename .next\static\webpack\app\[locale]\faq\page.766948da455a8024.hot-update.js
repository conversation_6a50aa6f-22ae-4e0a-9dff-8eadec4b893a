"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/faq/page",{

/***/ "(app-pages-browser)/./src/translations/index.ts":
/*!***********************************!*\
  !*** ./src/translations/index.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLocale: () => (/* binding */ useLocale),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var _stores_languageStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stores/languageStore */ \"(app-pages-browser)/./src/stores/languageStore.ts\");\n\nconst translations = {\n    en: {\n        // App Info\n        'app.name': 'ARTAL',\n        'app.tagline': 'Your Complete Business Solution',\n        // Navigation\n        'nav.home': 'Home',\n        'nav.shop': 'Shop',\n        'nav.productionLines': 'Production Lines',\n        'nav.services': 'Services',\n        'nav.blog': 'Blog',\n        'nav.contact': 'Contact',\n        // Header Actions\n        'header.search': 'Search',\n        'header.searchPlaceholder': 'Search products...',\n        'header.specialOffers': 'Special Offers',\n        'header.wishlist': 'Wishlist',\n        'header.cart': 'Cart',\n        'header.account': 'Account',\n        'header.signIn': 'Sign In',\n        'header.signUp': 'Sign Up',\n        // Hero Section\n        'hero.cta.explore': 'Explore Products',\n        'hero.cta.services': 'Our Services',\n        // Business Solutions\n        'solutions.title': 'Comprehensive Business Solutions',\n        'solutions.subtitle': 'Explore our full range of services designed to meet all your commercial needs.',\n        'solutions.features': 'Key Features',\n        // Products Section\n        'products.title': 'Featured Products',\n        'products.subtitle': 'Discover our latest innovations and best-selling industrial solutions.',\n        'products.viewAll': 'View All Products',\n        'products.newArrivals': 'New Arrivals',\n        'products.inStock': 'In Stock',\n        'products.outOfStock': 'Out of Stock',\n        'products.addToCart': 'Add to Cart',\n        'products.requestQuote': 'Request Quote',\n        // Shop Section\n        'shop.title': 'Shop',\n        'shop.filters.title': 'Filters',\n        'shop.filters.categories': 'Categories',\n        'shop.filters.allCategories': 'All Categories',\n        'shop.filters.priceRange': 'Price Range',\n        'shop.filters.availability': 'Availability & Features',\n        'shop.filters.inStockOnly': 'In Stock Only',\n        'shop.filters.onSaleOnly': 'On Sale Only',\n        'shop.filters.featuredOnly': 'Featured Products Only',\n        'shop.filters.newArrivalsOnly': 'New Arrivals Only',\n        'shop.filters.reset': 'Reset Filters',\n        'shop.filters.clearAll': 'Clear All',\n        'shop.filters.activeFilters': 'Active Filters',\n        'shop.filters.showFilters': 'Show Filters',\n        'shop.filters.hideFilters': 'Hide Filters',\n        'shop.search.placeholder': 'Search for products...',\n        'shop.search.clear': 'Clear search',\n        'shop.sort.featured': 'Featured',\n        'shop.sort.newest': 'Newest',\n        'shop.sort.priceAsc': 'Price: Low to High',\n        'shop.sort.priceDesc': 'Price: High to Low',\n        'shop.sort.popular': 'Most Popular',\n        'shop.sort.discount': 'Highest Discount',\n        'shop.categories.browse': 'Browse by Category',\n        'shop.categories.viewAll': 'View All in Category',\n        // Blog Section\n        'blog.title': 'Latest Industry Insights',\n        'blog.subtitle': 'Stay informed with our latest articles, trends, and industry news.',\n        'blog.readMore': 'Read More',\n        'blog.viewAll': 'View All Articles',\n        // Common Actions\n        'actions.viewDetails': 'View Details',\n        'actions.learnMore': 'Learn More',\n        'actions.getStarted': 'Get Started',\n        'actions.contactUs': 'Contact Us',\n        'actions.bookNow': 'Book Now',\n        // Authentication\n        'auth.signIn': 'Sign In',\n        'auth.signUp': 'Sign Up',\n        'auth.email': 'Email',\n        'auth.password': 'Password',\n        'auth.forgotPassword': 'Forgot Password?',\n        'auth.firstName': 'First Name',\n        'auth.lastName': 'Last Name',\n        'auth.processing': 'Processing...',\n        'auth.noAccount': 'Don\\'t have an account?',\n        'auth.haveAccount': 'Already have an account?',\n        'auth.signInSuccess': 'Signed in successfully!',\n        'auth.signUpSuccess': 'Account created successfully!',\n        'auth.genericError': 'An error occurred. Please try again.',\n        'auth.emailRequired': 'Email is required',\n        'auth.invalidEmail': 'Invalid email format',\n        'auth.passwordRequired': 'Password is required',\n        'auth.passwordTooShort': 'Password must be at least 6 characters',\n        'auth.firstNameRequired': 'First name is required',\n        'auth.lastNameRequired': 'Last name is required',\n        'auth.passwordRequirements': 'Password must be at least 6 characters',\n        'auth.invalidCredentials': 'Invalid email or password',\n        'auth.emailAlreadyInUse': 'This email is already registered',\n        'auth.accountCreated': 'Account created successfully',\n        'auth.loginSuccess': 'Logged in successfully',\n        // Cart\n        'cart.title': 'Your Cart',\n        'cart.empty': 'Your cart is empty',\n        'cart.emptyMessage': 'Add some items to your cart to get started',\n        'cart.continueShopping': 'Continue Shopping',\n        'cart.subtotal': 'Subtotal',\n        'cart.shipping': 'Shipping',\n        'cart.calculatedAtCheckout': 'Calculated at checkout',\n        'cart.tax': 'Tax',\n        'cart.total': 'Total',\n        'cart.proceedToCheckout': 'Proceed to Checkout',\n        'cart.remove': 'Remove',\n        'cart.quantity': 'Quantity',\n        'cart.clearCart': 'Clear Cart',\n        'cart.orderSummary': 'Order Summary',\n        // Wishlist\n        'wishlist.title': 'My Wishlist',\n        'wishlist.empty': 'Your wishlist is empty',\n        'wishlist.emptyMessage': 'Add items to your wishlist to save them for later',\n        'wishlist.continueShopping': 'Continue Shopping',\n        'wishlist.clearAll': 'Clear All',\n        'wishlist.addAllToCart': 'Add All to Cart',\n        'wishlist.remove': 'Remove',\n        'wishlist.addToCart': 'Add to Cart',\n        'wishlist.alreadyInCart': 'Already in cart',\n        // Account\n        'account.myAccount': 'My Account',\n        'account.profile': 'Profile',\n        'account.orders': 'Orders',\n        'account.addresses': 'Addresses',\n        'account.paymentMethods': 'Payment Methods',\n        'account.wishlist': 'Wishlist',\n        'account.loyalty': 'Loyalty Program',\n        'account.settings': 'Settings',\n        'account.signOut': 'Sign Out',\n        'account.notLoggedIn': 'Not logged in',\n        'account.loginRequired': 'You need to log in to access your account',\n        'account.profileInformation': 'Profile Information',\n        'account.firstName': 'First Name',\n        'account.lastName': 'Last Name',\n        'account.email': 'Email',\n        'account.phone': 'Phone',\n        'account.company': 'Company',\n        'account.emailCannotBeChanged': 'Email address cannot be changed',\n        'account.saveChanges': 'Save Changes',\n        'account.profileUpdated': 'Profile updated successfully',\n        'account.myOrders': 'My Orders',\n        'account.shippingAddresses': 'Shipping Addresses',\n        // Orders\n        'orders.searchOrders': 'Search orders',\n        'orders.allOrders': 'All Orders',\n        'orders.statusProcessing': 'Processing',\n        'orders.statusShipped': 'Shipped',\n        'orders.statusDelivered': 'Delivered',\n        'orders.statusCancelled': 'Cancelled',\n        'orders.noOrders': 'No orders found',\n        'orders.noOrdersMatchingFilters': 'No orders matching your filters',\n        'orders.noOrdersYet': 'You haven\\'t placed any orders yet',\n        'orders.items': 'items',\n        'orders.orderItems': 'Order Items',\n        'orders.quantity': 'Quantity',\n        'orders.shippingAddress': 'Shipping Address',\n        'orders.tracking': 'Tracking Information',\n        'orders.trackPackage': 'Track Package',\n        // Addresses\n        'addresses.addNew': 'Add New Address',\n        'addresses.editAddress': 'Edit Address',\n        'addresses.addNewAddress': 'Add New Address',\n        'addresses.fullName': 'Full Name',\n        'addresses.streetAddress': 'Street Address',\n        'addresses.city': 'City',\n        'addresses.stateProvince': 'State/Province',\n        'addresses.postalCode': 'Postal Code',\n        'addresses.country': 'Country',\n        'addresses.setAsDefault': 'Set as default address',\n        'addresses.addAddress': 'Add Address',\n        'addresses.noAddresses': 'No addresses found',\n        'addresses.addAddressPrompt': 'Add a shipping address to speed up checkout',\n        'addresses.addFirst': 'Add Your First Address',\n        'addresses.default': 'Default',\n        'addresses.setDefault': 'Set as Default',\n        // Payment Methods\n        'payment.addNew': 'Add New Payment Method',\n        'payment.editCard': 'Edit Card',\n        'payment.addNewCard': 'Add New Card',\n        'payment.cardNumber': 'Card Number',\n        'payment.cardholderName': 'Cardholder Name',\n        'payment.expiryDate': 'Expiry Date',\n        'payment.cvv': 'CVV',\n        'payment.setAsDefault': 'Set as default payment method',\n        'payment.addCard': 'Add Card',\n        'payment.noCards': 'No payment methods found',\n        'payment.addCardPrompt': 'Add a payment method to speed up checkout',\n        'payment.addFirst': 'Add Your First Payment Method',\n        'payment.default': 'Default',\n        'payment.setDefault': 'Set as Default',\n        'payment.expires': 'Expires',\n        'payment.securityNote': 'For demonstration purposes only. Do not enter real card information.',\n        'payment.creditCard': 'Credit Card',\n        'payment.paypal': 'PayPal',\n        'payment.bankTransfer': 'Bank Transfer',\n        // Settings\n        'settings.password': 'Password',\n        'settings.notifications': 'Notifications',\n        'settings.preferences': 'Preferences',\n        'settings.currentPassword': 'Current Password',\n        'settings.newPassword': 'New Password',\n        'settings.confirmPassword': 'Confirm Password',\n        'settings.changePassword': 'Change Password',\n        'settings.passwordChanged': 'Password changed successfully',\n        'settings.emailNotifications': 'Enable email notifications',\n        'settings.orderUpdates': 'Order status updates',\n        'settings.promotions': 'Promotions and special offers',\n        'settings.newsletter': 'Newsletter subscription',\n        'settings.notificationsUpdated': 'Notification preferences updated',\n        'settings.savePreferences': 'Save Preferences',\n        'settings.language': 'Language',\n        'settings.currency': 'Currency',\n        'settings.theme': 'Theme',\n        'settings.lightMode': 'Light Mode',\n        'settings.darkMode': 'Dark Mode',\n        // Common\n        'common.cancel': 'Cancel',\n        'common.save': 'Save',\n        'common.edit': 'Edit',\n        'common.delete': 'Delete',\n        // Wholesale Quote Form\n        'wholesale.wholesaleTitle': 'Wholesale Quote Request',\n        'wholesale.customProductTitle': 'Custom Product Quote Request',\n        'wholesale.companyName': 'Company Name',\n        'wholesale.contactName': 'Contact Name',\n        'wholesale.email': 'Email',\n        'wholesale.phone': 'Phone',\n        'wholesale.productType': 'Product Type',\n        'wholesale.productTypePlaceholder': 'e.g., Electronics, Machinery, Raw Materials',\n        'wholesale.specifications': 'Product Specifications',\n        'wholesale.specificationsPlaceholder': 'Please provide detailed specifications for your product requirements',\n        'wholesale.targetQuantity': 'Target Quantity',\n        'wholesale.targetQuantityPlaceholder': 'e.g., 1000 units',\n        'wholesale.targetPrice': 'Target Price (Optional)',\n        'wholesale.targetPricePlaceholder': 'Your desired price point',\n        'wholesale.timeline': 'Timeline',\n        'wholesale.timelinePlaceholder': 'When do you need the products?',\n        'wholesale.additionalNotes': 'Additional Notes',\n        'wholesale.additionalNotesPlaceholder': 'Any other information you\\'d like to share',\n        'wholesale.uploadFiles': 'Upload Files (Optional)',\n        'wholesale.dropFilesHere': 'Drop files here or click to upload product drawings, specifications, or reference images',\n        'wholesale.selectFiles': 'Select Files',\n        'wholesale.cancel': 'Cancel',\n        'wholesale.submitRequest': 'Submit Request',\n        'wholesale.submitting': 'Submitting...',\n        'wholesale.requestSubmitted': 'Request Submitted',\n        'wholesale.thankYou': 'Thank you for your request. Our team will contact you shortly.',\n        'wholesale.close': 'Close',\n        'wholesale.authRequired': 'You must be logged in to submit a quote request',\n        'wholesale.submitError': 'An error occurred while submitting your request. Please try again.',\n        // Common (Added placeholders)\n        'common.yes': 'Yes',\n        'common.no': 'No',\n        'common.confirm': 'Confirm',\n        'common.close': 'Close',\n        'common.loading': 'Loading',\n        'common.error': 'Error',\n        'common.success': 'Success',\n        'common.previous': 'Previous',\n        'common.next': 'Next',\n        // Footer\n        'footer.quickLinks': 'Quick Links',\n        'footer.support': 'Customer Support',\n        'footer.contact': 'Contact Information',\n        'footer.rights': 'All rights reserved'\n    },\n    ar: {\n        // App Info\n        'app.name': 'ارتال',\n        'app.tagline': 'حلول الأعمال المتكاملة',\n        // Navigation\n        'nav.home': 'الرئيسية',\n        'nav.shop': 'المتجر',\n        'nav.productionLines': 'خطوط الإنتاج',\n        'nav.services': 'الخدمات',\n        'nav.blog': 'المدونة',\n        'nav.contact': 'اتصل بنا',\n        // Header Actions\n        'header.search': 'بحث',\n        'header.searchPlaceholder': 'ابحث عن المنتجات...',\n        'header.specialOffers': 'عروض خاصة',\n        'header.wishlist': 'المفضلة',\n        'header.cart': 'السلة',\n        'header.account': 'الحساب',\n        'header.signIn': 'تسجيل الدخول',\n        'header.signUp': 'إنشاء حساب',\n        // Hero Section\n        'hero.cta.explore': 'استكشف المنتجات',\n        'hero.cta.services': 'خدماتنا',\n        // Business Solutions\n        'solutions.title': 'حلول أعمال شاملة',\n        'solutions.subtitle': 'استكشف مجموعتنا الكاملة من الخدمات المصممة لتلبية جميع احتياجات عملك.',\n        'solutions.features': 'الميزات الرئيسية',\n        // Products Section\n        'products.title': 'المنتجات المميزة',\n        'products.subtitle': 'اكتشف أحدث ابتكاراتنا وأفضل الحلول الصناعية مبيعاً.',\n        'products.viewAll': 'عرض جميع المنتجات',\n        'products.newArrivals': 'وصل حديثاً',\n        'products.inStock': 'متوفر',\n        'products.outOfStock': 'نفذ المخزون',\n        'products.addToCart': 'أضف إلى السلة',\n        'products.requestQuote': 'طلب عرض سعر',\n        // Blog Section\n        'blog.title': 'آخر رؤى الصناعة',\n        'blog.subtitle': 'ابق على اطلاع بأحدث المقالات والاتجاهات وأخبار الصناعة.',\n        'blog.readMore': 'اقرأ المزيد',\n        'blog.viewAll': 'عرض جميع المقالات',\n        // Common Actions\n        'actions.viewDetails': 'عرض التفاصيل',\n        'actions.learnMore': 'اعرف المزيد',\n        'actions.getStarted': 'ابدأ الآن',\n        'actions.contactUs': 'اتصل بنا',\n        'actions.bookNow': 'احجز الآن',\n        // Authentication\n        'auth.signIn': 'تسجيل الدخول',\n        'auth.signUp': 'إنشاء حساب',\n        'auth.email': 'البريد الإلكتروني',\n        'auth.password': 'كلمة المرور',\n        'auth.forgotPassword': 'نسيت كلمة المرور؟',\n        'auth.firstName': 'الاسم الأول',\n        'auth.lastName': 'الاسم الأخير',\n        'auth.processing': 'جاري المعالجة...',\n        'auth.noAccount': 'ليس لديك حساب؟',\n        'auth.haveAccount': 'لديك حساب بالفعل؟',\n        'auth.signInSuccess': 'تم تسجيل الدخول بنجاح!',\n        'auth.signUpSuccess': 'تم إنشاء الحساب بنجاح!',\n        'auth.genericError': 'حدث خطأ. يرجى المحاولة مرة أخرى.',\n        'auth.emailRequired': 'البريد الإلكتروني مطلوب',\n        'auth.invalidEmail': 'تنسيق البريد الإلكتروني غير صالح',\n        'auth.passwordRequired': 'كلمة المرور مطلوبة',\n        'auth.passwordTooShort': 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',\n        'auth.firstNameRequired': 'الاسم الأول مطلوب',\n        'auth.lastNameRequired': 'الاسم الأخير مطلوب',\n        'auth.passwordRequirements': 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',\n        'auth.invalidCredentials': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',\n        'auth.emailAlreadyInUse': 'هذا البريد الإلكتروني مسجل بالفعل',\n        'auth.accountCreated': 'تم إنشاء الحساب بنجاح',\n        'auth.loginSuccess': 'تم تسجيل الدخول بنجاح',\n        // Cart\n        'cart.title': 'السلة',\n        'cart.empty': 'السلة فارغة',\n        'cart.emptyMessage': 'أضف بعض المنتجات إلى السلة للبدء',\n        'cart.continueShopping': 'مواصلة التسوق',\n        'cart.subtotal': 'المجموع الفرعي',\n        'cart.shipping': 'الشحن',\n        'cart.calculatedAtCheckout': 'يتم حسابها عند الدفع',\n        'cart.tax': 'الضريبة',\n        'cart.total': 'المجموع',\n        'cart.proceedToCheckout': 'المتابعة إلى الدفع',\n        'cart.remove': 'إزالة',\n        'cart.quantity': 'الكمية',\n        'cart.clearCart': 'تفريغ السلة',\n        'cart.orderSummary': 'ملخص الطلب',\n        // Wishlist\n        'wishlist.title': 'المفضلة',\n        'wishlist.empty': 'قائمة المفضلة فارغة',\n        'wishlist.emptyMessage': 'أضف عناصر إلى المفضلة لحفظها لوقت لاحق',\n        'wishlist.continueShopping': 'مواصلة التسوق',\n        'wishlist.clearAll': 'مسح الكل',\n        'wishlist.addAllToCart': 'إضافة الكل إلى السلة',\n        'wishlist.remove': 'إزالة',\n        'wishlist.addToCart': 'أضف إلى السلة',\n        'wishlist.alreadyInCart': 'موجود بالفعل في السلة',\n        // Account\n        'account.myAccount': 'حسابي',\n        'account.profile': 'الملف الشخصي',\n        'account.orders': 'الطلبات',\n        'account.addresses': 'العناوين',\n        'account.paymentMethods': 'طرق الدفع',\n        'account.wishlist': 'المفضلة',\n        'account.loyalty': 'برنامج الولاء',\n        'account.settings': 'الإعدادات',\n        'account.signOut': 'تسجيل الخروج',\n        'account.notLoggedIn': 'لم يتم تسجيل الدخول',\n        'account.loginRequired': 'تحتاج إلى تسجيل الدخول للوصول إلى حسابك',\n        'account.profileInformation': 'معلومات الملف الشخصي',\n        'account.firstName': 'الاسم الأول',\n        'account.lastName': 'الاسم الأخير',\n        'account.email': 'البريد الإلكتروني',\n        'account.phone': 'الهاتف',\n        'account.company': 'الشركة',\n        'account.emailCannotBeChanged': 'لا يمكن تغيير عنوان البريد الإلكتروني',\n        'account.saveChanges': 'حفظ التغييرات',\n        'account.profileUpdated': 'تم تحديث الملف الشخصي بنجاح',\n        'account.myOrders': 'طلباتي',\n        'account.shippingAddresses': 'عناوين الشحن',\n        // Orders\n        'orders.searchOrders': 'البحث في الطلبات',\n        'orders.allOrders': 'جميع الطلبات',\n        'orders.statusProcessing': 'قيد المعالجة',\n        'orders.statusShipped': 'تم الشحن',\n        'orders.statusDelivered': 'تم التسليم',\n        'orders.statusCancelled': 'ملغي',\n        'orders.noOrders': 'لم يتم العثور على طلبات',\n        'orders.noOrdersMatchingFilters': 'لا توجد طلبات تطابق المرشحات',\n        'orders.noOrdersYet': 'لم تقم بإجراء أي طلبات بعد',\n        'orders.items': 'عناصر',\n        'orders.orderItems': 'عناصر الطلب',\n        'orders.quantity': 'الكمية',\n        'orders.shippingAddress': 'عنوان الشحن',\n        'orders.tracking': 'معلومات التتبع',\n        'orders.trackPackage': 'تتبع الشحنة',\n        // Addresses\n        'addresses.addNew': 'إضافة عنوان جديد',\n        'addresses.editAddress': 'تعديل العنوان',\n        'addresses.addNewAddress': 'إضافة عنوان جديد',\n        'addresses.fullName': 'الاسم الكامل',\n        'addresses.streetAddress': 'عنوان الشارع',\n        'addresses.city': 'المدينة',\n        'addresses.stateProvince': 'الولاية/المنطقة',\n        'addresses.postalCode': 'الرمز البريدي',\n        'addresses.country': 'البلد',\n        'addresses.setAsDefault': 'تعيين كعنوان افتراضي',\n        'addresses.addAddress': 'إضافة عنوان',\n        'addresses.noAddresses': 'لم يتم العثور على عناوين',\n        'addresses.addAddressPrompt': 'أضف عنوان شحن لتسريع عملية الدفع',\n        'addresses.addFirst': 'أضف عنوانك الأول',\n        'addresses.default': 'افتراضي',\n        'addresses.setDefault': 'تعيين كافتراضي',\n        // Payment Methods\n        'payment.addNew': 'إضافة طريقة دفع جديدة',\n        'payment.editCard': 'تعديل البطاقة',\n        'payment.addNewCard': 'إضافة بطاقة جديدة',\n        'payment.cardNumber': 'رقم البطاقة',\n        'payment.cardholderName': 'اسم حامل البطاقة',\n        'payment.expiryDate': 'تاريخ الانتهاء',\n        'payment.cvv': 'رمز الأمان',\n        'payment.setAsDefault': 'تعيين كطريقة دفع افتراضية',\n        'payment.addCard': 'إضافة بطاقة',\n        'payment.noCards': 'لم يتم العثور على طرق دفع',\n        'payment.addCardPrompt': 'أضف طريقة دفع لتسريع عملية الدفع',\n        'payment.addFirst': 'أضف طريقة الدفع الأولى',\n        'payment.default': 'افتراضي',\n        'payment.setDefault': 'تعيين كافتراضي',\n        'payment.expires': 'تنتهي الصلاحية',\n        'payment.securityNote': 'لأغراض العرض فقط. لا تدخل معلومات بطاقة حقيقية.',\n        'payment.creditCard': 'بطاقة ائتمان',\n        'payment.paypal': 'باي بال',\n        'payment.bankTransfer': 'تحويل بنكي',\n        // Settings\n        'settings.password': 'كلمة المرور',\n        'settings.notifications': 'الإشعارات',\n        'settings.preferences': 'التفضيلات',\n        'settings.currentPassword': 'كلمة المرور الحالية',\n        'settings.newPassword': 'كلمة المرور الجديدة',\n        'settings.confirmPassword': 'تأكيد كلمة المرور',\n        'settings.changePassword': 'تغيير كلمة المرور',\n        'settings.passwordChanged': 'تم تغيير كلمة المرور بنجاح',\n        'settings.emailNotifications': 'تفعيل إشعارات البريد الإلكتروني',\n        'settings.orderUpdates': 'تحديثات حالة الطلب',\n        'settings.promotions': 'العروض والعروض الخاصة',\n        'settings.newsletter': 'الاشتراك في النشرة الإخبارية',\n        'settings.notificationsUpdated': 'تم تحديث تفضيلات الإشعارات',\n        'settings.savePreferences': 'حفظ التفضيلات',\n        'settings.language': 'اللغة',\n        'settings.currency': 'العملة',\n        'settings.theme': 'المظهر',\n        'settings.lightMode': 'الوضع الفاتح',\n        'settings.darkMode': 'الوضع الداكن',\n        // Common\n        'common.cancel': 'إلغاء',\n        'common.save': 'حفظ',\n        'common.edit': 'تعديل',\n        'common.delete': 'حذف',\n        // Wholesale Quote Form\n        'wholesale.wholesaleTitle': 'طلب عرض سعر بالجملة',\n        'wholesale.customProductTitle': 'طلب عرض سعر لمنتج مخصص',\n        'wholesale.companyName': 'اسم الشركة',\n        'wholesale.contactName': 'اسم جهة الاتصال',\n        'wholesale.email': 'البريد الإلكتروني',\n        'wholesale.phone': 'رقم الهاتف',\n        'wholesale.productType': 'نوع المنتج',\n        'wholesale.productTypePlaceholder': 'مثال: إلكترونيات، آلات، مواد خام',\n        'wholesale.specifications': 'مواصفات المنتج',\n        'wholesale.specificationsPlaceholder': 'يرجى تقديم مواصفات مفصلة لمتطلبات المنتج الخاص بك',\n        'wholesale.targetQuantity': 'الكمية المستهدفة',\n        'wholesale.targetQuantityPlaceholder': 'مثال: 1000 وحدة',\n        'wholesale.targetPrice': 'السعر المستهدف (اختياري)',\n        'wholesale.targetPricePlaceholder': 'نقطة السعر المرغوبة',\n        'wholesale.timeline': 'الجدول الزمني',\n        'wholesale.timelinePlaceholder': 'متى تحتاج المنتجات؟',\n        'wholesale.additionalNotes': 'ملاحظات إضافية',\n        'wholesale.additionalNotesPlaceholder': 'أي معلومات أخرى ترغب في مشاركتها',\n        'wholesale.uploadFiles': 'تحميل الملفات (اختياري)',\n        'wholesale.dropFilesHere': 'قم بإسقاط الملفات هنا أو انقر لتحميل رسومات المنتج أو المواصفات أو الصور المرجعية',\n        'wholesale.selectFiles': 'اختر الملفات',\n        'wholesale.cancel': 'إلغاء',\n        'wholesale.submitRequest': 'إرسال الطلب',\n        'wholesale.submitting': 'جاري الإرسال...',\n        'wholesale.requestSubmitted': 'تم إرسال الطلب',\n        'wholesale.thankYou': 'شكرًا لطلبك. سيتواصل فريقنا معك قريبًا.',\n        'wholesale.close': 'إغلاق',\n        'wholesale.authRequired': 'يجب تسجيل الدخول لتقديم طلب عرض سعر',\n        'wholesale.submitError': 'حدث خطأ أثناء إرسال طلبك. يرجى المحاولة مرة أخرى.',\n        // Common (Added placeholders)\n        'common.yes': '[ARABIC TEXT FOR KEY: common.yes]',\n        'common.no': '[ARABIC TEXT FOR KEY: common.no]',\n        'common.confirm': '[ARABIC TEXT FOR KEY: common.confirm]',\n        'common.close': '[ARABIC TEXT FOR KEY: common.close]',\n        'common.loading': '[ARABIC TEXT FOR KEY: common.loading]',\n        'common.error': '[ARABIC TEXT FOR KEY: common.error]',\n        'common.success': '[ARABIC TEXT FOR KEY: common.success]',\n        'common.previous': '[ARABIC TEXT FOR KEY: common.previous]',\n        'common.next': '[ARABIC TEXT FOR KEY: common.next]',\n        // Footer\n        'footer.quickLinks': 'روابط سريعة',\n        'footer.support': 'دعم العملاء',\n        'footer.contact': 'معلومات الاتصال',\n        'footer.rights': 'جميع الحقوق محفوظة'\n    }\n};\nfunction useLocale() {\n    // Extract language from the path\n    const pathname =  true ? window.location.pathname : 0;\n    const localeMatch = pathname.match(/^\\/(ar|en)/);\n    return localeMatch ? localeMatch[1] : 'en'; // English is the default language\n}\nfunction useTranslation() {\n    const { language } = (0,_stores_languageStore__WEBPACK_IMPORTED_MODULE_0__.useLanguageStore)();\n    const locale = useLocale();\n    // Use language from the path or from the store\n    const currentLanguage = locale || language;\n    const t = (key)=>{\n        return translations[currentLanguage][key] || key;\n    };\n    return {\n        t,\n        language: currentLanguage,\n        locale\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/translations/index.ts\n"));

/***/ })

});