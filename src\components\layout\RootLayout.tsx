'use client';

import { ReactNode } from 'react';
import { useLanguageStore } from '../../stores/languageStore';
import { CsrfToken } from '../security/CsrfToken';
import { Prefetcher } from '../performance/Prefetcher';
import { MobileOptimizer } from '../ui/MobileOptimizer';

interface RootLayoutProps {
  children: ReactNode;
}

export function RootLayout({ children }: RootLayoutProps) {
  const { language } = useLanguageStore();

  return (
    <>
      <CsrfToken />
      <Prefetcher />
      <MobileOptimizer />
      {children}
    </>
  );
}