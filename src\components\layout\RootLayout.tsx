'use client';

import { ReactNode } from 'react';
import { useLanguageStore } from '../../stores/languageStore';
import { CsrfToken } from '../security/CsrfToken';
import { Prefetcher } from '../performance/Prefetcher';
import { MobileOptimizer } from '../ui/MobileOptimizer';

interface RootLayoutProps {
  children: ReactNode;
}

export function RootLayout({ children }: RootLayoutProps) {
  // Temporarily disable all complex components
  // const { language } = useLanguageStore();

  return (
    <>
      {/* Temporarily disable all components to isolate webpack issue */}
      {children}
    </>
  );
}