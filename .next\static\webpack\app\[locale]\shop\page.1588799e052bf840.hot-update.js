"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/shop/page",{

/***/ "(app-pages-browser)/./src/data/products.ts":
/*!******************************!*\
  !*** ./src/data/products.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   productCategories: () => (/* binding */ productCategories),\n/* harmony export */   products: () => (/* binding */ products)\n/* harmony export */ });\nconst products = [\n    {\n        id: 'smart-factory-sensor',\n        name: 'Smart Factory IoT Sensor Kit',\n        slug: 'smart-factory-sensor',\n        description: 'Next-generation IoT sensors for real-time manufacturing monitoring and predictive maintenance. Features advanced analytics and cloud connectivity.',\n        price: 2999.99,\n        compareAtPrice: 3499.99,\n        images: [\n            '/images/product-automation.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'IoT',\n            'Industry 4.0',\n            'Best Seller'\n        ],\n        stock: 50,\n        inStock: true,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 150,\n        specifications: {\n            'Sensor Type': 'Multi-parameter',\n            'Connectivity': 'WiFi, Bluetooth, LoRaWAN',\n            'Battery Life': 'Up to 2 years',\n            'Data Rate': '1 sample/second',\n            'Operating Temperature': '-20°C to 85°C',\n            'Warranty': '3 years',\n            'Certification': 'CE, FCC, RoHS'\n        },\n        createdAt: new Date().toISOString(),\n        relatedProducts: [\n            'industrial-control-panel',\n            'automation-software'\n        ]\n    },\n    {\n        id: 'ai-quality-system',\n        name: 'AI-Powered Quality Inspection System',\n        slug: 'ai-quality-system',\n        description: 'Advanced computer vision system for automated quality control in production lines.',\n        price: 8999.99,\n        compareAtPrice: 10999.99,\n        images: [\n            '/images/product-packaging.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Quality Control',\n        tags: [\n            'New Arrival',\n            'AI',\n            'Automation'\n        ],\n        stock: 15,\n        featured: true,\n        rating: 4.9,\n        reviewCount: 210,\n        specifications: {\n            'Camera Resolution': '4K Ultra HD',\n            'Processing Speed': 'Up to 100 items/minute',\n            'AI Model': 'Deep Learning CNN',\n            'Integration': 'REST API, MQTT',\n            'Accuracy Rate': '99.9%'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'smart-inventory',\n        name: 'Smart Inventory Management System',\n        slug: 'smart-inventory',\n        description: 'Cloud-based inventory tracking system with real-time analytics and AI-driven forecasting.',\n        price: 4999.99,\n        compareAtPrice: 5999.99,\n        images: [\n            '/images/product-manufacturing.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Warehouse Management',\n        tags: [\n            'New Arrival',\n            'Smart Systems',\n            'Cloud'\n        ],\n        stock: 30,\n        featured: true,\n        rating: 4.6,\n        reviewCount: 95,\n        specifications: {\n            'Storage Capacity': 'Unlimited',\n            'Real-time Tracking': 'Yes',\n            'Mobile App': 'iOS & Android',\n            'Barcode Support': '1D & 2D',\n            'Analytics': 'Advanced ML-based'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'collaborative-robot',\n        name: 'Next-Gen Collaborative Robot',\n        slug: 'collaborative-robot',\n        description: 'Advanced collaborative robot with enhanced safety features and intuitive programming.',\n        price: 29999.99,\n        compareAtPrice: 34999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Robotics',\n        tags: [\n            'New Arrival',\n            'Robotics',\n            'Safety'\n        ],\n        stock: 10,\n        featured: true,\n        rating: 4.8,\n        reviewCount: 75,\n        specifications: {\n            'Payload': 'Up to 10kg',\n            'Reach': '1.3m',\n            'Degrees of Freedom': '6-axis',\n            'Safety Features': 'Force limiting, Vision',\n            'Programming': 'Teach pendant, GUI'\n        },\n        createdAt: new Date().toISOString()\n    },\n    {\n        id: 'digital-twin-platform',\n        name: 'Digital Twin Manufacturing Platform',\n        slug: 'digital-twin-platform',\n        description: 'Complete digital twin solution for real-time production monitoring and optimization.',\n        price: 12999.99,\n        compareAtPrice: 15999.99,\n        images: [\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg',\n            '/images/product-placeholder-light.svg'\n        ],\n        category: 'Smart Manufacturing',\n        tags: [\n            'New Arrival',\n            'Industry 4.0',\n            'Digital Twin'\n        ],\n        stock: 20,\n        featured: true,\n        rating: 4.7,\n        reviewCount: 110,\n        specifications: {\n            'Simulation': 'Real-time 3D',\n            'Data Integration': 'OPC UA, MQTT',\n            'Analytics': 'Predictive maintenance',\n            'Visualization': 'AR/VR support',\n            'API': 'RESTful, GraphQL'\n        },\n        createdAt: new Date().toISOString()\n    }\n];\nconst productCategories = [\n    {\n        id: 'smart-manufacturing',\n        name: {\n            en: 'Smart Manufacturing',\n            ar: 'التصنيع الذكي'\n        },\n        description: {\n            en: 'Industry 4.0 solutions for modern factories',\n            ar: 'حلول الصناعة 4.0 للمصانع الحديثة'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'quality-control',\n        name: {\n            en: 'Quality Control',\n            ar: 'مراقبة الجودة'\n        },\n        description: {\n            en: 'Advanced quality assurance systems',\n            ar: 'أنظمة ضمان الجودة المتقدمة'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'warehouse-management',\n        name: {\n            en: 'Warehouse Management',\n            ar: 'إدارة المستودعات'\n        },\n        description: {\n            en: 'Smart warehouse and inventory solutions',\n            ar: 'حلول المستودعات والمخزون الذكية'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'robotics',\n        name: {\n            en: 'Robotics',\n            ar: 'الروبوتات'\n        },\n        description: {\n            en: 'Next-generation industrial robots',\n            ar: 'الجيل القادم من الروبوتات الصناعية'\n        },\n        image: '/images/placeholder-light.svg'\n    },\n    {\n        id: 'digital-solutions',\n        name: {\n            en: 'Digital Solutions',\n            ar: 'الحلول الرقمية'\n        },\n        description: {\n            en: 'Digital transformation tools',\n            ar: 'أدوات التحول الرقمي'\n        },\n        image: '/images/placeholder-light.svg'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9kYXRhL3Byb2R1Y3RzLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBRU8sTUFBTUEsV0FBc0I7SUFDakM7UUFDRUMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLGdCQUFnQjtRQUNoQkMsUUFBUTtZQUNOO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFVBQVU7UUFDVkMsTUFBTTtZQUFDO1lBQWU7WUFBTztZQUFnQjtTQUFjO1FBQzNEQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLGFBQWE7UUFDYkMsZ0JBQWdCO1lBQ2QsZUFBZTtZQUNmLGdCQUFnQjtZQUNoQixnQkFBZ0I7WUFDaEIsYUFBYTtZQUNiLHlCQUF5QjtZQUN6QixZQUFZO1lBQ1osaUJBQWlCO1FBQ25CO1FBQ0FDLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztRQUNqQ0MsaUJBQWlCO1lBQUM7WUFBNEI7U0FBc0I7SUFDdEU7SUFDQTtRQUNFbEIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLGdCQUFnQjtRQUNoQkMsUUFBUTtZQUNOO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFVBQVU7UUFDVkMsTUFBTTtZQUFDO1lBQWU7WUFBTTtTQUFhO1FBQ3pDQyxPQUFPO1FBQ1BFLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxhQUFhO1FBQ2JDLGdCQUFnQjtZQUNkLHFCQUFxQjtZQUNyQixvQkFBb0I7WUFDcEIsWUFBWTtZQUNaLGVBQWU7WUFDZixpQkFBaUI7UUFDbkI7UUFDQUMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO0lBQ25DO0lBQ0E7UUFDRWpCLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxnQkFBZ0I7UUFDaEJDLFFBQVE7WUFDTjtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxVQUFVO1FBQ1ZDLE1BQU07WUFBQztZQUFlO1lBQWlCO1NBQVE7UUFDL0NDLE9BQU87UUFDUEUsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLGFBQWE7UUFDYkMsZ0JBQWdCO1lBQ2Qsb0JBQW9CO1lBQ3BCLHNCQUFzQjtZQUN0QixjQUFjO1lBQ2QsbUJBQW1CO1lBQ25CLGFBQWE7UUFDZjtRQUNBQyxXQUFXLElBQUlDLE9BQU9DLFdBQVc7SUFDbkM7SUFDQTtRQUNFakIsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLGdCQUFnQjtRQUNoQkMsUUFBUTtZQUNOO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFVBQVU7UUFDVkMsTUFBTTtZQUFDO1lBQWU7WUFBWTtTQUFTO1FBQzNDQyxPQUFPO1FBQ1BFLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxhQUFhO1FBQ2JDLGdCQUFnQjtZQUNkLFdBQVc7WUFDWCxTQUFTO1lBQ1Qsc0JBQXNCO1lBQ3RCLG1CQUFtQjtZQUNuQixlQUFlO1FBQ2pCO1FBQ0FDLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztJQUNuQztJQUNBO1FBQ0VqQixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsZ0JBQWdCO1FBQ2hCQyxRQUFRO1lBQ047WUFDQTtZQUNBO1NBQ0Q7UUFDREMsVUFBVTtRQUNWQyxNQUFNO1lBQUM7WUFBZTtZQUFnQjtTQUFlO1FBQ3JEQyxPQUFPO1FBQ1BFLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxhQUFhO1FBQ2JDLGdCQUFnQjtZQUNkLGNBQWM7WUFDZCxvQkFBb0I7WUFDcEIsYUFBYTtZQUNiLGlCQUFpQjtZQUNqQixPQUFPO1FBQ1Q7UUFDQUMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO0lBQ25DO0NBQ0QsQ0FBQztBQUVLLE1BQU1FLG9CQUFvQjtJQUMvQjtRQUNFbkIsSUFBSTtRQUNKQyxNQUFNO1lBQUVtQixJQUFJO1lBQXVCQyxJQUFJO1FBQWdCO1FBQ3ZEbEIsYUFBYTtZQUFFaUIsSUFBSTtZQUErQ0MsSUFBSTtRQUFtQztRQUN6R0MsT0FBTztJQUNUO0lBQ0E7UUFDRXRCLElBQUk7UUFDSkMsTUFBTTtZQUFFbUIsSUFBSTtZQUFtQkMsSUFBSTtRQUFnQjtRQUNuRGxCLGFBQWE7WUFBRWlCLElBQUk7WUFBc0NDLElBQUk7UUFBNkI7UUFDMUZDLE9BQU87SUFDVDtJQUNBO1FBQ0V0QixJQUFJO1FBQ0pDLE1BQU07WUFBRW1CLElBQUk7WUFBd0JDLElBQUk7UUFBbUI7UUFDM0RsQixhQUFhO1lBQUVpQixJQUFJO1lBQTJDQyxJQUFJO1FBQWtDO1FBQ3BHQyxPQUFPO0lBQ1Q7SUFDQTtRQUNFdEIsSUFBSTtRQUNKQyxNQUFNO1lBQUVtQixJQUFJO1lBQVlDLElBQUk7UUFBWTtRQUN4Q2xCLGFBQWE7WUFBRWlCLElBQUk7WUFBcUNDLElBQUk7UUFBcUM7UUFDakdDLE9BQU87SUFDVDtJQUNBO1FBQ0V0QixJQUFJO1FBQ0pDLE1BQU07WUFBRW1CLElBQUk7WUFBcUJDLElBQUk7UUFBaUI7UUFDdERsQixhQUFhO1lBQUVpQixJQUFJO1lBQWdDQyxJQUFJO1FBQXNCO1FBQzdFQyxPQUFPO0lBQ1Q7Q0FDRCxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFrcmFtWWFoeWFcXERlc2t0b3BcXGVjb21tZXJjZXByb1xcc3JjXFxkYXRhXFxwcm9kdWN0cy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcm9kdWN0IH0gZnJvbSAnLi4vdHlwZXMvaW5kZXgnO1xuXG5leHBvcnQgY29uc3QgcHJvZHVjdHM6IFByb2R1Y3RbXSA9IFtcbiAge1xuICAgIGlkOiAnc21hcnQtZmFjdG9yeS1zZW5zb3InLFxuICAgIG5hbWU6ICdTbWFydCBGYWN0b3J5IElvVCBTZW5zb3IgS2l0JyxcbiAgICBzbHVnOiAnc21hcnQtZmFjdG9yeS1zZW5zb3InLFxuICAgIGRlc2NyaXB0aW9uOiAnTmV4dC1nZW5lcmF0aW9uIElvVCBzZW5zb3JzIGZvciByZWFsLXRpbWUgbWFudWZhY3R1cmluZyBtb25pdG9yaW5nIGFuZCBwcmVkaWN0aXZlIG1haW50ZW5hbmNlLiBGZWF0dXJlcyBhZHZhbmNlZCBhbmFseXRpY3MgYW5kIGNsb3VkIGNvbm5lY3Rpdml0eS4nLFxuICAgIHByaWNlOiAyOTk5Ljk5LFxuICAgIGNvbXBhcmVBdFByaWNlOiAzNDk5Ljk5LFxuICAgIGltYWdlczogW1xuICAgICAgJy9pbWFnZXMvcHJvZHVjdC1hdXRvbWF0aW9uLnN2ZycsXG4gICAgICAnL2ltYWdlcy9wcm9kdWN0LXBsYWNlaG9sZGVyLWxpZ2h0LnN2ZycsXG4gICAgICAnL2ltYWdlcy9wcm9kdWN0LXBsYWNlaG9sZGVyLWxpZ2h0LnN2ZydcbiAgICBdLFxuICAgIGNhdGVnb3J5OiAnU21hcnQgTWFudWZhY3R1cmluZycsXG4gICAgdGFnczogWydOZXcgQXJyaXZhbCcsICdJb1QnLCAnSW5kdXN0cnkgNC4wJywgJ0Jlc3QgU2VsbGVyJ10sXG4gICAgc3RvY2s6IDUwLFxuICAgIGluU3RvY2s6IHRydWUsXG4gICAgZmVhdHVyZWQ6IHRydWUsXG4gICAgcmF0aW5nOiA0LjcsXG4gICAgcmV2aWV3Q291bnQ6IDE1MCxcbiAgICBzcGVjaWZpY2F0aW9uczoge1xuICAgICAgJ1NlbnNvciBUeXBlJzogJ011bHRpLXBhcmFtZXRlcicsXG4gICAgICAnQ29ubmVjdGl2aXR5JzogJ1dpRmksIEJsdWV0b290aCwgTG9SYVdBTicsXG4gICAgICAnQmF0dGVyeSBMaWZlJzogJ1VwIHRvIDIgeWVhcnMnLFxuICAgICAgJ0RhdGEgUmF0ZSc6ICcxIHNhbXBsZS9zZWNvbmQnLFxuICAgICAgJ09wZXJhdGluZyBUZW1wZXJhdHVyZSc6ICctMjDCsEMgdG8gODXCsEMnLFxuICAgICAgJ1dhcnJhbnR5JzogJzMgeWVhcnMnLFxuICAgICAgJ0NlcnRpZmljYXRpb24nOiAnQ0UsIEZDQywgUm9IUydcbiAgICB9LFxuICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgIHJlbGF0ZWRQcm9kdWN0czogWydpbmR1c3RyaWFsLWNvbnRyb2wtcGFuZWwnLCAnYXV0b21hdGlvbi1zb2Z0d2FyZSddXG4gIH0sXG4gIHtcbiAgICBpZDogJ2FpLXF1YWxpdHktc3lzdGVtJyxcbiAgICBuYW1lOiAnQUktUG93ZXJlZCBRdWFsaXR5IEluc3BlY3Rpb24gU3lzdGVtJyxcbiAgICBzbHVnOiAnYWktcXVhbGl0eS1zeXN0ZW0nLFxuICAgIGRlc2NyaXB0aW9uOiAnQWR2YW5jZWQgY29tcHV0ZXIgdmlzaW9uIHN5c3RlbSBmb3IgYXV0b21hdGVkIHF1YWxpdHkgY29udHJvbCBpbiBwcm9kdWN0aW9uIGxpbmVzLicsXG4gICAgcHJpY2U6IDg5OTkuOTksXG4gICAgY29tcGFyZUF0UHJpY2U6IDEwOTk5Ljk5LFxuICAgIGltYWdlczogW1xuICAgICAgJy9pbWFnZXMvcHJvZHVjdC1wYWNrYWdpbmcuc3ZnJyxcbiAgICAgICcvaW1hZ2VzL3Byb2R1Y3QtcGxhY2Vob2xkZXItbGlnaHQuc3ZnJyxcbiAgICAgICcvaW1hZ2VzL3Byb2R1Y3QtcGxhY2Vob2xkZXItbGlnaHQuc3ZnJ1xuICAgIF0sXG4gICAgY2F0ZWdvcnk6ICdRdWFsaXR5IENvbnRyb2wnLFxuICAgIHRhZ3M6IFsnTmV3IEFycml2YWwnLCAnQUknLCAnQXV0b21hdGlvbiddLFxuICAgIHN0b2NrOiAxNSxcbiAgICBmZWF0dXJlZDogdHJ1ZSxcbiAgICByYXRpbmc6IDQuOSxcbiAgICByZXZpZXdDb3VudDogMjEwLFxuICAgIHNwZWNpZmljYXRpb25zOiB7XG4gICAgICAnQ2FtZXJhIFJlc29sdXRpb24nOiAnNEsgVWx0cmEgSEQnLFxuICAgICAgJ1Byb2Nlc3NpbmcgU3BlZWQnOiAnVXAgdG8gMTAwIGl0ZW1zL21pbnV0ZScsXG4gICAgICAnQUkgTW9kZWwnOiAnRGVlcCBMZWFybmluZyBDTk4nLFxuICAgICAgJ0ludGVncmF0aW9uJzogJ1JFU1QgQVBJLCBNUVRUJyxcbiAgICAgICdBY2N1cmFjeSBSYXRlJzogJzk5LjklJ1xuICAgIH0sXG4gICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgfSxcbiAge1xuICAgIGlkOiAnc21hcnQtaW52ZW50b3J5JyxcbiAgICBuYW1lOiAnU21hcnQgSW52ZW50b3J5IE1hbmFnZW1lbnQgU3lzdGVtJyxcbiAgICBzbHVnOiAnc21hcnQtaW52ZW50b3J5JyxcbiAgICBkZXNjcmlwdGlvbjogJ0Nsb3VkLWJhc2VkIGludmVudG9yeSB0cmFja2luZyBzeXN0ZW0gd2l0aCByZWFsLXRpbWUgYW5hbHl0aWNzIGFuZCBBSS1kcml2ZW4gZm9yZWNhc3RpbmcuJyxcbiAgICBwcmljZTogNDk5OS45OSxcbiAgICBjb21wYXJlQXRQcmljZTogNTk5OS45OSxcbiAgICBpbWFnZXM6IFtcbiAgICAgICcvaW1hZ2VzL3Byb2R1Y3QtbWFudWZhY3R1cmluZy5zdmcnLFxuICAgICAgJy9pbWFnZXMvcHJvZHVjdC1wbGFjZWhvbGRlci1saWdodC5zdmcnLFxuICAgICAgJy9pbWFnZXMvcHJvZHVjdC1wbGFjZWhvbGRlci1saWdodC5zdmcnXG4gICAgXSxcbiAgICBjYXRlZ29yeTogJ1dhcmVob3VzZSBNYW5hZ2VtZW50JyxcbiAgICB0YWdzOiBbJ05ldyBBcnJpdmFsJywgJ1NtYXJ0IFN5c3RlbXMnLCAnQ2xvdWQnXSxcbiAgICBzdG9jazogMzAsXG4gICAgZmVhdHVyZWQ6IHRydWUsXG4gICAgcmF0aW5nOiA0LjYsXG4gICAgcmV2aWV3Q291bnQ6IDk1LFxuICAgIHNwZWNpZmljYXRpb25zOiB7XG4gICAgICAnU3RvcmFnZSBDYXBhY2l0eSc6ICdVbmxpbWl0ZWQnLFxuICAgICAgJ1JlYWwtdGltZSBUcmFja2luZyc6ICdZZXMnLFxuICAgICAgJ01vYmlsZSBBcHAnOiAnaU9TICYgQW5kcm9pZCcsXG4gICAgICAnQmFyY29kZSBTdXBwb3J0JzogJzFEICYgMkQnLFxuICAgICAgJ0FuYWx5dGljcyc6ICdBZHZhbmNlZCBNTC1iYXNlZCdcbiAgICB9LFxuICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gIH0sXG4gIHtcbiAgICBpZDogJ2NvbGxhYm9yYXRpdmUtcm9ib3QnLFxuICAgIG5hbWU6ICdOZXh0LUdlbiBDb2xsYWJvcmF0aXZlIFJvYm90JyxcbiAgICBzbHVnOiAnY29sbGFib3JhdGl2ZS1yb2JvdCcsXG4gICAgZGVzY3JpcHRpb246ICdBZHZhbmNlZCBjb2xsYWJvcmF0aXZlIHJvYm90IHdpdGggZW5oYW5jZWQgc2FmZXR5IGZlYXR1cmVzIGFuZCBpbnR1aXRpdmUgcHJvZ3JhbW1pbmcuJyxcbiAgICBwcmljZTogMjk5OTkuOTksXG4gICAgY29tcGFyZUF0UHJpY2U6IDM0OTk5Ljk5LFxuICAgIGltYWdlczogW1xuICAgICAgJy9pbWFnZXMvcHJvZHVjdC1wbGFjZWhvbGRlci1saWdodC5zdmcnLFxuICAgICAgJy9pbWFnZXMvcHJvZHVjdC1wbGFjZWhvbGRlci1saWdodC5zdmcnLFxuICAgICAgJy9pbWFnZXMvcHJvZHVjdC1wbGFjZWhvbGRlci1saWdodC5zdmcnXG4gICAgXSxcbiAgICBjYXRlZ29yeTogJ1JvYm90aWNzJyxcbiAgICB0YWdzOiBbJ05ldyBBcnJpdmFsJywgJ1JvYm90aWNzJywgJ1NhZmV0eSddLFxuICAgIHN0b2NrOiAxMCxcbiAgICBmZWF0dXJlZDogdHJ1ZSxcbiAgICByYXRpbmc6IDQuOCxcbiAgICByZXZpZXdDb3VudDogNzUsXG4gICAgc3BlY2lmaWNhdGlvbnM6IHtcbiAgICAgICdQYXlsb2FkJzogJ1VwIHRvIDEwa2cnLFxuICAgICAgJ1JlYWNoJzogJzEuM20nLFxuICAgICAgJ0RlZ3JlZXMgb2YgRnJlZWRvbSc6ICc2LWF4aXMnLFxuICAgICAgJ1NhZmV0eSBGZWF0dXJlcyc6ICdGb3JjZSBsaW1pdGluZywgVmlzaW9uJyxcbiAgICAgICdQcm9ncmFtbWluZyc6ICdUZWFjaCBwZW5kYW50LCBHVUknXG4gICAgfSxcbiAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICB9LFxuICB7XG4gICAgaWQ6ICdkaWdpdGFsLXR3aW4tcGxhdGZvcm0nLFxuICAgIG5hbWU6ICdEaWdpdGFsIFR3aW4gTWFudWZhY3R1cmluZyBQbGF0Zm9ybScsXG4gICAgc2x1ZzogJ2RpZ2l0YWwtdHdpbi1wbGF0Zm9ybScsXG4gICAgZGVzY3JpcHRpb246ICdDb21wbGV0ZSBkaWdpdGFsIHR3aW4gc29sdXRpb24gZm9yIHJlYWwtdGltZSBwcm9kdWN0aW9uIG1vbml0b3JpbmcgYW5kIG9wdGltaXphdGlvbi4nLFxuICAgIHByaWNlOiAxMjk5OS45OSxcbiAgICBjb21wYXJlQXRQcmljZTogMTU5OTkuOTksXG4gICAgaW1hZ2VzOiBbXG4gICAgICAnL2ltYWdlcy9wcm9kdWN0LXBsYWNlaG9sZGVyLWxpZ2h0LnN2ZycsXG4gICAgICAnL2ltYWdlcy9wcm9kdWN0LXBsYWNlaG9sZGVyLWxpZ2h0LnN2ZycsXG4gICAgICAnL2ltYWdlcy9wcm9kdWN0LXBsYWNlaG9sZGVyLWxpZ2h0LnN2ZydcbiAgICBdLFxuICAgIGNhdGVnb3J5OiAnU21hcnQgTWFudWZhY3R1cmluZycsXG4gICAgdGFnczogWydOZXcgQXJyaXZhbCcsICdJbmR1c3RyeSA0LjAnLCAnRGlnaXRhbCBUd2luJ10sXG4gICAgc3RvY2s6IDIwLFxuICAgIGZlYXR1cmVkOiB0cnVlLFxuICAgIHJhdGluZzogNC43LFxuICAgIHJldmlld0NvdW50OiAxMTAsXG4gICAgc3BlY2lmaWNhdGlvbnM6IHtcbiAgICAgICdTaW11bGF0aW9uJzogJ1JlYWwtdGltZSAzRCcsXG4gICAgICAnRGF0YSBJbnRlZ3JhdGlvbic6ICdPUEMgVUEsIE1RVFQnLFxuICAgICAgJ0FuYWx5dGljcyc6ICdQcmVkaWN0aXZlIG1haW50ZW5hbmNlJyxcbiAgICAgICdWaXN1YWxpemF0aW9uJzogJ0FSL1ZSIHN1cHBvcnQnLFxuICAgICAgJ0FQSSc6ICdSRVNUZnVsLCBHcmFwaFFMJ1xuICAgIH0sXG4gICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgfVxuXTtcblxuZXhwb3J0IGNvbnN0IHByb2R1Y3RDYXRlZ29yaWVzID0gW1xuICB7XG4gICAgaWQ6ICdzbWFydC1tYW51ZmFjdHVyaW5nJyxcbiAgICBuYW1lOiB7IGVuOiAnU21hcnQgTWFudWZhY3R1cmluZycsIGFyOiAn2KfZhNiq2LXZhtmK2Lkg2KfZhNiw2YPZiicgfSxcbiAgICBkZXNjcmlwdGlvbjogeyBlbjogJ0luZHVzdHJ5IDQuMCBzb2x1dGlvbnMgZm9yIG1vZGVybiBmYWN0b3JpZXMnLCBhcjogJ9it2YTZiNmEINin2YTYtdmG2KfYudipIDQuMCDZhNmE2YXYtdin2YbYuSDYp9mE2K3Yr9mK2KvYqScgfSxcbiAgICBpbWFnZTogJy9pbWFnZXMvcGxhY2Vob2xkZXItbGlnaHQuc3ZnJ1xuICB9LFxuICB7XG4gICAgaWQ6ICdxdWFsaXR5LWNvbnRyb2wnLFxuICAgIG5hbWU6IHsgZW46ICdRdWFsaXR5IENvbnRyb2wnLCBhcjogJ9mF2LHYp9mC2KjYqSDYp9mE2KzZiNiv2KknIH0sXG4gICAgZGVzY3JpcHRpb246IHsgZW46ICdBZHZhbmNlZCBxdWFsaXR5IGFzc3VyYW5jZSBzeXN0ZW1zJywgYXI6ICfYo9mG2LjZhdipINi22YXYp9mGINin2YTYrNmI2K/YqSDYp9mE2YXYqtmC2K/ZhdipJyB9LFxuICAgIGltYWdlOiAnL2ltYWdlcy9wbGFjZWhvbGRlci1saWdodC5zdmcnXG4gIH0sXG4gIHtcbiAgICBpZDogJ3dhcmVob3VzZS1tYW5hZ2VtZW50JyxcbiAgICBuYW1lOiB7IGVuOiAnV2FyZWhvdXNlIE1hbmFnZW1lbnQnLCBhcjogJ9il2K/Yp9ix2Kkg2KfZhNmF2LPYqtmI2K/Yudin2KonIH0sXG4gICAgZGVzY3JpcHRpb246IHsgZW46ICdTbWFydCB3YXJlaG91c2UgYW5kIGludmVudG9yeSBzb2x1dGlvbnMnLCBhcjogJ9it2YTZiNmEINin2YTZhdiz2KrZiNiv2LnYp9iqINmI2KfZhNmF2K7YstmI2YYg2KfZhNiw2YPZitipJyB9LFxuICAgIGltYWdlOiAnL2ltYWdlcy9wbGFjZWhvbGRlci1saWdodC5zdmcnXG4gIH0sXG4gIHtcbiAgICBpZDogJ3JvYm90aWNzJyxcbiAgICBuYW1lOiB7IGVuOiAnUm9ib3RpY3MnLCBhcjogJ9in2YTYsdmI2KjZiNiq2KfYqicgfSxcbiAgICBkZXNjcmlwdGlvbjogeyBlbjogJ05leHQtZ2VuZXJhdGlvbiBpbmR1c3RyaWFsIHJvYm90cycsIGFyOiAn2KfZhNis2YrZhCDYp9mE2YLYp9iv2YUg2YXZhiDYp9mE2LHZiNio2YjYqtin2Kog2KfZhNi12YbYp9i52YrYqScgfSxcbiAgICBpbWFnZTogJy9pbWFnZXMvcGxhY2Vob2xkZXItbGlnaHQuc3ZnJ1xuICB9LFxuICB7XG4gICAgaWQ6ICdkaWdpdGFsLXNvbHV0aW9ucycsXG4gICAgbmFtZTogeyBlbjogJ0RpZ2l0YWwgU29sdXRpb25zJywgYXI6ICfYp9mE2K3ZhNmI2YQg2KfZhNix2YLZhdmK2KknIH0sXG4gICAgZGVzY3JpcHRpb246IHsgZW46ICdEaWdpdGFsIHRyYW5zZm9ybWF0aW9uIHRvb2xzJywgYXI6ICfYo9iv2YjYp9iqINin2YTYqtit2YjZhCDYp9mE2LHZgtmF2YonIH0sXG4gICAgaW1hZ2U6ICcvaW1hZ2VzL3BsYWNlaG9sZGVyLWxpZ2h0LnN2ZydcbiAgfVxuXTsiXSwibmFtZXMiOlsicHJvZHVjdHMiLCJpZCIsIm5hbWUiLCJzbHVnIiwiZGVzY3JpcHRpb24iLCJwcmljZSIsImNvbXBhcmVBdFByaWNlIiwiaW1hZ2VzIiwiY2F0ZWdvcnkiLCJ0YWdzIiwic3RvY2siLCJpblN0b2NrIiwiZmVhdHVyZWQiLCJyYXRpbmciLCJyZXZpZXdDb3VudCIsInNwZWNpZmljYXRpb25zIiwiY3JlYXRlZEF0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwicmVsYXRlZFByb2R1Y3RzIiwicHJvZHVjdENhdGVnb3JpZXMiLCJlbiIsImFyIiwiaW1hZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/products.ts\n"));

/***/ })

});